package handlers

import (
	"context"
	"encoding/json"
	"log"
	"net/http"
	"time"

	"enhanced-pastebin/internal/models"
	"enhanced-pastebin/internal/services"
	"enhanced-pastebin/internal/utils"
	websocketPkg "enhanced-pastebin/internal/websocket"

	"github.com/gin-gonic/gin"
)

// WebSocketHandler handles WebSocket connections
type WebSocketHandler struct {
	hub                  *websocketPkg.Hub
	collaborationService *services.CollaborationService
}

// NewWebSocketHandler creates a new WebSocket handler
func NewWebSocketHandler(hub *websocketPkg.Hub, collaborationService *services.CollaborationService) *WebSocketHandler {
	return &WebSocketHandler{
		hub:                  hub,
		collaborationService: collaborationService,
	}
}

// HandleWebSocket upgrades HTTP connections to WebSocket
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// Extract user information from JWT token if present
	userID := "anonymous"
	username := "Anonymous"

	// Try to get user info from Authorization header
	authHeader := c.Get<PERSON>eader("Authorization")
	if authHeader != "" {
		token := authHeader
		if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
			token = authHeader[7:]
		}

		if claims, err := utils.ValidateJWT(token); err == nil {
			userID = claims.UserID
			// For now, use userID as username since we don't store username in JWT
			username = claims.UserID
		}
	}

	// Upgrade HTTP connection to WebSocket
	conn, err := websocketPkg.Upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Failed to upgrade connection: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to upgrade connection"})
		return
	}

	// Create new client
	client := websocketPkg.NewClient(h.hub, conn, userID, username, h)

	// Register client with hub
	h.hub.Register <- client.Client

	// Start goroutines for reading and writing
	go client.WritePump()
	go client.ReadPump()

	log.Printf("WebSocket connection established for user %s (%s)", username, userID)
}

// HandleCollaborativeMessage handles collaborative editing messages
func (h *WebSocketHandler) HandleCollaborativeMessage(client *websocketPkg.Client, message models.WebSocketMessage) {
	ctx := context.Background()
	
	switch message.Type {
	case models.MessageTypeOperation:
		h.handleOperation(ctx, client, message)
	case models.MessageTypeCursor:
		h.handleCursorUpdate(ctx, client, message)
	case models.MessageTypeSelection:
		h.handleSelectionUpdate(ctx, client, message)
	case models.MessageTypeSync:
		h.handleSyncRequest(ctx, client, message)
	}
}

// handleOperation processes text operations for collaborative editing
func (h *WebSocketHandler) handleOperation(ctx context.Context, client *websocketPkg.Client, message models.WebSocketMessage) {
	var operation models.Operation
	if err := json.Unmarshal(message.Data, &operation); err != nil {
		log.Printf("Error parsing operation: %v", err)
		h.sendError(client, "Invalid operation format")
		return
	}

	// Set operation metadata
	operation.UserID = client.UserID
	operation.Username = client.Username
	operation.Timestamp = time.Now()

	// Apply operation using collaboration service
	result, err := h.collaborationService.ApplyOperation(ctx, &operation)
	if err != nil {
		log.Printf("Error applying operation: %v", err)
		h.sendError(client, "Failed to apply operation")
		return
	}

	if !result.Success {
		h.sendError(client, result.Error)
		return
	}

	// Send acknowledgment to the sender
	ackMessage := models.WebSocketMessage{
		Type:      models.MessageTypeAck,
		PasteID:   operation.PasteID,
		UserID:    client.UserID,
		Data:      mustMarshal(result),
		Timestamp: time.Now(),
	}
	h.sendToClient(client, ackMessage)

	// Broadcast the transformed operation to other collaborators
	broadcastMessage := models.WebSocketMessage{
		Type:      models.MessageTypeOperation,
		PasteID:   operation.PasteID,
		UserID:    client.UserID,
		Username:  client.Username,
		Data:      mustMarshal(result.TransformedOp),
		Timestamp: time.Now(),
	}
	h.broadcastToRoom(operation.PasteID, broadcastMessage, client.UserID)
}

// handleCursorUpdate processes cursor position updates
func (h *WebSocketHandler) handleCursorUpdate(ctx context.Context, client *websocketPkg.Client, message models.WebSocketMessage) {
	var cursor models.CursorPosition
	if err := json.Unmarshal(message.Data, &cursor); err != nil {
		log.Printf("Error parsing cursor position: %v", err)
		return
	}

	// Update cursor in collaboration service
	if err := h.collaborationService.UpdateCursor(ctx, message.PasteID, client.UserID, cursor); err != nil {
		log.Printf("Error updating cursor: %v", err)
		return
	}

	// Broadcast cursor update to other collaborators
	broadcastMessage := models.WebSocketMessage{
		Type:      models.MessageTypeCursor,
		PasteID:   message.PasteID,
		UserID:    client.UserID,
		Username:  client.Username,
		Data:      message.Data,
		Timestamp: time.Now(),
	}
	h.broadcastToRoom(message.PasteID, broadcastMessage, client.UserID)
}

// handleSelectionUpdate processes text selection updates
func (h *WebSocketHandler) handleSelectionUpdate(ctx context.Context, client *websocketPkg.Client, message models.WebSocketMessage) {
	var selection models.Selection
	if err := json.Unmarshal(message.Data, &selection); err != nil {
		log.Printf("Error parsing selection: %v", err)
		return
	}

	// Update selection in collaboration service
	if err := h.collaborationService.UpdateSelection(ctx, message.PasteID, client.UserID, &selection); err != nil {
		log.Printf("Error updating selection: %v", err)
		return
	}

	// Broadcast selection update to other collaborators
	broadcastMessage := models.WebSocketMessage{
		Type:      models.MessageTypeSelection,
		PasteID:   message.PasteID,
		UserID:    client.UserID,
		Username:  client.Username,
		Data:      message.Data,
		Timestamp: time.Now(),
	}
	h.broadcastToRoom(message.PasteID, broadcastMessage, client.UserID)
}

// handleSyncRequest handles requests for document synchronization
func (h *WebSocketHandler) handleSyncRequest(ctx context.Context, client *websocketPkg.Client, message models.WebSocketMessage) {
	docState, err := h.collaborationService.GetDocumentState(ctx, message.PasteID)
	if err != nil {
		log.Printf("Error getting document state: %v", err)
		h.sendError(client, "Failed to get document state")
		return
	}

	// Send current document state to client
	syncMessage := models.WebSocketMessage{
		Type:      models.MessageTypeDocumentState,
		PasteID:   message.PasteID,
		Data:      mustMarshal(docState),
		Timestamp: time.Now(),
	}
	h.sendToClient(client, syncMessage)
}

// JoinCollaborativeSession adds a user to a collaborative editing session
func (h *WebSocketHandler) JoinCollaborativeSession(client *websocketPkg.Client, pasteID string) {
	ctx := context.Background()
	
	// Join document in collaboration service
	docState, err := h.collaborationService.JoinDocument(ctx, pasteID, client.UserID, client.Username)
	if err != nil {
		log.Printf("Error joining collaborative session: %v", err)
		h.sendError(client, "Failed to join collaborative session")
		return
	}

	// Send current document state to the joining user
	stateMessage := models.WebSocketMessage{
		Type:      models.MessageTypeDocumentState,
		PasteID:   pasteID,
		Data:      mustMarshal(docState),
		Timestamp: time.Now(),
	}
	h.sendToClient(client, stateMessage)

	// Broadcast presence update to all collaborators
	h.broadcastPresenceUpdate(pasteID)
}

// LeaveCollaborativeSession removes a user from a collaborative editing session
func (h *WebSocketHandler) LeaveCollaborativeSession(client *websocketPkg.Client, pasteID string) {
	ctx := context.Background()
	
	// Leave document in collaboration service
	if err := h.collaborationService.LeaveDocument(ctx, pasteID, client.UserID); err != nil {
		log.Printf("Error leaving collaborative session: %v", err)
	}

	// Broadcast presence update to remaining collaborators
	h.broadcastPresenceUpdate(pasteID)
}

// Helper methods

// sendToClient sends a message to a specific client
func (h *WebSocketHandler) sendToClient(client *websocketPkg.Client, message models.WebSocketMessage) {
	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("Error marshaling message: %v", err)
		return
	}

	select {
	case client.Send <- messageBytes:
	default:
		log.Printf("Failed to send message to client %s", client.ID)
	}
}

// broadcastToRoom broadcasts a message to all clients in a room except the sender
func (h *WebSocketHandler) broadcastToRoom(pasteID string, message models.WebSocketMessage, excludeUserID string) {
	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("Error marshaling broadcast message: %v", err)
		return
	}

	if roomClients, exists := h.hub.Rooms[pasteID]; exists {
		for client := range roomClients {
			if client.UserID != excludeUserID {
				select {
				case client.Send <- messageBytes:
				default:
					log.Printf("Failed to broadcast to client %s", client.ID)
				}
			}
		}
	}
}

// broadcastPresenceUpdate sends presence updates to all collaborators
func (h *WebSocketHandler) broadcastPresenceUpdate(pasteID string) {
	ctx := context.Background()
	docState, err := h.collaborationService.GetDocumentState(ctx, pasteID)
	if err != nil {
		log.Printf("Error getting document state for presence update: %v", err)
		return
	}

	presenceData := models.PresenceData{
		ActiveUsers: make([]models.ActiveUser, len(docState.Collaborators)),
		UserCount:   len(docState.Collaborators),
	}

	for i, collaborator := range docState.Collaborators {
		presenceData.ActiveUsers[i] = models.ActiveUser{
			UserID:   collaborator.UserID,
			Username: collaborator.Username,
			JoinedAt: collaborator.LastSeen, // Using LastSeen as JoinedAt for now
			LastSeen: collaborator.LastSeen,
		}
	}

	presenceMessage := models.WebSocketMessage{
		Type:      models.MessageTypePresence,
		PasteID:   pasteID,
		Data:      mustMarshal(presenceData),
		Timestamp: time.Now(),
	}

	h.broadcastToRoom(pasteID, presenceMessage, "")
}

// sendError sends an error message to a client
func (h *WebSocketHandler) sendError(client *websocketPkg.Client, errorMsg string) {
	errorMessage := models.WebSocketMessage{
		Type:      models.MessageTypeError,
		Data:      mustMarshal(map[string]string{"error": errorMsg}),
		Timestamp: time.Now(),
	}
	h.sendToClient(client, errorMessage)
}

// mustMarshal marshals data to JSON, panicking on error (for internal use)
func mustMarshal(v interface{}) json.RawMessage {
	data, err := json.Marshal(v)
	if err != nil {
		panic(err)
	}
	return json.RawMessage(data)
}

// GetRoomStats returns statistics about active rooms
func (h *WebSocketHandler) GetRoomStats(c *gin.Context) {
	roomStats := make(map[string]int)
	
	for room, clients := range h.hub.Rooms {
		roomStats[room] = len(clients)
	}

	c.JSON(http.StatusOK, gin.H{
		"total_clients": len(h.hub.Clients),
		"total_rooms":   len(h.hub.Rooms),
		"room_stats":    roomStats,
	})
}