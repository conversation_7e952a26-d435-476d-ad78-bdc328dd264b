package services

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"enhanced-pastebin/internal/models"
)

type DependencyService interface {
	ExtractDependencies(content, language string) (*models.DependencyInfo, error)
	ExtractDependenciesWithSecurity(ctx context.Context, content, language string) (*models.DependencyInfo, error)
}

type dependencyService struct{
	securityService SecurityService
}

func NewDependencyService(securityService SecurityService) DependencyService {
	return &dependencyService{
		securityService: securityService,
	}
}

func (s *dependencyService) ExtractDependencies(content, language string) (*models.DependencyInfo, error) {
	var dependencies []models.Dependency

	switch strings.ToLower(language) {
	case "javascript", "typescript", "jsx", "tsx":
		dependencies = s.extractJavaScriptDependencies(content)
	case "python":
		dependencies = s.extractPythonDependencies(content)
	case "go":
		dependencies = s.extractGoDependencies(content)
	default:
		// For unsupported languages, return empty result
		return &models.DependencyInfo{
			Dependencies: []models.Dependency{},
			Language:     language,
			TotalCount:   0,
		}, nil
	}

	return &models.DependencyInfo{
		Dependencies:  dependencies,
		Language:      language,
		TotalCount:    len(dependencies),
		VulnCount:     0,
		CriticalCount: 0,
		HighCount:     0,
		ModerateCount: 0,
		LowCount:      0,
		SecurityScore: 100,
		LastScanned:   time.Now(),
	}, nil
}

func (s *dependencyService) extractJavaScriptDependencies(content string) []models.Dependency {
	var dependencies []models.Dependency
	seen := make(map[string]bool)

	// ES6 import patterns
	importRegex := regexp.MustCompile(`import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)\s+from\s+)?['"]([^'"]+)['"]`)
	matches := importRegex.FindAllStringSubmatch(content, -1)
	for _, match := range matches {
		if len(match) > 1 {
			depName := match[1]
			if !strings.HasPrefix(depName, ".") && !strings.HasPrefix(depName, "/") && !seen[depName] {
				seen[depName] = true
				dependencies = append(dependencies, models.Dependency{
					Name:        depName,
					Type:        "import",
					Language:    "javascript",
					Registry:    "npm",
					RegistryURL: fmt.Sprintf("https://www.npmjs.com/package/%s", depName),
					InstallCmd:  fmt.Sprintf("npm install %s", depName),
				})
			}
		}
	}

	// CommonJS require patterns
	requireRegex := regexp.MustCompile(`require\s*\(\s*['"]([^'"]+)['"]\s*\)`)
	matches = requireRegex.FindAllStringSubmatch(content, -1)
	for _, match := range matches {
		if len(match) > 1 {
			depName := match[1]
			if !strings.HasPrefix(depName, ".") && !strings.HasPrefix(depName, "/") && !seen[depName] {
				seen[depName] = true
				dependencies = append(dependencies, models.Dependency{
					Name:        depName,
					Type:        "require",
					Language:    "javascript",
					Registry:    "npm",
					RegistryURL: fmt.Sprintf("https://www.npmjs.com/package/%s", depName),
					InstallCmd:  fmt.Sprintf("npm install %s", depName),
				})
			}
		}
	}

	return dependencies
}

func (s *dependencyService) extractPythonDependencies(content string) []models.Dependency {
	var dependencies []models.Dependency
	seen := make(map[string]bool)

	// Python import patterns - handle both "import" and "from ... import"
	lines := strings.Split(content, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		
		// Handle "import module" and "import module as alias"
		if strings.HasPrefix(line, "import ") {
			importRegex := regexp.MustCompile(`import\s+(\w+)(?:\s+as\s+\w+)?`)
			matches := importRegex.FindAllStringSubmatch(line, -1)
			for _, match := range matches {
				if len(match) > 1 {
					depName := strings.ToLower(match[1])
					if !s.isPythonBuiltin(depName) && !seen[depName] {
						seen[depName] = true
						dependencies = append(dependencies, models.Dependency{
							Name:        depName,
							Type:        "import",
							Language:    "python",
							Registry:    "pypi",
							RegistryURL: fmt.Sprintf("https://pypi.org/project/%s/", depName),
							InstallCmd:  fmt.Sprintf("pip install %s", depName),
						})
					}
				}
			}
		}
		
		// Handle "from module import ..." and "from module.submodule import ..."
		if strings.HasPrefix(line, "from ") {
			fromRegex := regexp.MustCompile(`from\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s+import`)
			matches := fromRegex.FindAllStringSubmatch(line, -1)
			for _, match := range matches {
				if len(match) > 1 {
					fullPath := match[1]
					// Get the root module name (first part before any dots)
					depName := strings.Split(fullPath, ".")[0]
					depName = strings.ToLower(depName)
					if !s.isPythonBuiltin(depName) && !seen[depName] {
						seen[depName] = true
						dependencies = append(dependencies, models.Dependency{
							Name:        depName,
							Type:        "import",
							Language:    "python",
							Registry:    "pypi",
							RegistryURL: fmt.Sprintf("https://pypi.org/project/%s/", depName),
							InstallCmd:  fmt.Sprintf("pip install %s", depName),
						})
					}
				}
			}
		}
	}

	return dependencies
}

func (s *dependencyService) extractGoDependencies(content string) []models.Dependency {
	var dependencies []models.Dependency
	seen := make(map[string]bool)

	// Go import patterns
	importRegex := regexp.MustCompile(`import\s+(?:\(\s*(?:[^)]*)\)|"([^"]+)")`)
	matches := importRegex.FindAllStringSubmatch(content, -1)
	for _, match := range matches {
		if len(match) > 1 && match[1] != "" {
			depName := match[1]
			if strings.Contains(depName, ".") && !s.isGoStdLib(depName) && !seen[depName] {
				seen[depName] = true
				dependencies = append(dependencies, models.Dependency{
					Name:        depName,
					Type:        "import",
					Language:    "go",
					Registry:    "go",
					RegistryURL: fmt.Sprintf("https://pkg.go.dev/%s", depName),
					InstallCmd:  fmt.Sprintf("go get %s", depName),
				})
			}
		}
	}

	// Multi-line import blocks
	multiImportRegex := regexp.MustCompile(`import\s*\(\s*((?:[^)]*\n?)*)\)`)
	multiMatches := multiImportRegex.FindAllStringSubmatch(content, -1)
	for _, multiMatch := range multiMatches {
		if len(multiMatch) > 1 {
			importBlock := multiMatch[1]
			lineRegex := regexp.MustCompile(`"([^"]+)"`)
			lineMatches := lineRegex.FindAllStringSubmatch(importBlock, -1)
			for _, lineMatch := range lineMatches {
				if len(lineMatch) > 1 {
					depName := lineMatch[1]
					if strings.Contains(depName, ".") && !s.isGoStdLib(depName) && !seen[depName] {
						seen[depName] = true
						dependencies = append(dependencies, models.Dependency{
							Name:        depName,
							Type:        "import",
							Language:    "go",
							Registry:    "go",
							RegistryURL: fmt.Sprintf("https://pkg.go.dev/%s", depName),
							InstallCmd:  fmt.Sprintf("go get %s", depName),
						})
					}
				}
			}
		}
	}

	return dependencies
}

// isPythonBuiltin checks if a module is part of Python's standard library
func (s *dependencyService) isPythonBuiltin(module string) bool {
	builtins := map[string]bool{
		"os": true, "sys": true, "json": true, "re": true, "time": true,
		"datetime": true, "math": true, "random": true, "collections": true,
		"itertools": true, "functools": true, "operator": true, "pathlib": true,
		"urllib": true, "http": true, "socket": true, "threading": true,
		"multiprocessing": true, "subprocess": true, "logging": true,
		"unittest": true, "pickle": true, "csv": true, "xml": true,
		"html": true, "email": true, "base64": true, "hashlib": true,
		"hmac": true, "secrets": true, "ssl": true, "sqlite3": true,
		"tkinter": true, "argparse": true, "configparser": true,
		"tempfile": true, "shutil": true, "glob": true, "fnmatch": true,
		"linecache": true, "textwrap": true, "string": true, "io": true,
		"struct": true, "codecs": true, "unicodedata": true, "stringprep": true,
		"readline": true, "rlcompleter": true, "pprint": true, "reprlib": true,
		"enum": true, "types": true, "copy": true, "pyclbr": true,
		"pkgutil": true, "modulefinder": true, "runpy": true, "importlib": true,
		"ast": true, "symtable": true, "symbol": true, "token": true,
		"keyword": true, "tokenize": true, "tabnanny": true, "pdb": true,
		"profile": true, "pstats": true, "timeit": true, "trace": true,
		"tracemalloc": true, "gc": true, "inspect": true, "site": true,
	}
	return builtins[module]
}

// isGoStdLib checks if a package is part of Go's standard library
func (s *dependencyService) isGoStdLib(pkg string) bool {
	// Go standard library packages don't contain dots in their import paths
	// or are well-known standard packages
	stdLibPrefixes := []string{
		"fmt", "os", "io", "net", "http", "time", "strings", "strconv",
		"regexp", "sort", "math", "crypto", "encoding", "database",
		"context", "sync", "runtime", "reflect", "unsafe", "builtin",
		"errors", "log", "flag", "path", "bytes", "bufio", "scanner",
		"text", "html", "image", "archive", "compress", "container",
		"debug", "go", "hash", "index", "mime", "plugin", "testing",
		"unicode", "vendor", "internal",
	}

	for _, prefix := range stdLibPrefixes {
		if strings.HasPrefix(pkg, prefix+"/") || pkg == prefix {
			return true
		}
	}

	// If it doesn't contain a dot, it's likely standard library
	return !strings.Contains(pkg, ".")
}

func (s *dependencyService) ExtractDependenciesWithSecurity(ctx context.Context, content, language string) (*models.DependencyInfo, error) {
	// First extract basic dependencies
	basicInfo, err := s.ExtractDependencies(content, language)
	if err != nil {
		return nil, err
	}

	// If no dependencies found, return basic info
	if len(basicInfo.Dependencies) == 0 {
		return basicInfo, nil
	}

	// Scan dependencies for security vulnerabilities
	scannedDeps, err := s.securityService.ScanDependencies(ctx, basicInfo.Dependencies)
	if err != nil {
		// If security scanning fails, return basic info with warning
		return basicInfo, nil
	}

	// Calculate security statistics
	var vulnCount, criticalCount, highCount, moderateCount, lowCount int
	var totalSecurityScore int

	for _, dep := range scannedDeps {
		if dep.Security != nil && dep.Security.HasVulns {
			vulnCount++
			for _, vuln := range dep.Security.Vulnerabilities {
				switch strings.ToLower(vuln.Severity) {
				case "critical":
					criticalCount++
				case "high":
					highCount++
				case "moderate", "medium":
					moderateCount++
				case "low":
					lowCount++
				}
			}
			totalSecurityScore += dep.Security.SecurityScore
		} else if dep.Security != nil {
			totalSecurityScore += dep.Security.SecurityScore
		} else {
			totalSecurityScore += 100 // Assume safe if no security info
		}
	}

	// Calculate overall security score
	overallSecurityScore := 100
	if len(scannedDeps) > 0 {
		overallSecurityScore = totalSecurityScore / len(scannedDeps)
	}

	return &models.DependencyInfo{
		Dependencies:  scannedDeps,
		Language:      language,
		TotalCount:    len(scannedDeps),
		VulnCount:     vulnCount,
		CriticalCount: criticalCount,
		HighCount:     highCount,
		ModerateCount: moderateCount,
		LowCount:      lowCount,
		SecurityScore: overallSecurityScore,
		LastScanned:   time.Now(),
	}, nil
}