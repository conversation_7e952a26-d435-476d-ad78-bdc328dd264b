# Security Vulnerability Scanning Test

This document tests the security vulnerability scanning functionality.

## Test Steps

1. Start the server
2. Create a paste with JavaScript dependencies
3. Test dependency extraction without security scanning
4. Test dependency extraction with security scanning
5. Test the dedicated security endpoint

## Test Commands

### 1. Create a test paste with vulnerable dependencies

```bash
curl -X POST http://localhost:8080/api/v1/pastes \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Security Scanning",
    "content": "import React from '\''react'\'';\nimport axios from '\''axios'\'';\nimport lodash from '\''lodash'\'';\nimport moment from '\''moment'\'';",
    "language": "javascript"
  }'
```

### 2. Get dependencies without security scanning

```bash
curl http://localhost:8080/api/v1/pastes/{PASTE_ID}/dependencies
```

### 3. Get dependencies with security scanning

```bash
curl http://localhost:8080/api/v1/pastes/{PASTE_ID}/dependencies?security=true
```

### 4. Get security scan results

```bash
curl http://localhost:8080/api/v1/pastes/{PASTE_ID}/security
```

## Expected Results

1. **Basic dependencies**: Should return dependency information without security details
2. **Dependencies with security**: Should return dependency information with security scores and vulnerability counts
3. **Security endpoint**: Should return a security summary with vulnerability statistics

## Security Features Implemented

- ✅ Integration with OSV (Open Source Vulnerabilities) database
- ✅ Support for npm audit (when available)
- ✅ Security scoring (0-100 scale)
- ✅ Vulnerability severity classification (critical, high, moderate, low)
- ✅ Security information display in frontend
- ✅ Vulnerability details with CVE information
- ✅ Security warnings and indicators

## API Endpoints Added

- `GET /api/v1/pastes/{id}/dependencies?security=true` - Get dependencies with security scanning
- `GET /api/v1/pastes/{id}/security` - Get security scan summary

## Frontend Features Added

- Security badges showing vulnerability counts
- Security score display (0-100)
- Expandable vulnerability details
- Color-coded severity indicators
- CVE and CVSS information display
- Links to vulnerability advisories