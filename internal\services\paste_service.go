package services

import (
	"errors"
	"time"

	"enhanced-pastebin/internal/models"
	"enhanced-pastebin/internal/repositories"
	"enhanced-pastebin/internal/utils"
)

type PasteService interface {
	CreatePaste(req *models.CreatePasteRequest, userID *string) (*models.Paste, error)
	GetPaste(id string) (*models.Paste, error)
	GetPasteByCustomURL(customURL string) (*models.Paste, error)
	UpdatePaste(id string, req *models.UpdatePasteRequest, userID string) (*models.Paste, error)
	DeletePaste(id string, userID string) error
	GetUserPastes(userID string, limit, offset int) ([]*models.Paste, error)
	CheckCustomURLAvailability(customURL string) (bool, error)
}

type pasteService struct {
	pasteRepo    repositories.PasteRepository
	versionRepo  repositories.VersionRepository
}

// Add edit count tracking to paste model
func (s *pasteService) incrementEditCount(pasteID string) error {
	// This would require adding an edit_count field to the pastes table
	// For now, we'll track this through version count
	return nil
}

func NewPasteService(pasteRepo repositories.PasteRepository, versionRepo repositories.VersionRepository) PasteService {
	return &pasteService{
		pasteRepo:   pasteRepo,
		versionRepo: versionRepo,
	}
}

func (s *pasteService) CreatePaste(req *models.CreatePasteRequest, userID *string) (*models.Paste, error) {
	// Validate content
	if err := s.validatePasteContent(req); err != nil {
		return nil, err
	}

	// Check if custom URL is already taken
	if req.CustomURL != nil && *req.CustomURL != "" {
		existing, err := s.pasteRepo.GetByCustomURL(*req.CustomURL)
		if err != nil {
			return nil, err
		}
		if existing != nil {
			return nil, errors.New("custom URL already taken")
		}
	}

	// Create paste
	paste := &models.Paste{
		ID:           utils.GenerateID(),
		CustomURL:    req.CustomURL,
		Title:        req.Title,
		Content:      req.Content,
		Language:     req.Language,
		IsEncrypted:  req.IsEncrypted,
		ExpiresAt:    req.ExpiresAt,
		ViewCount:    0,
		MaxViews:     req.MaxViews,
		IsWatermarked: req.IsWatermarked,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		UserID:       userID,
	}

	if err := s.pasteRepo.Create(paste); err != nil {
		return nil, err
	}

	return paste, nil
}

func (s *pasteService) GetPaste(id string) (*models.Paste, error) {
	paste, err := s.pasteRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	if paste == nil {
		return nil, errors.New("paste not found")
	}

	// Check if paste has expired
	if paste.ExpiresAt != nil && time.Now().After(*paste.ExpiresAt) {
		return nil, errors.New("paste has expired")
	}

	// Check view limit
	if paste.MaxViews != nil && paste.ViewCount >= *paste.MaxViews {
		return nil, errors.New("paste view limit exceeded")
	}

	// Increment view count
	if err := s.pasteRepo.IncrementViewCount(id); err != nil {
		return nil, err
	}

	return paste, nil
}

func (s *pasteService) GetPasteByCustomURL(customURL string) (*models.Paste, error) {
	paste, err := s.pasteRepo.GetByCustomURL(customURL)
	if err != nil {
		return nil, err
	}
	if paste == nil {
		return nil, errors.New("paste not found")
	}

	// Check if paste has expired
	if paste.ExpiresAt != nil && time.Now().After(*paste.ExpiresAt) {
		return nil, errors.New("paste has expired")
	}

	// Check view limit
	if paste.MaxViews != nil && paste.ViewCount >= *paste.MaxViews {
		return nil, errors.New("paste view limit exceeded")
	}

	// Increment view count
	if err := s.pasteRepo.IncrementViewCount(paste.ID); err != nil {
		return nil, err
	}

	return paste, nil
}

func (s *pasteService) UpdatePaste(id string, req *models.UpdatePasteRequest, userID string) (*models.Paste, error) {
	paste, err := s.pasteRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	if paste == nil {
		return nil, errors.New("paste not found")
	}

	// Check ownership
	if paste.UserID == nil || *paste.UserID != userID {
		return nil, errors.New("unauthorized")
	}

	// Validate update request
	if err := s.validatePasteUpdate(req); err != nil {
		return nil, err
	}

	// Store original content for version tracking
	originalContent := paste.Content

	// Track if any content changes were made
	contentChanged := false

	// Update fields
	if req.Title != nil {
		paste.Title = *req.Title
		contentChanged = true
	}
	if req.Content != nil {
		// Create a version with the old content before updating
		if *req.Content != originalContent {
			s.createVersionFromUpdate(id, originalContent, &userID)
			contentChanged = true
		}
		paste.Content = *req.Content
	}
	if req.Language != nil {
		paste.Language = *req.Language
		contentChanged = true
	}
	if req.ExpiresAt != nil {
		paste.ExpiresAt = req.ExpiresAt
	}
	if req.MaxViews != nil {
		paste.MaxViews = req.MaxViews
	}
	if req.IsWatermarked != nil {
		paste.IsWatermarked = *req.IsWatermarked
	}

	// Increment edit count if content was changed
	if contentChanged {
		paste.EditCount++
	}

	paste.UpdatedAt = time.Now()

	if err := s.pasteRepo.Update(paste); err != nil {
		return nil, err
	}

	return paste, nil
}

// createVersionFromUpdate creates a version entry for the old content
func (s *pasteService) createVersionFromUpdate(pasteID, oldContent string, userID *string) {
	// Get next version number
	versionNumber, err := s.versionRepo.GetNextVersionNumber(pasteID)
	if err != nil {
		// Log error but don't fail the update
		return
	}

	version := &models.PasteVersion{
		ID:            utils.GenerateID(),
		PasteID:       pasteID,
		VersionNumber: versionNumber,
		Content:       oldContent,
		CreatedAt:     time.Now(),
		CreatedBy:     userID,
	}

	// Create version (ignore errors to not fail the paste update)
	s.versionRepo.Create(version)
}

// validatePasteUpdate validates paste update request
func (s *pasteService) validatePasteUpdate(req *models.UpdatePasteRequest) error {
	// Check content length if provided
	if req.Content != nil && len(*req.Content) > 1024*1024 {
		return errors.New("content too large (max 1MB)")
	}

	// Check title length if provided
	if req.Title != nil {
		if len(*req.Title) == 0 {
			return errors.New("title cannot be empty")
		}
		if len(*req.Title) > 500 {
			return errors.New("title too long (max 500 characters)")
		}
	}

	// Validate expiration settings if provided
	if req.ExpiresAt != nil && req.ExpiresAt.Before(time.Now()) {
		return errors.New("expiration date must be in the future")
	}

	// Validate max views if provided
	if req.MaxViews != nil && *req.MaxViews < 1 {
		return errors.New("max views must be at least 1")
	}

	return nil
}

func (s *pasteService) DeletePaste(id string, userID string) error {
	paste, err := s.pasteRepo.GetByID(id)
	if err != nil {
		return err
	}
	if paste == nil {
		return errors.New("paste not found")
	}

	// Check ownership
	if paste.UserID == nil || *paste.UserID != userID {
		return errors.New("unauthorized")
	}

	return s.pasteRepo.Delete(id)
}

func (s *pasteService) GetUserPastes(userID string, limit, offset int) ([]*models.Paste, error) {
	return s.pasteRepo.GetByUserID(userID, limit, offset)
}

// validatePasteContent validates paste content and metadata
func (s *pasteService) validatePasteContent(req *models.CreatePasteRequest) error {
	// Check content
	if len(req.Content) == 0 {
		return errors.New("content is required")
	}
	if len(req.Content) > 1024*1024 {
		return errors.New("content too large (max 1MB)")
	}

	// Check title length
	if len(req.Title) == 0 {
		return errors.New("title is required")
	}
	if len(req.Title) > 500 {
		return errors.New("title too long (max 500 characters)")
	}

	// Validate custom URL format if provided
	if req.CustomURL != nil && *req.CustomURL != "" {
		if err := s.validateCustomURL(*req.CustomURL); err != nil {
			return err
		}
	}

	// Validate expiration settings
	if req.ExpiresAt != nil && req.ExpiresAt.Before(time.Now()) {
		return errors.New("expiration date must be in the future")
	}

	// Validate max views
	if req.MaxViews != nil && *req.MaxViews < 1 {
		return errors.New("max views must be at least 1")
	}

	return nil
}

func (s *pasteService) CheckCustomURLAvailability(customURL string) (bool, error) {
	// First validate the format
	if err := s.validateCustomURL(customURL); err != nil {
		return false, err
	}

	// Check if URL is already taken
	existing, err := s.pasteRepo.GetByCustomURL(customURL)
	if err != nil {
		return false, err
	}

	// Available if no existing paste found
	return existing == nil, nil
}

// validateCustomURL validates custom URL format and reserved words
func (s *pasteService) validateCustomURL(customURL string) error {
	// Check length
	if len(customURL) < 3 {
		return errors.New("custom URL must be at least 3 characters")
	}
	if len(customURL) > 255 {
		return errors.New("custom URL too long (max 255 characters)")
	}

	// Check for valid characters (alphanumeric, hyphens, underscores)
	for _, char := range customURL {
		if !((char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') || 
			 (char >= '0' && char <= '9') || char == '-' || char == '_') {
			return errors.New("custom URL can only contain letters, numbers, hyphens, and underscores")
		}
	}

	// Check for reserved words
	reservedWords := []string{
		"api", "admin", "www", "mail", "ftp", "localhost", "create", "edit", "delete",
		"user", "users", "paste", "pastes", "login", "register", "profile", "settings",
		"help", "about", "contact", "terms", "privacy", "static", "assets", "public",
		"health", "status", "ping", "test", "debug", "error", "null", "undefined",
	}

	for _, reserved := range reservedWords {
		if customURL == reserved {
			return errors.New("custom URL cannot use reserved word: " + reserved)
		}
	}

	return nil
}