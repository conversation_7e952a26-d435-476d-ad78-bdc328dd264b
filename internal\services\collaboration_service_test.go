package services

import (
	"context"
	"testing"

	"enhanced-pastebin/internal/models"
)

func TestCollaborationService_JoinDocument(t *testing.T) {
	service := NewCollaborationService()
	ctx := context.Background()

	pasteID := "test-paste-123"
	userID := "user-1"
	username := "testuser"

	// Test joining a new document
	docState, err := service.JoinDocument(ctx, pasteID, userID, username)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if docState.PasteID != pasteID {
		t.<PERSON>rrorf("Expected paste ID %s, got %s", pasteID, docState.PasteID)
	}

	if len(docState.Collaborators) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 collaborator, got %d", len(docState.Collaborators))
	}

	collaborator := docState.Collaborators[0]
	if collaborator.UserID != userID {
		t.<PERSON>rrorf("Expected user ID %s, got %s", userID, collaborator.UserID)
	}

	if collaborator.Username != username {
		t.<PERSON>("Expected username %s, got %s", username, collaborator.Username)
	}

	if collaborator.Color == "" {
		t.<PERSON>rror("Expected collaborator to have a color assigned")
	}
}

func TestCollaborationService_ApplyOperation(t *testing.T) {
	service := NewCollaborationService()
	ctx := context.Background()

	pasteID := "test-paste-123"
	userID := "user-1"
	username := "testuser"

	// Join document first
	_, err := service.JoinDocument(ctx, pasteID, userID, username)
	if err != nil {
		t.Fatalf("Failed to join document: %v", err)
	}

	// Create an insert operation
	operation := &models.Operation{
		ID:       "op-1",
		PasteID:  pasteID,
		UserID:   userID,
		Username: username,
		Operations: []models.TextOperation{
			{
				Type:     models.OperationTypeInsert,
				Position: 0,
				Content:  "Hello, World!",
			},
		},
		Version: 0,
	}

	// Apply the operation
	result, err := service.ApplyOperation(ctx, operation)
	if err != nil {
		t.Fatalf("Failed to apply operation: %v", err)
	}

	if !result.Success {
		t.Errorf("Expected operation to succeed, got error: %s", result.Error)
	}

	if result.NewContent != "Hello, World!" {
		t.Errorf("Expected content 'Hello, World!', got '%s'", result.NewContent)
	}

	if result.NewVersion != 1 {
		t.Errorf("Expected version 1, got %d", result.NewVersion)
	}
}

func TestCollaborationService_TransformOperation(t *testing.T) {
	service := NewCollaborationService()

	// Test insert-insert transformation
	op1 := &models.Operation{
		Operations: []models.TextOperation{
			{
				Type:     models.OperationTypeInsert,
				Position: 5,
				Content:  "ABC",
			},
		},
	}

	op2 := &models.Operation{
		Operations: []models.TextOperation{
			{
				Type:     models.OperationTypeInsert,
				Position: 3,
				Content:  "XYZ",
			},
		},
	}

	transformed, err := service.transformOperation(op1, op2)
	if err != nil {
		t.Fatalf("Failed to transform operation: %v", err)
	}

	// op1's position should be shifted by the length of op2's content
	expectedPosition := 5 + len("XYZ")
	if transformed.Operations[0].Position != expectedPosition {
		t.Errorf("Expected position %d, got %d", expectedPosition, transformed.Operations[0].Position)
	}
}

func TestCollaborationService_ApplyTextOperation(t *testing.T) {
	service := NewCollaborationService()

	tests := []struct {
		name      string
		content   string
		operation models.TextOperation
		expected  string
		expectErr bool
	}{
		{
			name:    "Insert at beginning",
			content: "World!",
			operation: models.TextOperation{
				Type:     models.OperationTypeInsert,
				Position: 0,
				Content:  "Hello, ",
			},
			expected: "Hello, World!",
		},
		{
			name:    "Insert in middle",
			content: "Hello World!",
			operation: models.TextOperation{
				Type:     models.OperationTypeInsert,
				Position: 5,
				Content:  ", Beautiful",
			},
			expected: "Hello, Beautiful World!",
		},
		{
			name:    "Delete from beginning",
			content: "Hello, World!",
			operation: models.TextOperation{
				Type:     models.OperationTypeDelete,
				Position: 0,
				Length:   7,
			},
			expected: "World!",
		},
		{
			name:    "Delete from middle",
			content: "Hello, Beautiful World!",
			operation: models.TextOperation{
				Type:     models.OperationTypeDelete,
				Position: 5,
				Length:   11, // ", Beautiful" is 11 characters
			},
			expected: "Hello World!",
		},
		{
			name:    "Insert out of bounds",
			content: "Hello",
			operation: models.TextOperation{
				Type:     models.OperationTypeInsert,
				Position: 10,
				Content:  "World",
			},
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.applyTextOperation(tt.content, tt.operation)
			
			if tt.expectErr {
				if err == nil {
					t.Error("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Fatalf("Unexpected error: %v", err)
			}

			if result != tt.expected {
				t.Errorf("Expected '%s', got '%s'", tt.expected, result)
			}
		})
	}
}

func TestCollaborationService_LeaveDocument(t *testing.T) {
	service := NewCollaborationService()
	ctx := context.Background()

	pasteID := "test-paste-123"
	userID1 := "user-1"
	userID2 := "user-2"
	username1 := "testuser1"
	username2 := "testuser2"

	// Join document with two users
	_, err := service.JoinDocument(ctx, pasteID, userID1, username1)
	if err != nil {
		t.Fatalf("Failed to join document: %v", err)
	}

	docState, err := service.JoinDocument(ctx, pasteID, userID2, username2)
	if err != nil {
		t.Fatalf("Failed to join document: %v", err)
	}

	if len(docState.Collaborators) != 2 {
		t.Errorf("Expected 2 collaborators, got %d", len(docState.Collaborators))
	}

	// Leave document with first user
	err = service.LeaveDocument(ctx, pasteID, userID1)
	if err != nil {
		t.Fatalf("Failed to leave document: %v", err)
	}

	// Check remaining collaborators
	docState, err = service.GetDocumentState(ctx, pasteID)
	if err != nil {
		t.Fatalf("Failed to get document state: %v", err)
	}

	if len(docState.Collaborators) != 1 {
		t.Errorf("Expected 1 collaborator after leave, got %d", len(docState.Collaborators))
	}

	if docState.Collaborators[0].UserID != userID2 {
		t.Errorf("Expected remaining user to be %s, got %s", userID2, docState.Collaborators[0].UserID)
	}
}