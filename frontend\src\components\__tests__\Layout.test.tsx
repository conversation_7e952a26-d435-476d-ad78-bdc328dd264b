import { screen, fireEvent, waitFor } from '@testing-library/react';
import { render, mockUser, setupTest, cleanupTest } from '@/test-utils';
import Layout from '@/components/Layout';

// Mock the child components that might not be needed for layout tests
jest.mock('@/components/AccessibilityChecker', () => {
  return function MockAccessibilityChecker() {
    return <div data-testid="accessibility-checker">Accessibility Checker</div>;
  };
});

jest.mock('@/components/PerformanceMonitor', () => {
  return function MockPerformanceMonitor() {
    return <div data-testid="performance-monitor">Performance Monitor</div>;
  };
});

describe('Layout Component', () => {
  beforeEach(() => {
    setupTest();
  });

  afterEach(() => {
    cleanupTest();
  });

  it('renders the main navigation', () => {
    render(
      <Layout>
        <div>Test Content</div>
      </Layout>
    );

    expect(screen.getByText('Enhanced Pastebin')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('shows authenticated user navigation when logged in', () => {
    render(
      <Layout>
        <div>Test Content</div>
      </Layout>
    );

    // Should show authenticated navigation
    expect(screen.getByText('New Paste')).toBeInTheDocument();
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('Logout')).toBeInTheDocument();
  });

  it('handles mobile menu toggle', async () => {
    render(
      <Layout>
        <div>Test Content</div>
      </Layout>
    );

    // Find the mobile menu button (should be hidden on desktop but present in DOM)
    const menuButton = screen.getByLabelText('Toggle menu');
    expect(menuButton).toBeInTheDocument();

    // Click to open mobile menu
    fireEvent.click(menuButton);

    // Mobile menu should be visible
    await waitFor(() => {
      expect(screen.getAllByText('New Paste')).toHaveLength(2); // Desktop + mobile
    });

    // Click to close mobile menu
    fireEvent.click(menuButton);

    // Mobile menu should be hidden
    await waitFor(() => {
      expect(screen.getAllByText('New Paste')).toHaveLength(1); // Only desktop
    });
  });

  it('closes mobile menu when clicking navigation links', async () => {
    render(
      <Layout>
        <div>Test Content</div>
      </Layout>
    );

    const menuButton = screen.getByLabelText('Toggle menu');
    fireEvent.click(menuButton);

    // Wait for mobile menu to open
    await waitFor(() => {
      expect(screen.getAllByText('Profile')).toHaveLength(2);
    });

    // Click on a mobile navigation link
    const mobileProfileLinks = screen.getAllByText('Profile');
    const mobileProfileLink = mobileProfileLinks.find(link => 
      link.closest('nav')?.className.includes('md:hidden')
    );
    
    if (mobileProfileLink) {
      fireEvent.click(mobileProfileLink);
    }

    // Mobile menu should close
    await waitFor(() => {
      expect(screen.getAllByText('Profile')).toHaveLength(1);
    });
  });

  it('handles keyboard navigation for mobile menu', async () => {
    render(
      <Layout>
        <div>Test Content</div>
      </Layout>
    );

    const menuButton = screen.getByLabelText('Toggle menu');
    
    // Open mobile menu
    fireEvent.click(menuButton);
    
    await waitFor(() => {
      expect(screen.getAllByText('New Paste')).toHaveLength(2);
    });

    // Press Escape to close menu
    fireEvent.keyDown(document, { key: 'Escape' });

    await waitFor(() => {
      expect(screen.getAllByText('New Paste')).toHaveLength(1);
    });
  });

  it('has proper accessibility attributes', () => {
    render(
      <Layout>
        <div>Test Content</div>
      </Layout>
    );

    // Check main navigation has proper structure
    const nav = screen.getByRole('navigation');
    expect(nav).toBeInTheDocument();

    // Check mobile menu button has proper attributes
    const menuButton = screen.getByLabelText('Toggle menu');
    expect(menuButton).toHaveAttribute('aria-expanded');

    // Check main content area
    const main = screen.getByRole('main');
    expect(main).toBeInTheDocument();
    expect(main).toHaveAttribute('id', 'main-content');
  });

  it('renders development tools in development mode', () => {
    // Mock NODE_ENV for this test
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    render(
      <Layout>
        <div>Test Content</div>
      </Layout>
    );

    expect(screen.getByTestId('accessibility-checker')).toBeInTheDocument();
    expect(screen.getByTestId('performance-monitor')).toBeInTheDocument();

    // Restore original environment
    process.env.NODE_ENV = originalEnv;
  });

  it('has responsive design classes', () => {
    render(
      <Layout>
        <div>Test Content</div>
      </Layout>
    );

    // Check that responsive classes are applied
    const header = screen.getByRole('banner');
    expect(header).toHaveClass('border-b');

    const container = header.querySelector('.container');
    expect(container).toHaveClass('mx-auto', 'px-4', 'py-4');
  });

  it('handles focus management properly', () => {
    render(
      <Layout>
        <div>Test Content</div>
      </Layout>
    );

    // Check that the logo link has proper focus styles
    const logoLink = screen.getByText('Enhanced Pastebin').closest('a');
    expect(logoLink).toHaveClass('focus:outline-none', 'focus:ring-2');
  });

  it('shows correct navigation based on authentication state', () => {
    // This test uses the mocked authenticated user from test-utils
    render(
      <Layout>
        <div>Test Content</div>
      </Layout>
    );

    // Should show authenticated navigation
    expect(screen.getByText('New Paste')).toBeInTheDocument();
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('Logout')).toBeInTheDocument();

    // Should not show unauthenticated navigation
    expect(screen.queryByText('Login')).not.toBeInTheDocument();
    expect(screen.queryByText('Register')).not.toBeInTheDocument();
  });

  it('maintains proper heading hierarchy', () => {
    render(
      <Layout>
        <h1>Page Title</h1>
        <div>Test Content</div>
      </Layout>
    );

    // The layout should not interfere with heading hierarchy
    const headings = screen.getAllByRole('heading');
    expect(headings[0]).toHaveTextContent('Page Title');
  });

  it('handles window resize events properly', () => {
    render(
      <Layout>
        <div>Test Content</div>
      </Layout>
    );

    // Simulate window resize
    global.innerWidth = 500;
    fireEvent(window, new Event('resize'));

    // Mobile menu button should still be functional
    const menuButton = screen.getByLabelText('Toggle menu');
    expect(menuButton).toBeInTheDocument();
  });
});
