import React from 'react';
import JsonViewer from './JsonViewer';
import MarkdownPreview from './MarkdownPreview';

interface MultiFormatViewerProps {
  content: string;
  language: string;
  title?: string;
}

export default function MultiFormatViewer({ content, language, title }: MultiFormatViewerProps) {
  // Detect format based on language and content
  const detectFormat = () => {
    const lang = language.toLowerCase();
    
    // JSON detection
    if (lang === 'json' || isValidJson(content)) {
      return 'json';
    }
    
    // Markdown detection
    if (lang === 'markdown' || lang === 'md' || hasMarkdownSyntax(content)) {
      return 'markdown';
    }
    
    // XML detection
    if (lang === 'xml' || hasXmlSyntax(content)) {
      return 'xml';
    }
    
    // YAML detection
    if (lang === 'yaml' || lang === 'yml' || hasYamlSyntax(content)) {
      return 'yaml';
    }
    
    return 'code';
  };

  const isValidJson = (str: string): boolean => {
    try {
      JSON.parse(str.trim());
      return true;
    } catch {
      return false;
    }
  };

  const hasMarkdownSyntax = (str: string): boolean => {
    const markdownPatterns = [
      /^#{1,6}\s+/m,           // Headers
      /\*\*.*?\*\*/,           // Bold
      /\*.*?\*/,               // Italic
      /\[.*?\]\(.*?\)/,        // Links
      /```[\s\S]*?```/,        // Code blocks
      /`.*?`/,                 // Inline code
      /^\* /m,                 // Unordered lists
      /^\d+\. /m,              // Ordered lists
      /^> /m,                  // Blockquotes
    ];
    
    return markdownPatterns.some(pattern => pattern.test(str));
  };

  const hasXmlSyntax = (str: string): boolean => {
    const trimmed = str.trim();
    return trimmed.startsWith('<') && trimmed.endsWith('>') && 
           /<[^>]+>/.test(trimmed);
  };

  const hasYamlSyntax = (str: string): boolean => {
    const yamlPatterns = [
      /^---\s*$/m,             // YAML document separator
      /^\s*\w+:\s*\w+/m,       // Key-value pairs
      /^\s*-\s+\w+/m,          // Array items
    ];
    
    return yamlPatterns.some(pattern => pattern.test(str));
  };

  const renderXmlViewer = () => (
    <div className="mt-4">
      <div className="bg-muted p-4 rounded-md">
        <h3 className="text-lg font-medium mb-2">XML Content</h3>
        <pre className="text-sm font-mono overflow-auto max-h-96 bg-background p-3 rounded border">
          {formatXml(content)}
        </pre>
      </div>
    </div>
  );

  const renderYamlViewer = () => (
    <div className="mt-4">
      <div className="bg-muted p-4 rounded-md">
        <h3 className="text-lg font-medium mb-2">YAML Content</h3>
        <pre className="text-sm font-mono overflow-auto max-h-96 bg-background p-3 rounded border whitespace-pre-wrap">
          {content}
        </pre>
      </div>
    </div>
  );

  const formatXml = (xml: string): string => {
    try {
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xml, 'text/xml');
      const serializer = new XMLSerializer();
      let formatted = serializer.serializeToString(xmlDoc);
      
      // Basic indentation
      formatted = formatted.replace(/></g, '>\n<');
      const lines = formatted.split('\n');
      let indent = 0;
      
      return lines.map(line => {
        const trimmed = line.trim();
        if (trimmed.startsWith('</')) {
          indent = Math.max(0, indent - 1);
        }
        
        const indentedLine = '  '.repeat(indent) + trimmed;
        
        if (trimmed.startsWith('<') && !trimmed.startsWith('</') && !trimmed.endsWith('/>')) {
          indent++;
        }
        
        return indentedLine;
      }).join('\n');
    } catch {
      return xml;
    }
  };

  const format = detectFormat();

  // Don't render anything if content is empty
  if (!content.trim()) {
    return null;
  }

  switch (format) {
    case 'json':
      return <JsonViewer content={content} title={title || "JSON Data"} />;
    
    case 'markdown':
      return <MarkdownPreview content={content} title={title || "Markdown Preview"} />;
    
    case 'xml':
      return renderXmlViewer();
    
    case 'yaml':
      return renderYamlViewer();
    
    default:
      // For regular code, don't show additional viewer
      return null;
  }
}
