import { FullConfig } from '@playwright/test';
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown...');

  try {
    // Stop docker-compose services
    console.log('🛑 Stopping database services...');
    await stopDatabaseServices();

    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw here as we want tests to complete even if teardown fails
  }
}

async function stopDatabaseServices(): Promise<void> {
  return new Promise((resolve, reject) => {
    const projectRoot = path.resolve(__dirname, '../../..');
    
    const stopProcess = spawn('docker-compose', ['down'], {
      cwd: projectRoot,
      stdio: 'pipe',
      shell: true
    });

    let output = '';
    stopProcess.stdout?.on('data', (data) => {
      output += data.toString();
    });

    stopProcess.stderr?.on('data', (data) => {
      output += data.toString();
    });

    stopProcess.on('close', (code) => {
      if (code === 0) {
        console.log('Database services stopped successfully');
        resolve();
      } else {
        console.warn(`Database services stop returned code ${code}: ${output}`);
        resolve(); // Don't fail teardown if services were already stopped
      }
    });

    stopProcess.on('error', (error) => {
      console.warn(`Failed to stop database services: ${error.message}`);
      resolve(); // Don't fail teardown
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      console.warn('Database services stop timed out');
      resolve();
    }, 30000);
  });
}

export default globalTeardown;
