{"name": "enhanced-pastebin-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@monaco-editor/react": "^4.6.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "axios": "^1.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "js-beautify": "^1.15.4", "lucide-react": "^0.292.0", "prettier": "^3.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@types/js-beautify": "^1.14.3", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^26.1.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^3.2.4"}}