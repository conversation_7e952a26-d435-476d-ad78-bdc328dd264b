# Dependency Detection Test

## Test Cases

### JavaScript/TypeScript
```javascript
import React from 'react';
import { useState, useEffect } from 'react';
import axios from 'axios';
import lodash from 'lodash';
const express = require('express');
const fs = require('fs');
```

Expected dependencies:
- react (npm)
- axios (npm) 
- lodash (npm)
- express (npm)
- fs (npm)

### Python
```python
import requests
import numpy as np
from flask import Flask, request
from django.http import HttpResponse
import os
import sys
```

Expected dependencies:
- requests (pypi)
- numpy (pypi)
- flask (pypi)
- django (pypi)

### Go
```go
package main

import (
    "fmt"
    "net/http"
    "github.com/gin-gonic/gin"
    "github.com/lib/pq"
    "gorm.io/gorm"
)
```

Expected dependencies:
- github.com/gin-gonic/gin (go)
- github.com/lib/pq (go)
- gorm.io/gorm (go)

## API Endpoint

Test the dependency detection by creating a paste and then calling:
`GET /api/v1/pastes/{id}/dependencies`

## Frontend Integration

The dependency information should appear automatically on the ViewPaste page for supported languages.