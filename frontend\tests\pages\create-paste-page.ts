import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base-page';

export class CreatePastePage extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  // Page-specific locators
  get pageTitle(): Locator {
    return this.page.locator('h1').first();
  }

  get createForm(): Locator {
    return this.page.locator('form, [data-testid="create-paste-form"]').first();
  }

  get titleInput(): Locator {
    return this.page.locator('input[name="title"]').first();
  }

  get contentEditor(): Locator {
    return this.page.locator('.monaco-editor, textarea[name="content"]').first();
  }

  get languageSelect(): Locator {
    return this.page.locator('select[name="language"], [data-testid="language-select"]').first();
  }

  get customUrlInput(): Locator {
    return this.page.locator('input[name="custom_url"], input[name="customUrl"]').first();
  }

  get isPublicCheckbox(): Locator {
    return this.page.locator('input[name="is_public"], input[name="isPublic"]').first();
  }

  get expirationSelect(): Locator {
    return this.page.locator('select[name="expires_at"], select[name="expiration"]').first();
  }

  get encryptionCheckbox(): Locator {
    return this.page.locator('input[name="encrypted"], input[type="checkbox"]:near(:text("encrypt"))').first();
  }

  get passwordInput(): Locator {
    return this.page.locator('input[name="password"], input[type="password"]').first();
  }

  get submitButton(): Locator {
    return this.page.locator('button[type="submit"], button:has-text("Create")').first();
  }

  get previewButton(): Locator {
    return this.page.locator('button:has-text("Preview")').first();
  }

  get formatButton(): Locator {
    return this.page.locator('button:has-text("Format")').first();
  }

  get validationErrors(): Locator {
    return this.page.locator('.field-error, .validation-error, [data-testid="field-error"]');
  }

  get titleError(): Locator {
    return this.page.locator('.field-error:near(input[name="title"]), [data-testid="title-error"]').first();
  }

  get contentError(): Locator {
    return this.page.locator('.field-error:near(.monaco-editor), [data-testid="content-error"]').first();
  }

  get customUrlError(): Locator {
    return this.page.locator('.field-error:near(input[name="custom_url"]), [data-testid="custom-url-error"]').first();
  }

  get customUrlAvailability(): Locator {
    return this.page.locator('.url-availability, [data-testid="url-availability"]').first();
  }

  get previewPanel(): Locator {
    return this.page.locator('.preview-panel, [data-testid="preview"]').first();
  }

  // Page actions
  async visit(): Promise<void> {
    await this.page.goto('/create');
    await this.waitForPageLoad();
  }

  async fillTitle(title: string): Promise<void> {
    await this.titleInput.fill(title);
  }

  async fillContent(content: string): Promise<void> {
    // Handle Monaco editor or textarea
    if (await this.page.locator('.monaco-editor').isVisible()) {
      await this.contentEditor.click();
      await this.page.keyboard.press('Control+A');
      await this.page.keyboard.type(content);
    } else {
      await this.contentEditor.fill(content);
    }
  }

  async selectLanguage(language: string): Promise<void> {
    await this.languageSelect.selectOption(language);
  }

  async fillCustomUrl(customUrl: string): Promise<void> {
    await this.customUrlInput.fill(customUrl);
  }

  async setPublic(isPublic: boolean): Promise<void> {
    const isChecked = await this.isPublicCheckbox.isChecked();
    if (isPublic !== isChecked) {
      await this.isPublicCheckbox.click();
    }
  }

  async setExpiration(expiration: string): Promise<void> {
    await this.expirationSelect.selectOption(expiration);
  }

  async setEncryption(encrypted: boolean, password?: string): Promise<void> {
    const isChecked = await this.encryptionCheckbox.isChecked();
    if (encrypted !== isChecked) {
      await this.encryptionCheckbox.click();
    }
    
    if (encrypted && password) {
      await this.passwordInput.fill(password);
    }
  }

  async submitForm(): Promise<void> {
    await this.submitButton.click();
  }

  async previewPaste(): Promise<void> {
    await this.previewButton.click();
  }

  async formatCode(): Promise<void> {
    await this.formatButton.click();
  }

  async createPaste(pasteData: {
    title: string;
    content: string;
    language?: string;
    customUrl?: string;
    isPublic?: boolean;
    expiration?: string;
    encrypted?: boolean;
    password?: string;
  }): Promise<void> {
    await this.fillTitle(pasteData.title);
    await this.fillContent(pasteData.content);
    
    if (pasteData.language) {
      await this.selectLanguage(pasteData.language);
    }
    
    if (pasteData.customUrl) {
      await this.fillCustomUrl(pasteData.customUrl);
    }
    
    if (pasteData.isPublic !== undefined) {
      await this.setPublic(pasteData.isPublic);
    }
    
    if (pasteData.expiration) {
      await this.setExpiration(pasteData.expiration);
    }
    
    if (pasteData.encrypted) {
      await this.setEncryption(pasteData.encrypted, pasteData.password);
    }
    
    await this.submitForm();
  }

  async createPasteAndWaitForRedirect(pasteData: {
    title: string;
    content: string;
    language?: string;
    customUrl?: string;
    isPublic?: boolean;
  }): Promise<string> {
    await this.createPaste(pasteData);
    
    // Wait for redirect to paste view page
    await this.page.waitForURL(/\/paste\/\w+/);
    
    // Extract paste ID from URL
    const url = this.page.url();
    const match = url.match(/\/paste\/(\w+)/);
    return match ? match[1] : '';
  }

  // Page assertions
  async expectToBeOnCreatePage(): Promise<void> {
    await expect(this.page).toHaveURL('/create');
    await expect(this.pageTitle).toContainText(/create/i);
  }

  async expectCreateForm(): Promise<void> {
    await expect(this.createForm).toBeVisible();
    await expect(this.titleInput).toBeVisible();
    await expect(this.contentEditor).toBeVisible();
    await expect(this.submitButton).toBeVisible();
  }

  async expectTitleError(message: string): Promise<void> {
    await expect(this.titleError).toBeVisible();
    await expect(this.titleError).toContainText(message);
  }

  async expectContentError(message: string): Promise<void> {
    await expect(this.contentError).toBeVisible();
    await expect(this.contentError).toContainText(message);
  }

  async expectCustomUrlError(message: string): Promise<void> {
    await expect(this.customUrlError).toBeVisible();
    await expect(this.customUrlError).toContainText(message);
  }

  async expectCustomUrlAvailable(): Promise<void> {
    await expect(this.customUrlAvailability).toBeVisible();
    await expect(this.customUrlAvailability).toContainText(/available|valid/i);
  }

  async expectCustomUrlUnavailable(): Promise<void> {
    await expect(this.customUrlAvailability).toBeVisible();
    await expect(this.customUrlAvailability).toContainText(/unavailable|taken|exists/i);
  }

  async expectValidationErrors(): Promise<void> {
    await expect(this.validationErrors.first()).toBeVisible();
  }

  async expectNoValidationErrors(): Promise<void> {
    await expect(this.validationErrors).toHaveCount(0);
  }

  async expectCreateSuccess(): Promise<void> {
    // Should be redirected to paste view page
    await expect(this.page).toHaveURL(/\/paste\/\w+/);
  }

  async expectCreateFailure(errorMessage?: string): Promise<void> {
    // Should still be on create page
    await expect(this.page).toHaveURL('/create');
    
    if (errorMessage) {
      await this.expectErrorMessage(errorMessage);
    } else {
      // Should show some error indication
      await expect(this.errorMessage.or(this.validationErrors.first())).toBeVisible();
    }
  }

  async expectPreviewVisible(): Promise<void> {
    await expect(this.previewPanel).toBeVisible();
  }

  async expectLanguageSelected(language: string): Promise<void> {
    await expect(this.languageSelect).toHaveValue(language);
  }

  async expectEncryptionEnabled(): Promise<void> {
    await expect(this.encryptionCheckbox).toBeChecked();
    await expect(this.passwordInput).toBeVisible();
  }

  async expectEncryptionDisabled(): Promise<void> {
    await expect(this.encryptionCheckbox).not.toBeChecked();
    await expect(this.passwordInput).not.toBeVisible();
  }

  // Form validation helpers
  async expectRequiredFieldValidation(): Promise<void> {
    // Try to submit empty form
    await this.submitForm();
    
    // Should show validation errors
    await this.expectValidationErrors();
  }

  async expectTitleRequired(): Promise<void> {
    await this.expectTitleError(/required|empty/i);
  }

  async expectContentRequired(): Promise<void> {
    await this.expectContentError(/required|empty/i);
  }

  // Utility methods
  async clearForm(): Promise<void> {
    await this.titleInput.clear();
    await this.fillContent('');
    await this.customUrlInput.clear();
  }

  async isFormValid(): Promise<boolean> {
    const title = await this.titleInput.inputValue();
    const content = await this.getContentValue();
    
    return title.length > 0 && content.length > 0;
  }

  async getContentValue(): Promise<string> {
    if (await this.page.locator('.monaco-editor').isVisible()) {
      // For Monaco editor, we need to get the value differently
      return await this.page.evaluate(() => {
        const editor = (window as any).monaco?.editor?.getModels()?.[0];
        return editor ? editor.getValue() : '';
      });
    } else {
      return await this.contentEditor.inputValue();
    }
  }

  async getFormData(): Promise<{
    title: string;
    content: string;
    language: string;
    customUrl: string;
    isPublic: boolean;
    expiration: string;
  }> {
    const title = await this.titleInput.inputValue();
    const content = await this.getContentValue();
    const language = await this.languageSelect.inputValue();
    const customUrl = await this.customUrlInput.inputValue();
    const isPublic = await this.isPublicCheckbox.isChecked();
    const expiration = await this.expirationSelect.inputValue();
    
    return { title, content, language, customUrl, isPublic, expiration };
  }

  async waitForFormSubmission(): Promise<void> {
    // Wait for form submission to complete (either success or error)
    await Promise.race([
      this.page.waitForURL(url => !url.includes('/create')),
      this.errorMessage.waitFor(),
      this.validationErrors.first().waitFor()
    ]);
  }
}
