package services

import (
	"errors"
	"strings"
	"time"

	"enhanced-pastebin/internal/models"
	"enhanced-pastebin/internal/repositories"

	"github.com/google/uuid"
)

type VersionService interface {
	CreateVersion(req *models.CreateVersionRequest, userID *string) (*models.PasteVersion, error)
	GetVersions(pasteID string, limit, offset int) ([]*models.PasteVersion, error)
	GetVersion(id string) (*models.PasteVersion, error)
	GetLatestVersion(pasteID string) (*models.PasteVersion, error)
	GetVersionCount(pasteID string) (int, error)
	CompareVersions(version1ID, version2ID string) (*VersionComparison, error)
	CreateVersionFromPasteUpdate(pasteID, oldContent, newContent string, userID *string) (*models.PasteVersion, error)
}

type VersionComparison struct {
	Version1    *models.PasteVersion `json:"version1"`
	Version2    *models.PasteVersion `json:"version2"`
	Differences []DiffLine           `json:"differences"`
}

type DiffLine struct {
	Type    string `json:"type"`    // "added", "removed", "unchanged"
	LineNum int    `json:"line_num"`
	Content string `json:"content"`
}

type versionService struct {
	versionRepo repositories.VersionRepository
	pasteRepo   repositories.PasteRepository
}

func NewVersionService(versionRepo repositories.VersionRepository, pasteRepo repositories.PasteRepository) VersionService {
	return &versionService{
		versionRepo: versionRepo,
		pasteRepo:   pasteRepo,
	}
}

func (s *versionService) CreateVersion(req *models.CreateVersionRequest, userID *string) (*models.PasteVersion, error) {
	// Get next version number
	versionNumber, err := s.versionRepo.GetNextVersionNumber(req.PasteID)
	if err != nil {
		return nil, err
	}

	version := &models.PasteVersion{
		ID:            uuid.New().String(),
		PasteID:       req.PasteID,
		VersionNumber: versionNumber,
		Content:       req.Content,
		CreatedAt:     time.Now(),
		CreatedBy:     userID,
	}

	if err := s.versionRepo.Create(version); err != nil {
		return nil, err
	}

	return s.versionRepo.GetByID(version.ID)
}

func (s *versionService) GetVersions(pasteID string, limit, offset int) ([]*models.PasteVersion, error) {
	return s.versionRepo.GetByPasteID(pasteID, limit, offset)
}

func (s *versionService) GetVersion(id string) (*models.PasteVersion, error) {
	return s.versionRepo.GetByID(id)
}

func (s *versionService) GetLatestVersion(pasteID string) (*models.PasteVersion, error) {
	return s.versionRepo.GetLatestVersion(pasteID)
}

func (s *versionService) GetVersionCount(pasteID string) (int, error) {
	return s.versionRepo.GetVersionCount(pasteID)
}

func (s *versionService) CreateVersionFromPasteUpdate(pasteID, oldContent, newContent string, userID *string) (*models.PasteVersion, error) {
	// Only create a version if content actually changed
	if oldContent == newContent {
		return nil, nil
	}

	req := &models.CreateVersionRequest{
		PasteID: pasteID,
		Content: oldContent, // Store the old content as a version
	}

	return s.CreateVersion(req, userID)
}

func (s *versionService) CompareVersions(version1ID, version2ID string) (*VersionComparison, error) {
	version1, err := s.versionRepo.GetByID(version1ID)
	if err != nil {
		return nil, err
	}
	if version1 == nil {
		return nil, errors.New("version 1 not found")
	}

	version2, err := s.versionRepo.GetByID(version2ID)
	if err != nil {
		return nil, err
	}
	if version2 == nil {
		return nil, errors.New("version 2 not found")
	}

	// Generate diff
	differences := s.generateDiff(version1.Content, version2.Content)

	return &VersionComparison{
		Version1:    version1,
		Version2:    version2,
		Differences: differences,
	}, nil
}

func (s *versionService) generateDiff(content1, content2 string) []DiffLine {
	lines1 := strings.Split(content1, "\n")
	lines2 := strings.Split(content2, "\n")

	var differences []DiffLine
	
	// Simple line-by-line diff implementation
	// In a production system, you'd want to use a more sophisticated diff algorithm
	maxLines := len(lines1)
	if len(lines2) > maxLines {
		maxLines = len(lines2)
	}

	for i := 0; i < maxLines; i++ {
		var line1, line2 string
		
		if i < len(lines1) {
			line1 = lines1[i]
		}
		if i < len(lines2) {
			line2 = lines2[i]
		}

		if line1 == line2 {
			differences = append(differences, DiffLine{
				Type:    "unchanged",
				LineNum: i + 1,
				Content: line1,
			})
		} else {
			if line1 != "" {
				differences = append(differences, DiffLine{
					Type:    "removed",
					LineNum: i + 1,
					Content: line1,
				})
			}
			if line2 != "" {
				differences = append(differences, DiffLine{
					Type:    "added",
					LineNum: i + 1,
					Content: line2,
				})
			}
		}
	}

	return differences
}
