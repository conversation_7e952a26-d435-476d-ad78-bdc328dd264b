package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"enhanced-pastebin/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockPasteService is a mock implementation of PasteService
type MockPasteService struct {
	mock.Mock
}

// MockDependencyService is a mock implementation of DependencyService
type MockDependencyService struct {
	mock.Mock
}

func (m *MockDependencyService) ExtractDependencies(content, language string) (*models.DependencyInfo, error) {
	args := m.Called(content, language)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.DependencyInfo), args.Error(1)
}

func (m *MockDependencyService) ExtractDependenciesWithSecurity(ctx context.Context, content, language string) (*models.DependencyInfo, error) {
	args := m.Called(ctx, content, language)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.DependencyInfo), args.Error(1)
}

// MockSecurityService is a mock implementation of SecurityService
type MockSecurityService struct {
	mock.Mock
}

func (m *MockSecurityService) ScanDependencies(ctx context.Context, dependencies []models.Dependency) ([]models.Dependency, error) {
	args := m.Called(ctx, dependencies)
	return args.Get(0).([]models.Dependency), args.Error(1)
}

func (m *MockSecurityService) ScanNPMPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error) {
	args := m.Called(ctx, packageName, version)
	return args.Get(0).(*models.DependencySecurityInfo), args.Error(1)
}

func (m *MockSecurityService) ScanPyPIPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error) {
	args := m.Called(ctx, packageName, version)
	return args.Get(0).(*models.DependencySecurityInfo), args.Error(1)
}

func (m *MockSecurityService) ScanGoPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error) {
	args := m.Called(ctx, packageName, version)
	return args.Get(0).(*models.DependencySecurityInfo), args.Error(1)
}

func (m *MockSecurityService) CalculateSecurityScore(vulnerabilities []models.SecurityVulnerability) int {
	args := m.Called(vulnerabilities)
	return args.Int(0)
}

func (m *MockPasteService) CreatePaste(req *models.CreatePasteRequest, userID *string) (*models.Paste, error) {
	args := m.Called(req, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Paste), args.Error(1)
}

func (m *MockPasteService) GetPaste(id string) (*models.Paste, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Paste), args.Error(1)
}

func (m *MockPasteService) GetPasteByCustomURL(customURL string) (*models.Paste, error) {
	args := m.Called(customURL)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Paste), args.Error(1)
}

func (m *MockPasteService) UpdatePaste(id string, req *models.UpdatePasteRequest, userID string) (*models.Paste, error) {
	args := m.Called(id, req, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Paste), args.Error(1)
}

func (m *MockPasteService) DeletePaste(id string, userID string) error {
	args := m.Called(id, userID)
	return args.Error(0)
}

func (m *MockPasteService) GetUserPastes(userID string, limit, offset int) ([]*models.Paste, error) {
	args := m.Called(userID, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.Paste), args.Error(1)
}

func (m *MockPasteService) CheckCustomURLAvailability(customURL string) (bool, error) {
	args := m.Called(customURL)
	return args.Bool(0), args.Error(1)
}

func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	return gin.New()
}

func TestPasteHandler_CreatePaste(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    models.CreatePasteRequest
		mockSetup      func(*MockPasteService)
		expectedStatus int
	}{
		{
			name: "successful paste creation",
			requestBody: models.CreatePasteRequest{
				Title:    "Test Paste",
				Content:  "Hello, World!",
				Language: "text",
			},
			mockSetup: func(m *MockPasteService) {
				paste := &models.Paste{
					ID:        "test-id",
					Title:     "Test Paste",
					Content:   "Hello, World!",
					Language:  "text",
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}
				m.On("CreatePaste", mock.AnythingOfType("*models.CreatePasteRequest"), mock.AnythingOfType("*string")).Return(paste, nil)
			},
			expectedStatus: http.StatusCreated,
		},
		{
			name: "service error",
			requestBody: models.CreatePasteRequest{
				Title:    "Test Paste",
				Content:  "Hello, World!",
				Language: "text",
			},
			mockSetup: func(m *MockPasteService) {
				m.On("CreatePaste", mock.AnythingOfType("*models.CreatePasteRequest"), mock.AnythingOfType("*string")).Return(nil, errors.New("service error"))
			},
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := new(MockPasteService)
			tt.mockSetup(mockService)

			mockDependencyService := &MockDependencyService{}
			mockSecurityService := &MockSecurityService{}
			handler := NewPasteHandler(mockService, mockDependencyService, mockSecurityService)
			router := setupTestRouter()
			router.POST("/pastes", handler.CreatePaste)

			body, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/pastes", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			mockService.AssertExpectations(t)
		})
	}
}

func TestPasteHandler_GetPaste(t *testing.T) {
	tests := []struct {
		name           string
		pasteID        string
		mockSetup      func(*MockPasteService)
		expectedStatus int
	}{
		{
			name:    "successful paste retrieval",
			pasteID: "test-id",
			mockSetup: func(m *MockPasteService) {
				paste := &models.Paste{
					ID:        "test-id",
					Title:     "Test Paste",
					Content:   "Hello, World!",
					Language:  "text",
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}
				m.On("GetPaste", "test-id").Return(paste, nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:    "paste not found",
			pasteID: "nonexistent",
			mockSetup: func(m *MockPasteService) {
				m.On("GetPaste", "nonexistent").Return(nil, errors.New("paste not found"))
			},
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := new(MockPasteService)
			tt.mockSetup(mockService)

			mockDependencyService := &MockDependencyService{}
			mockSecurityService := &MockSecurityService{}
			handler := NewPasteHandler(mockService, mockDependencyService, mockSecurityService)
			router := setupTestRouter()
			router.GET("/pastes/:id", handler.GetPaste)

			req := httptest.NewRequest(http.MethodGet, "/pastes/"+tt.pasteID, nil)
			w := httptest.NewRecorder()

			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			mockService.AssertExpectations(t)
		})
	}
}

func stringPtr(s string) *string {
	return &s
}

func TestPasteHandler_CheckCustomURLAvailability(t *testing.T) {
	tests := []struct {
		name           string
		url            string
		mockSetup      func(*MockPasteService)
		expectedStatus int
		expectedBody   map[string]interface{}
	}{
		{
			name: "Available URL",
			url:  "my-custom-url",
			mockSetup: func(m *MockPasteService) {
				m.On("CheckCustomURLAvailability", "my-custom-url").Return(true, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   map[string]interface{}{"available": true},
		},
		{
			name: "Unavailable URL",
			url:  "taken-url",
			mockSetup: func(m *MockPasteService) {
				m.On("CheckCustomURLAvailability", "taken-url").Return(false, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   map[string]interface{}{"available": false},
		},
		{
			name: "Invalid URL",
			url:  "ab",
			mockSetup: func(m *MockPasteService) {
				m.On("CheckCustomURLAvailability", "ab").Return(false, errors.New("custom URL must be at least 3 characters"))
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   map[string]interface{}{"available": false, "error": "custom URL must be at least 3 characters"},
		},
		{
			name:           "Missing URL parameter",
			url:            "",
			mockSetup:      func(m *MockPasteService) {},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   map[string]interface{}{"error": "url parameter is required"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := new(MockPasteService)
			tt.mockSetup(mockService)

			mockDependencyService := &MockDependencyService{}
			mockSecurityService := &MockSecurityService{}
			handler := NewPasteHandler(mockService, mockDependencyService, mockSecurityService)
			router := setupTestRouter()
			router.GET("/check-url", handler.CheckCustomURLAvailability)

			req := httptest.NewRequest("GET", "/check-url?url="+tt.url, nil)
			w := httptest.NewRecorder()

			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			for key, expectedValue := range tt.expectedBody {
				assert.Equal(t, expectedValue, response[key])
			}

			mockService.AssertExpectations(t)
		})
	}
}

func TestPasteHandler_GetPasteByCustomURL(t *testing.T) {
	tests := []struct {
		name           string
		customURL      string
		mockSetup      func(*MockPasteService)
		expectedStatus int
	}{
		{
			name:      "Valid custom URL",
			customURL: "my-custom-paste",
			mockSetup: func(m *MockPasteService) {
				customURL := "my-custom-paste"
				paste := &models.Paste{
					ID:        "test-id",
					CustomURL: &customURL,
					Title:     "Test Paste",
					Content:   "Test content",
					Language:  "text",
				}
				m.On("GetPasteByCustomURL", "my-custom-paste").Return(paste, nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:      "Non-existent custom URL",
			customURL: "non-existent",
			mockSetup: func(m *MockPasteService) {
				m.On("GetPasteByCustomURL", "non-existent").Return(nil, errors.New("paste not found"))
			},
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := new(MockPasteService)
			tt.mockSetup(mockService)

			mockDependencyService := &MockDependencyService{}
			mockSecurityService := &MockSecurityService{}
			handler := NewPasteHandler(mockService, mockDependencyService, mockSecurityService)
			router := setupTestRouter()
			router.GET("/p/:customUrl", handler.GetPasteByCustomURL)

			req := httptest.NewRequest("GET", "/p/"+tt.customURL, nil)
			w := httptest.NewRecorder()

			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			mockService.AssertExpectations(t)
		})
	}
}

func TestPasteHandler_GetPasteDependencies(t *testing.T) {
	tests := []struct {
		name           string
		pasteID        string
		mockSetup      func(*MockPasteService, *MockDependencyService)
		expectedStatus int
	}{
		{
			name:    "successful dependency extraction",
			pasteID: "test-id",
			mockSetup: func(ps *MockPasteService, ds *MockDependencyService) {
				paste := &models.Paste{
					ID:       "test-id",
					Title:    "Test Paste",
					Content:  "import React from 'react';\nimport axios from 'axios';",
					Language: "javascript",
				}
				ps.On("GetPaste", "test-id").Return(paste, nil)
				
				dependencies := &models.DependencyInfo{
					Dependencies: []models.Dependency{
						{
							Name:        "react",
							Type:        "import",
							Language:    "javascript",
							Registry:    "npm",
							RegistryURL: "https://www.npmjs.com/package/react",
							InstallCmd:  "npm install react",
						},
						{
							Name:        "axios",
							Type:        "import",
							Language:    "javascript",
							Registry:    "npm",
							RegistryURL: "https://www.npmjs.com/package/axios",
							InstallCmd:  "npm install axios",
						},
					},
					Language:   "javascript",
					TotalCount: 2,
				}
				ds.On("ExtractDependencies", paste.Content, paste.Language).Return(dependencies, nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:    "paste not found",
			pasteID: "nonexistent",
			mockSetup: func(ps *MockPasteService, ds *MockDependencyService) {
				ps.On("GetPaste", "nonexistent").Return(nil, errors.New("paste not found"))
			},
			expectedStatus: http.StatusNotFound,
		},
		{
			name:    "dependency extraction error",
			pasteID: "test-id",
			mockSetup: func(ps *MockPasteService, ds *MockDependencyService) {
				paste := &models.Paste{
					ID:       "test-id",
					Title:    "Test Paste",
					Content:  "some content",
					Language: "javascript",
				}
				ps.On("GetPaste", "test-id").Return(paste, nil)
				ds.On("ExtractDependencies", paste.Content, paste.Language).Return(nil, errors.New("extraction failed"))
			},
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPasteService := new(MockPasteService)
			mockDependencyService := new(MockDependencyService)
			mockSecurityService := new(MockSecurityService)
			tt.mockSetup(mockPasteService, mockDependencyService)

			handler := NewPasteHandler(mockPasteService, mockDependencyService, mockSecurityService)
			router := setupTestRouter()
			router.GET("/pastes/:id/dependencies", handler.GetPasteDependencies)

			req := httptest.NewRequest(http.MethodGet, "/pastes/"+tt.pasteID+"/dependencies", nil)
			w := httptest.NewRecorder()

			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			mockPasteService.AssertExpectations(t)
			mockDependencyService.AssertExpectations(t)
		})
	}
}