package handlers

import (
	"net/http"
	"strconv"

	"enhanced-pastebin/internal/services"

	"github.com/gin-gonic/gin"
)

type ExpirationHandler struct {
	cleanupService services.CleanupService
}

func NewExpirationHandler(cleanupService services.CleanupService) *ExpirationHandler {
	return &ExpirationHandler{
		cleanupService: cleanupService,
	}
}

func (h *ExpirationHandler) GetExpiringPastes(c *gin.Context) {
	// Parse hours parameter (default to 24 hours)
	hoursStr := c.Default<PERSON>uery("hours", "24")
	hours, err := strconv.Atoi(hoursStr)
	if err != nil || hours <= 0 || hours > 168 { // Max 1 week
		hours = 24
	}

	expiringPastes, err := h.cleanupService.GetExpiringPastes(hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	response := gin.H{
		"expiring_pastes": expiringPastes,
		"hours":          hours,
		"count":          len(expiringPastes),
	}

	c.<PERSON>(http.StatusOK, response)
}

func (h *ExpirationHandler) TriggerCleanup(c *gin.Context) {
	// Manual cleanup trigger (admin only)
	err := h.cleanupService.CleanupExpiredPastes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Cleanup completed successfully"})
}
