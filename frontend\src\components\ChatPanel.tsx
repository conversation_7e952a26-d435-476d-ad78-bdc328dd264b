import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { MessageCircle, Send, X, Minimize2, Maximize2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { chatAPI, ChatMessage } from '@/lib/api';
import { useWebSocket } from '@/hooks/useWebSocket';

interface ChatPanelProps {
  pasteId: string;
  onLineReference?: (lineNumber: number) => void;
}

export default function ChatPanel({ pasteId, onLineReference }: ChatPanelProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // WebSocket for real-time chat
  const { isConnected, sendMessage } = useWebSocket({
    pasteId,
    autoConnect: true
  });

  // Load initial messages
  useEffect(() => {
    if (isOpen && pasteId) {
      loadMessages();
    }
  }, [isOpen, pasteId]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Handle incoming WebSocket messages
  useEffect(() => {
    const handleWebSocketMessage = (event: MessageEvent) => {
      try {
        const wsMessage = JSON.parse(event.data);
        
        if (wsMessage.type === 'chat' && wsMessage.paste_id === pasteId) {
          const chatMessage = JSON.parse(wsMessage.data);
          setMessages(prev => [...prev, chatMessage]);
        } else if (wsMessage.type === 'chat_delete' && wsMessage.paste_id === pasteId) {
          const { message_id } = JSON.parse(wsMessage.data);
          setMessages(prev => prev.filter(msg => msg.id !== message_id));
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    // Note: This is a simplified approach. In a real implementation,
    // you'd want to use the WebSocket client from useWebSocket hook
    // and handle message routing properly
    
    return () => {
      // Cleanup if needed
    };
  }, [pasteId]);

  const loadMessages = async () => {
    try {
      setIsLoading(true);
      const response = await chatAPI.getMessages(pasteId);
      setMessages(response.data.messages || []);
    } catch (error) {
      console.error('Error loading messages:', error);
      toast({
        title: "Error",
        description: "Failed to load chat messages",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const sendChatMessage = async () => {
    if (!newMessage.trim() || !user) return;

    try {
      const messageData = {
        paste_id: pasteId,
        content: newMessage.trim(),
        line_references: extractLineReferences(newMessage)
      };

      await chatAPI.createMessage(messageData);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
    }
  };

  const extractLineReferences = (content: string): number[] => {
    // Extract line references like "line 42" or "L42" from message content
    const lineRegex = /(?:line\s+|L)(\d+)/gi;
    const matches = content.matchAll(lineRegex);
    return Array.from(matches, match => parseInt(match[1], 10));
  };

  const handleLineClick = (lineNumber: number) => {
    if (onLineReference) {
      onLineReference(lineNumber);
    }
  };

  const renderMessage = (message: ChatMessage) => {
    const isOwnMessage = message.user_id === user?.id;
    
    return (
      <div
        key={message.id}
        className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} mb-3`}
      >
        <div
          className={`max-w-[80%] rounded-lg px-3 py-2 ${
            isOwnMessage
              ? 'bg-primary text-primary-foreground'
              : 'bg-muted text-muted-foreground'
          }`}
        >
          {!isOwnMessage && (
            <div className="text-xs font-medium mb-1">{message.username}</div>
          )}
          <div className="text-sm">{renderMessageContent(message.content)}</div>
          {message.line_references && message.line_references.length > 0 && (
            <div className="mt-1 flex flex-wrap gap-1">
              {message.line_references.map((lineNum, index) => (
                <button
                  key={index}
                  onClick={() => handleLineClick(lineNum)}
                  className="text-xs bg-background/20 hover:bg-background/40 rounded px-1 py-0.5 transition-colors"
                >
                  L{lineNum}
                </button>
              ))}
            </div>
          )}
          <div className="text-xs opacity-70 mt-1">
            {new Date(message.created_at).toLocaleTimeString()}
          </div>
        </div>
      </div>
    );
  };

  const renderMessageContent = (content: string) => {
    // Highlight line references in the message content
    return content.replace(
      /(?:line\s+|L)(\d+)/gi,
      '<span class="font-medium underline cursor-pointer">$&</span>'
    );
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendChatMessage();
    }
  };

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 rounded-full h-12 w-12 shadow-lg"
        size="icon"
      >
        <MessageCircle className="h-5 w-5" />
      </Button>
    );
  }

  return (
    <Card className={`fixed bottom-4 right-4 w-80 shadow-lg transition-all duration-200 ${
      isMinimized ? 'h-12' : 'h-96'
    }`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          Chat {isConnected && <span className="text-green-500">●</span>}
        </CardTitle>
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => setIsMinimized(!isMinimized)}
          >
            {isMinimized ? <Maximize2 className="h-3 w-3" /> : <Minimize2 className="h-3 w-3" />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => setIsOpen(false)}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>
      
      {!isMinimized && (
        <CardContent className="p-0 flex flex-col h-80">
          <ScrollArea className="flex-1 p-3">
            {isLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-sm text-muted-foreground">Loading messages...</div>
              </div>
            ) : messages.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-sm text-muted-foreground">No messages yet</div>
              </div>
            ) : (
              <>
                {messages.map(renderMessage)}
                <div ref={messagesEndRef} />
              </>
            )}
          </ScrollArea>
          
          {user && (
            <div className="border-t p-3">
              <div className="flex space-x-2">
                <Input
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type a message... (use 'line 42' to reference code)"
                  className="flex-1"
                />
                <Button
                  onClick={sendChatMessage}
                  disabled={!newMessage.trim()}
                  size="icon"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}
