package services

import (
	"context"
	"testing"
	"time"

	"enhanced-pastebin/internal/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSecurityService_CalculateSecurityScore(t *testing.T) {
	service := NewSecurityService()

	tests := []struct {
		name            string
		vulnerabilities []models.SecurityVulnerability
		expectedScore   int
	}{
		{
			name:            "no vulnerabilities",
			vulnerabilities: []models.SecurityVulnerability{},
			expectedScore:   100,
		},
		{
			name: "single critical vulnerability",
			vulnerabilities: []models.SecurityVulnerability{
				{
					ID:       "test-1",
					Severity: "critical",
				},
			},
			expectedScore: 70,
		},
		{
			name: "single high vulnerability",
			vulnerabilities: []models.SecurityVulnerability{
				{
					ID:       "test-1",
					Severity: "high",
				},
			},
			expectedScore: 80,
		},
		{
			name: "single moderate vulnerability",
			vulnerabilities: []models.SecurityVulnerability{
				{
					ID:       "test-1",
					Severity: "moderate",
				},
			},
			expectedScore: 90,
		},
		{
			name: "single low vulnerability",
			vulnerabilities: []models.SecurityVulnerability{
				{
					ID:       "test-1",
					Severity: "low",
				},
			},
			expectedScore: 95,
		},
		{
			name: "multiple vulnerabilities",
			vulnerabilities: []models.SecurityVulnerability{
				{
					ID:       "test-1",
					Severity: "critical",
				},
				{
					ID:       "test-2",
					Severity: "high",
				},
				{
					ID:       "test-3",
					Severity: "moderate",
				},
			},
			expectedScore: 40, // 100 - 30 - 20 - 10 = 40
		},
		{
			name: "score cannot go below zero",
			vulnerabilities: []models.SecurityVulnerability{
				{ID: "test-1", Severity: "critical"},
				{ID: "test-2", Severity: "critical"},
				{ID: "test-3", Severity: "critical"},
				{ID: "test-4", Severity: "critical"},
				{ID: "test-5", Severity: "critical"},
			},
			expectedScore: 0, // Would be -50, but clamped to 0
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			score := service.CalculateSecurityScore(tt.vulnerabilities)
			assert.Equal(t, tt.expectedScore, score)
		})
	}
}

func TestSecurityService_ScanDependencies(t *testing.T) {
	service := NewSecurityService()
	ctx := context.Background()

	dependencies := []models.Dependency{
		{
			Name:     "test-package",
			Version:  "1.0.0",
			Language: "javascript",
		},
		{
			Name:     "unknown-language-package",
			Version:  "1.0.0",
			Language: "unknown",
		},
	}

	scannedDeps, err := service.ScanDependencies(ctx, dependencies)
	require.NoError(t, err)
	require.Len(t, scannedDeps, 2)

	// Check that security info was added
	for _, dep := range scannedDeps {
		assert.NotNil(t, dep.Security)
		assert.Equal(t, dep.Name, dep.Security.PackageName)
		assert.Equal(t, dep.Version, dep.Security.Version)
		assert.NotZero(t, dep.Security.LastScanned)
	}

	// Unknown language should have safe default score
	unknownLangDep := scannedDeps[1]
	assert.Equal(t, 100, unknownLangDep.Security.SecurityScore)
	assert.False(t, unknownLangDep.Security.HasVulns)
}

func TestSecurityService_ConvertOSVToSecurityInfo(t *testing.T) {
	service := &securityService{}

	// Mock OSV response structure
	osvResponse := &struct {
		Vulns []struct {
			ID        string    `json:"id"`
			Summary   string    `json:"summary"`
			Details   string    `json:"details"`
			Aliases   []string  `json:"aliases"`
			Published time.Time `json:"published"`
			Modified  time.Time `json:"modified"`
			Affected  []struct {
				Package struct {
					Name      string `json:"name"`
					Ecosystem string `json:"ecosystem"`
				} `json:"package"`
				Ranges []struct {
					Type   string `json:"type"`
					Events []struct {
						Introduced string `json:"introduced,omitempty"`
						Fixed      string `json:"fixed,omitempty"`
					} `json:"events"`
				} `json:"ranges"`
				Versions             []string    `json:"versions,omitempty"`
				DatabaseSpecific     interface{} `json:"database_specific,omitempty"`
				EcosystemSpecific    interface{} `json:"ecosystem_specific,omitempty"`
			} `json:"affected"`
			References []struct {
				Type string `json:"type"`
				URL  string `json:"url"`
			} `json:"references"`
			Severity []struct {
				Type  string  `json:"type"`
				Score string  `json:"score"`
			} `json:"severity,omitempty"`
			DatabaseSpecific interface{} `json:"database_specific,omitempty"`
		} `json:"vulns"`
	}{
		Vulns: []struct {
			ID        string    `json:"id"`
			Summary   string    `json:"summary"`
			Details   string    `json:"details"`
			Aliases   []string  `json:"aliases"`
			Published time.Time `json:"published"`
			Modified  time.Time `json:"modified"`
			Affected  []struct {
				Package struct {
					Name      string `json:"name"`
					Ecosystem string `json:"ecosystem"`
				} `json:"package"`
				Ranges []struct {
					Type   string `json:"type"`
					Events []struct {
						Introduced string `json:"introduced,omitempty"`
						Fixed      string `json:"fixed,omitempty"`
					} `json:"events"`
				} `json:"ranges"`
				Versions             []string    `json:"versions,omitempty"`
				DatabaseSpecific     interface{} `json:"database_specific,omitempty"`
				EcosystemSpecific    interface{} `json:"ecosystem_specific,omitempty"`
			} `json:"affected"`
			References []struct {
				Type string `json:"type"`
				URL  string `json:"url"`
			} `json:"references"`
			Severity []struct {
				Type  string  `json:"type"`
				Score string  `json:"score"`
			} `json:"severity,omitempty"`
			DatabaseSpecific interface{} `json:"database_specific,omitempty"`
		}{
			{
				ID:        "GHSA-test-1234",
				Summary:   "Test vulnerability",
				Details:   "This is a test vulnerability",
				Aliases:   []string{"CVE-2023-1234"},
				Published: time.Now().Add(-24 * time.Hour),
				References: []struct {
					Type string `json:"type"`
					URL  string `json:"url"`
				}{
					{
						Type: "ADVISORY",
						URL:  "https://github.com/advisories/GHSA-test-1234",
					},
				},
				Severity: []struct {
					Type  string  `json:"type"`
					Score string  `json:"score"`
				}{
					{
						Type:  "CVSS_V3",
						Score: "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",
					},
				},
			},
		},
	}

	securityInfo := service.convertOSVToSecurityInfo("test-package", "1.0.0", osvResponse)

	assert.Equal(t, "test-package", securityInfo.PackageName)
	assert.Equal(t, "1.0.0", securityInfo.Version)
	assert.True(t, securityInfo.HasVulns)
	assert.Len(t, securityInfo.Vulnerabilities, 1)

	vuln := securityInfo.Vulnerabilities[0]
	assert.Equal(t, "GHSA-test-1234", vuln.ID)
	assert.Equal(t, "Test vulnerability", vuln.Title)
	assert.Equal(t, "This is a test vulnerability", vuln.Description)
	assert.Equal(t, "CVE-2023-1234", vuln.CVE)
	assert.Equal(t, "https://github.com/advisories/GHSA-test-1234", vuln.URL)
	assert.Equal(t, "moderate", vuln.Severity) // Default mapping for CVSS
}

func TestSecurityService_CVSSToSeverity(t *testing.T) {
	service := &securityService{}

	tests := []struct {
		name         string
		cvssVector   string
		expectedSev  string
	}{
		{
			name:         "critical score",
			cvssVector:   "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H/9.8",
			expectedSev:  "critical",
		},
		{
			name:         "high score",
			cvssVector:   "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H/7.5",
			expectedSev:  "high",
		},
		{
			name:         "moderate score",
			cvssVector:   "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H/5.0",
			expectedSev:  "moderate",
		},
		{
			name:         "low score",
			cvssVector:   "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H/2.0",
			expectedSev:  "low",
		},
		{
			name:         "unknown format",
			cvssVector:   "unknown",
			expectedSev:  "low",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			severity := service.cvssToSeverity(tt.cvssVector)
			assert.Equal(t, tt.expectedSev, severity)
		})
	}
}