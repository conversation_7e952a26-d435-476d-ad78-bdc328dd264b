import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, <PERSON>, Eye } from 'lucide-react';
import { Paste } from '@/lib/api';

interface ExpirationWarningProps {
  paste: Paste;
}

export default function ExpirationWarning({ paste }: ExpirationWarningProps) {
  const getExpirationInfo = () => {
    const now = new Date();
    let timeWarning = null;
    let viewWarning = null;

    // Check time-based expiration
    if (paste.expires_at) {
      const expiresAt = new Date(paste.expires_at);
      const timeLeft = expiresAt.getTime() - now.getTime();
      
      if (timeLeft > 0) {
        const hoursLeft = Math.floor(timeLeft / (1000 * 60 * 60));
        const daysLeft = Math.floor(hoursLeft / 24);
        
        if (daysLeft > 0) {
          timeWarning = `${daysLeft} day${daysLeft > 1 ? 's' : ''}`;
        } else if (hoursLeft > 0) {
          timeWarning = `${hoursLeft} hour${hoursLeft > 1 ? 's' : ''}`;
        } else {
          const minutesLeft = Math.floor(timeLeft / (1000 * 60));
          timeWarning = `${minutesLeft} minute${minutesLeft > 1 ? 's' : ''}`;
        }
      } else {
        timeWarning = 'Expired';
      }
    }

    // Check view-based expiration
    if (paste.max_views) {
      const viewsLeft = paste.max_views - paste.view_count;
      if (viewsLeft > 0) {
        viewWarning = `${viewsLeft} view${viewsLeft > 1 ? 's' : ''}`;
      } else {
        viewWarning = 'No views left';
      }
    }

    return { timeWarning, viewWarning };
  };

  const { timeWarning, viewWarning } = getExpirationInfo();

  // Don't show warning if paste doesn't expire or has plenty of time/views left
  const shouldShowWarning = () => {
    if (timeWarning === 'Expired' || viewWarning === 'No views left') {
      return true;
    }
    
    if (paste.expires_at) {
      const expiresAt = new Date(paste.expires_at);
      const timeLeft = expiresAt.getTime() - new Date().getTime();
      const hoursLeft = timeLeft / (1000 * 60 * 60);
      
      // Show warning if less than 24 hours left
      if (hoursLeft < 24) {
        return true;
      }
    }
    
    if (paste.max_views) {
      const viewsLeft = paste.max_views - paste.view_count;
      // Show warning if 5 or fewer views left
      if (viewsLeft <= 5) {
        return true;
      }
    }
    
    return false;
  };

  if (!shouldShowWarning()) {
    return null;
  }

  const isExpired = timeWarning === 'Expired' || viewWarning === 'No views left';
  const variant = isExpired ? 'destructive' : 'default';

  return (
    <Alert variant={variant} className="mb-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription>
        <div className="font-medium mb-1">
          {isExpired ? 'This paste has expired' : 'This paste will expire soon'}
        </div>
        <div className="text-sm space-y-1">
          {timeWarning && (
            <div className="flex items-center gap-2">
              <Clock className="h-3 w-3" />
              <span>
                {isExpired && timeWarning === 'Expired' 
                  ? 'Time limit exceeded' 
                  : `Time remaining: ${timeWarning}`}
              </span>
            </div>
          )}
          {viewWarning && (
            <div className="flex items-center gap-2">
              <Eye className="h-3 w-3" />
              <span>
                {isExpired && viewWarning === 'No views left'
                  ? 'View limit exceeded'
                  : `Views remaining: ${viewWarning}`}
              </span>
            </div>
          )}
        </div>
        {isExpired && (
          <div className="text-sm mt-2 opacity-90">
            This paste and its associated data will be automatically deleted.
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
}
