package utils

import (
	"os"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

func TestGenerateJWT(t *testing.T) {
	userID := "test-user-id"
	
	token, err := GenerateJWT(userID)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if token == "" {
		t.<PERSON>rror("Expected non-empty token")
	}

	// Verify token can be parsed
	claims, err := ValidateJWT(token)
	if err != nil {
		t.Fatalf("Token validation failed: %v", err)
	}

	if claims.UserID != userID {
		t.Errorf("Expected user ID %s, got %s", userID, claims.UserID)
	}

	// Check expiration is set (should be 24 hours from now)
	expectedExp := time.Now().Add(24 * time.Hour).Unix()
	actualExp := claims.ExpiresAt.Unix()
	
	// Allow 1 minute tolerance for test execution time
	if actualExp < expectedExp-60 || actualExp > expectedExp+60 {
		t.<PERSON>("Token expiration not set correctly. Expected around %d, got %d", expectedExp, actualExp)
	}
}

func TestValidateJWT(t *testing.T) {
	userID := "test-user-id"
	
	// Generate a token
	token, err := GenerateJWT(userID)
	if err != nil {
		t.Fatalf("Token generation failed: %v", err)
	}

	// Validate the token
	claims, err := ValidateJWT(token)
	if err != nil {
		t.Fatalf("Token validation failed: %v", err)
	}

	if claims.UserID != userID {
		t.Errorf("Expected user ID %s, got %s", userID, claims.UserID)
	}
}

func TestValidateJWT_InvalidToken(t *testing.T) {
	invalidToken := "invalid.token.here"
	
	_, err := ValidateJWT(invalidToken)
	if err == nil {
		t.Error("Expected error for invalid token, got nil")
	}
}

func TestValidateJWT_ExpiredToken(t *testing.T) {
	userID := "test-user-id"
	
	// Create an expired token manually
	claims := &Claims{
		UserID: userID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(-1 * time.Hour)), // Expired 1 hour ago
			IssuedAt:  jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		secret = "your-secret-key"
	}

	tokenString, err := token.SignedString([]byte(secret))
	if err != nil {
		t.Fatalf("Failed to create expired token: %v", err)
	}

	// Try to validate the expired token
	_, err = ValidateJWT(tokenString)
	if err == nil {
		t.Error("Expected error for expired token, got nil")
	}
}

func TestJWT_WithCustomSecret(t *testing.T) {
	// Set custom secret
	originalSecret := os.Getenv("JWT_SECRET")
	os.Setenv("JWT_SECRET", "custom-test-secret")
	defer func() {
		if originalSecret != "" {
			os.Setenv("JWT_SECRET", originalSecret)
		} else {
			os.Unsetenv("JWT_SECRET")
		}
	}()

	userID := "test-user-id"
	
	// Generate token with custom secret
	token, err := GenerateJWT(userID)
	if err != nil {
		t.Fatalf("Token generation failed: %v", err)
	}

	// Validate token with same secret
	claims, err := ValidateJWT(token)
	if err != nil {
		t.Fatalf("Token validation failed: %v", err)
	}

	if claims.UserID != userID {
		t.Errorf("Expected user ID %s, got %s", userID, claims.UserID)
	}
}