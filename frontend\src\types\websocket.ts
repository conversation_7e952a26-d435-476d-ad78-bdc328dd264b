export type MessageType = 'join' | 'leave' | 'presence' | 'broadcast' | 'error' | 'operation' | 'cursor' | 'selection' | 'sync' | 'ack' | 'conflict' | 'document_state';

export interface WebSocketMessage {
  type: MessageType;
  paste_id?: string;
  user_id?: string;
  username?: string;
  data?: any;
  timestamp: string;
}

export interface ActiveUser {
  user_id: string;
  username: string;
  joined_at: string;
  last_seen: string;
}

export interface PresenceData {
  active_users: ActiveUser[];
  user_count: number;
}

export interface WebSocketClientOptions {
  url: string;
  token?: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

// Collaborative editing types
export type OperationType = 'insert' | 'delete' | 'retain';

export interface TextOperation {
  type: OperationType;
  position: number;
  content?: string; // For insert operations
  length?: number;  // For delete/retain operations
}

export interface Operation {
  id: string;
  paste_id: string;
  user_id: string;
  username: string;
  operations: TextOperation[];
  timestamp: string;
  version: number;
}

export interface CursorPosition {
  line: number;
  column: number;
  offset: number;
}

export interface Selection {
  start: CursorPosition;
  end: CursorPosition;
}

export interface CollaboratorState {
  user_id: string;
  username: string;
  color: string;
  cursor: CursorPosition;
  selection?: Selection;
  last_seen: string;
}

export interface DocumentState {
  paste_id: string;
  content: string;
  version: number;
  collaborators: CollaboratorState[];
  last_modified: string;
}

export interface OperationResult {
  success: boolean;
  new_content?: string;
  new_version?: number;
  transformed_operation?: Operation;
  error?: string;
}

export interface WebSocketEventHandlers {
  onConnect?: () => void;
  onDisconnect?: () => void;
  onMessage?: (message: WebSocketMessage) => void;
  onPresenceUpdate?: (presence: PresenceData) => void;
  onUserJoin?: (user: ActiveUser) => void;
  onUserLeave?: (userId: string) => void;
  onError?: (error: Event) => void;
  onReconnect?: (attempt: number) => void;
  onOperation?: (operation: Operation) => void;
  onCursorUpdate?: (userId: string, cursor: CursorPosition) => void;
  onSelectionUpdate?: (userId: string, selection: Selection) => void;
  onDocumentState?: (state: DocumentState) => void;
  onOperationAck?: (result: OperationResult) => void;
}