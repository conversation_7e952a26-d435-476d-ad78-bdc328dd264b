import { Page, expect } from '@playwright/test';
import { TestEnvironment, DEFAULT_TEST_CONFIG } from './test-environment';
import { DatabaseManager, DEFAULT_DB_CONFIG } from './database';
import { TestApiClient, TestUser, TestPaste } from './api-client';

export class TestHelpers {
  public environment: TestEnvironment;
  public database: DatabaseManager;
  public api: TestApiClient;

  constructor() {
    this.environment = new TestEnvironment(DEFAULT_TEST_CONFIG);
    this.database = new DatabaseManager(DEFAULT_DB_CONFIG);
    this.api = new TestApiClient(DEFAULT_TEST_CONFIG.backendUrl);
  }

  // Navigation helpers
  async navigateToHome(page: Page): Promise<void> {
    await page.goto('/');
    await expect(page).toHaveTitle(/Enhanced Pastebin/);
  }

  async navigateToLogin(page: Page): Promise<void> {
    await page.goto('/login');
    await expect(page.locator('h1')).toContainText('Login');
  }

  async navigateToRegister(page: Page): Promise<void> {
    await page.goto('/register');
    await expect(page.locator('h1')).toContainText('Register');
  }

  async navigateToCreatePaste(page: Page): Promise<void> {
    await page.goto('/create');
    await expect(page.locator('h1')).toContainText('Create');
  }

  async navigateToPaste(page: Page, pasteId: string): Promise<void> {
    await page.goto(`/paste/${pasteId}`);
    await page.waitForLoadState('networkidle');
  }

  async navigateToProfile(page: Page): Promise<void> {
    await page.goto('/profile');
    await expect(page.locator('h1')).toContainText('Profile');
  }

  // Authentication helpers
  async loginUser(page: Page, username: string, password: string): Promise<void> {
    await this.navigateToLogin(page);
    
    await page.fill('input[name="username"]', username);
    await page.fill('input[name="password"]', password);
    await page.click('button[type="submit"]');
    
    // Wait for successful login (redirect to home or dashboard)
    await page.waitForURL(/\/(|dashboard|profile)/);
  }

  async registerUser(page: Page, user: { username: string; email: string; password: string }): Promise<void> {
    await this.navigateToRegister(page);
    
    await page.fill('input[name="username"]', user.username);
    await page.fill('input[name="email"]', user.email);
    await page.fill('input[name="password"]', user.password);
    await page.click('button[type="submit"]');
    
    // Wait for successful registration
    await page.waitForURL(/\/(|login)/);
  }

  async logoutUser(page: Page): Promise<void> {
    // Look for logout button/link and click it
    const logoutButton = page.locator('button:has-text("Logout"), a:has-text("Logout")').first();
    if (await logoutButton.isVisible()) {
      await logoutButton.click();
    }
    
    // Wait for redirect to home or login page
    await page.waitForURL(/\/(|login)/);
  }

  async loginAsTestUser(page: Page, userOverrides: Partial<TestUser> = {}): Promise<TestUser> {
    const user = await this.api.createTestUser(userOverrides);
    await this.loginUser(page, user.username, user.password);
    return user;
  }

  // Paste helpers
  async createPasteViaUI(page: Page, paste: {
    title: string;
    content: string;
    language?: string;
    customUrl?: string;
    isPublic?: boolean;
  }): Promise<void> {
    await this.navigateToCreatePaste(page);
    
    await page.fill('input[name="title"]', paste.title);
    
    // Fill content in Monaco editor
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.type(paste.content);
    
    if (paste.language) {
      await page.selectOption('select[name="language"]', paste.language);
    }
    
    if (paste.customUrl) {
      await page.fill('input[name="custom_url"]', paste.customUrl);
    }
    
    if (paste.isPublic !== undefined) {
      const publicCheckbox = page.locator('input[name="is_public"]');
      if (paste.isPublic !== await publicCheckbox.isChecked()) {
        await publicCheckbox.click();
      }
    }
    
    await page.click('button[type="submit"]');
    
    // Wait for redirect to the created paste
    await page.waitForURL(/\/paste\/\w+/);
  }

  async createTestPasteViaAPI(overrides: Partial<TestPaste> = {}): Promise<TestPaste> {
    return this.api.createTestPaste(overrides);
  }

  // Assertion helpers
  async expectToBeOnHomePage(page: Page): Promise<void> {
    await expect(page).toHaveURL(/\/$/);
    await expect(page).toHaveTitle(/Enhanced Pastebin/);
  }

  async expectToBeOnLoginPage(page: Page): Promise<void> {
    await expect(page).toHaveURL(/\/login/);
    await expect(page.locator('h1')).toContainText('Login');
  }

  async expectToBeOnPastePage(page: Page, pasteId?: string): Promise<void> {
    if (pasteId) {
      await expect(page).toHaveURL(new RegExp(`/paste/${pasteId}`));
    } else {
      await expect(page).toHaveURL(/\/paste\/\w+/);
    }
  }

  async expectPasteContent(page: Page, expectedContent: string): Promise<void> {
    const codeContent = page.locator('.monaco-editor, pre, code').first();
    await expect(codeContent).toContainText(expectedContent);
  }

  async expectUserToBeLoggedIn(page: Page, username?: string): Promise<void> {
    // Check for user indicator in navigation or header
    const userIndicator = page.locator('[data-testid="user-menu"], .user-menu, .user-avatar').first();
    await expect(userIndicator).toBeVisible();
    
    if (username) {
      await expect(userIndicator).toContainText(username);
    }
  }

  async expectUserToBeLoggedOut(page: Page): Promise<void> {
    // Check that login/register links are visible
    const loginLink = page.locator('a:has-text("Login"), button:has-text("Login")').first();
    await expect(loginLink).toBeVisible();
  }

  // Wait helpers
  async waitForApiResponse(page: Page, endpoint: string, timeout: number = 5000): Promise<void> {
    await page.waitForResponse(
      response => response.url().includes(endpoint) && response.status() === 200,
      { timeout }
    );
  }

  async waitForToast(page: Page, message?: string): Promise<void> {
    const toast = page.locator('[data-testid="toast"], .toast, .notification').first();
    await expect(toast).toBeVisible();
    
    if (message) {
      await expect(toast).toContainText(message);
    }
  }

  // Cleanup helpers
  async cleanupTestData(): Promise<void> {
    // This would typically clean up any test data created during the test
    // For now, we rely on database reset between test suites
    this.api.clearAuthToken();
  }

  // Error handling helpers
  async expectErrorMessage(page: Page, message: string): Promise<void> {
    const errorElement = page.locator('.error, .alert-error, [data-testid="error"]').first();
    await expect(errorElement).toBeVisible();
    await expect(errorElement).toContainText(message);
  }

  async expectSuccessMessage(page: Page, message: string): Promise<void> {
    const successElement = page.locator('.success, .alert-success, [data-testid="success"]').first();
    await expect(successElement).toBeVisible();
    await expect(successElement).toContainText(message);
  }
}
