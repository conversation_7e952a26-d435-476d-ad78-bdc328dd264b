import { test, expect } from '@playwright/test';
import { LoginPage, HomePage, CreatePastePage } from '../../pages';
import { TestHelpers } from '../../utils/test-helpers';

test.describe('Protected Routes', () => {
  let loginPage: LoginPage;
  let homePage: HomePage;
  let createPastePage: CreatePastePage;
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    homePage = new HomePage(page);
    createPastePage = new CreatePastePage(page);
    helpers = new TestHelpers();
  });

  test.afterEach(async () => {
    await helpers.cleanupTestData();
  });

  test('should redirect to login when accessing profile without authentication', async ({ page }) => {
    await page.goto('/profile');
    await loginPage.expectToBeOnLoginPage();
  });

  test('should redirect to login when accessing create paste without authentication', async ({ page }) => {
    await page.goto('/create');
    
    // Depending on app configuration, might redirect to login or allow anonymous paste creation
    // Adjust this test based on your app's behavior
    const currentUrl = page.url();
    if (currentUrl.includes('/login')) {
      await loginPage.expectToBeOnLoginPage();
    } else {
      await createPastePage.expectToBeOnCreatePage();
    }
  });

  test('should redirect to login when accessing user pastes without authentication', async ({ page }) => {
    await page.goto('/profile/pastes');
    await loginPage.expectToBeOnLoginPage();
  });

  test('should redirect to login when accessing edit paste without authentication', async ({ page }) => {
    // Create a paste first via API
    const paste = await helpers.api.createTestPaste();
    
    await page.goto(`/paste/${paste.id}/edit`);
    await loginPage.expectToBeOnLoginPage();
  });

  test('should allow access to protected routes after login', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);

    // Should be able to access profile
    await page.goto('/profile');
    await expect(page).toHaveURL('/profile');

    // Should be able to access create paste
    await page.goto('/create');
    await expect(page).toHaveURL('/create');

    // Should be able to access user pastes
    await page.goto('/profile/pastes');
    await expect(page).toHaveURL('/profile/pastes');
  });

  test('should redirect to intended page after login', async ({ page }) => {
    // Try to access profile while logged out
    await page.goto('/profile');
    await loginPage.expectToBeOnLoginPage();

    // Login
    const user = await helpers.api.createTestUser();
    await loginPage.loginAndWaitForRedirect(user.username, user.password, '/profile');

    // Should be redirected to the originally intended page
    await expect(page).toHaveURL('/profile');
  });

  test('should handle multiple redirect attempts', async ({ page }) => {
    // Try to access multiple protected pages
    await page.goto('/profile');
    await loginPage.expectToBeOnLoginPage();

    await page.goto('/create');
    // Should still be on login page or redirect to login
    const currentUrl = page.url();
    expect(currentUrl).toMatch(/\/(login|create)/);

    // Login
    const user = await helpers.api.createTestUser();
    await loginPage.login(user.username, user.password);

    // Should be redirected to one of the intended pages
    await page.waitForURL(url => !url.includes('/login'));
    const finalUrl = page.url();
    expect(finalUrl).toMatch(/\/(profile|create|dashboard)/);
  });

  test('should preserve query parameters in redirect', async ({ page }) => {
    // Try to access profile with query parameters
    await page.goto('/profile?tab=settings&view=detailed');
    await loginPage.expectToBeOnLoginPage();

    // Login
    const user = await helpers.api.createTestUser();
    await loginPage.login(user.username, user.password);

    // Should be redirected with query parameters preserved
    await expect(page).toHaveURL('/profile?tab=settings&view=detailed');
  });

  test('should handle expired session on protected routes', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);
    await page.goto('/profile');

    // Simulate expired token
    await page.route('**/api/v1/**', route => {
      if (route.request().headers()['authorization']) {
        route.fulfill({ 
          status: 401, 
          body: JSON.stringify({ error: 'Token expired' })
        });
      } else {
        route.continue();
      }
    });

    // Try to navigate to another protected route
    await page.goto('/profile/pastes');

    // Should be redirected to login
    await loginPage.expectToBeOnLoginPage();
  });

  test('should handle insufficient permissions', async ({ page }) => {
    // Login as regular user
    const user = await helpers.loginAsTestUser(page);

    // Try to access admin-only route (if exists)
    await page.goto('/admin');

    // Should show 403 error or redirect to unauthorized page
    const currentUrl = page.url();
    if (currentUrl.includes('/admin')) {
      // Check for 403 error message
      await expect(page.locator('h1, .error')).toContainText(/403|unauthorized|forbidden/i);
    } else {
      // Should be redirected away from admin page
      expect(currentUrl).not.toContain('/admin');
    }
  });

  test('should allow access to public routes without authentication', async ({ page }) => {
    // Public routes should be accessible without login
    await page.goto('/');
    await homePage.expectToBeOnHomePage();

    await page.goto('/login');
    await loginPage.expectToBeOnLoginPage();

    await page.goto('/register');
    await expect(page).toHaveURL('/register');

    // Public paste view should be accessible
    const paste = await helpers.api.createTestPaste({ is_public: true });
    await page.goto(`/paste/${paste.id}`);
    await expect(page).toHaveURL(`/paste/${paste.id}`);
  });

  test('should handle private paste access without authentication', async ({ page }) => {
    // Create a private paste
    const user = await helpers.api.loginAsTestUser();
    const paste = await helpers.api.createTestPaste({ is_public: false });

    // Try to access private paste without login
    await page.goto(`/paste/${paste.id}`);

    // Should either redirect to login or show access denied
    const currentUrl = page.url();
    if (currentUrl.includes('/login')) {
      await loginPage.expectToBeOnLoginPage();
    } else {
      await expect(page.locator('h1, .error')).toContainText(/private|access denied|not found/i);
    }
  });

  test('should handle route guards with loading states', async ({ page }) => {
    // Simulate slow authentication check
    await page.route('**/api/v1/users/profile', route => 
      new Promise(resolve => 
        setTimeout(() => resolve(route.fulfill({ status: 200, body: '{}' })), 1000)
      )
    );

    const user = await helpers.loginAsTestUser(page);
    await page.goto('/profile');

    // Should show loading state while checking authentication
    const loadingIndicator = page.locator('.loading, .spinner, [data-testid="loading"]');
    if (await loadingIndicator.isVisible()) {
      await expect(loadingIndicator).toBeVisible();
    }

    // Should eventually load the protected page
    await expect(page).toHaveURL('/profile');
  });

  test('should handle navigation between protected and public routes', async ({ page }) => {
    // Start on public route
    await homePage.visit();
    await homePage.expectToBeOnHomePage();

    // Navigate to protected route (should redirect to login)
    await page.goto('/profile');
    await loginPage.expectToBeOnLoginPage();

    // Login
    const user = await helpers.api.createTestUser();
    await loginPage.loginAndWaitForRedirect(user.username, user.password, '/profile');

    // Navigate back to public route (should work)
    await homePage.visit();
    await homePage.expectToBeOnHomePage();

    // Navigate to another protected route (should work without re-login)
    await page.goto('/create');
    await expect(page).toHaveURL('/create');
  });

  test('should handle deep linking to protected routes', async ({ page }) => {
    // Try to access a deep protected route
    await page.goto('/profile/pastes?filter=private&sort=date');
    await loginPage.expectToBeOnLoginPage();

    // Login
    const user = await helpers.api.createTestUser();
    await loginPage.login(user.username, user.password);

    // Should be redirected to the deep link with all parameters
    await expect(page).toHaveURL('/profile/pastes?filter=private&sort=date');
  });

  test('should handle browser back/forward with protected routes', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);

    // Navigate through several routes
    await homePage.visit();
    await page.goto('/profile');
    await page.goto('/create');

    // Use browser back button
    await page.goBack();
    await expect(page).toHaveURL('/profile');

    // Use browser forward button
    await page.goForward();
    await expect(page).toHaveURL('/create');

    // Should maintain authentication throughout navigation
    await loginPage.expectToBeLoggedIn(user.username);
  });
});
