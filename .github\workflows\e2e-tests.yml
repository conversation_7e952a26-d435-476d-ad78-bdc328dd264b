name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  DATABASE_URL: postgres://postgres:password@localhost:5432/enhanced_pastebin?sslmode=disable
  JWT_SECRET: test-jwt-secret-key-for-ci
  PORT: 8080
  GIN_MODE: release

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: enhanced_pastebin
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.21'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install Go dependencies
        run: go mod download

      - name: Install frontend dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Install Playwright browsers
        working-directory: ./frontend
        run: npx playwright install --with-deps

      - name: Wait for PostgreSQL
        run: |
          until pg_isready -h localhost -p 5432 -U postgres; do
            echo "Waiting for PostgreSQL..."
            sleep 2
          done

      - name: Run database migrations
        run: go run cmd/migrate/main.go up

      - name: Build frontend
        working-directory: ./frontend
        run: npm run build

      - name: Start backend server
        run: |
          go run cmd/server/main.go &
          echo $! > backend.pid
          # Wait for backend to be ready
          timeout 30 bash -c 'until curl -f http://localhost:8080/api/v1/health; do sleep 1; done'

      - name: Run Playwright tests
        working-directory: ./frontend
        run: npm run test:e2e

      - name: Stop backend server
        if: always()
        run: |
          if [ -f backend.pid ]; then
            kill $(cat backend.pid) || true
            rm backend.pid
          fi

      - name: Upload Playwright report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: frontend/playwright-report/
          retention-days: 30

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: frontend/test-results/
          retention-days: 30

  # Optional: Run tests on multiple OS
  e2e-tests-matrix:
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    runs-on: ${{ matrix.os }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.21'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Start PostgreSQL (Ubuntu/macOS)
        if: runner.os != 'Windows'
        run: |
          if [ "$RUNNER_OS" == "Linux" ]; then
            sudo systemctl start postgresql
            sudo -u postgres createdb enhanced_pastebin
          elif [ "$RUNNER_OS" == "macOS" ]; then
            brew services start postgresql
            createdb enhanced_pastebin
          fi

      - name: Start PostgreSQL (Windows)
        if: runner.os == 'Windows'
        run: |
          # Use Docker on Windows
          docker run -d --name postgres -e POSTGRES_DB=enhanced_pastebin -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=password -p 5432:5432 postgres:15

      - name: Install dependencies
        run: |
          go mod download
          cd frontend && npm ci

      - name: Install Playwright browsers
        working-directory: ./frontend
        run: npx playwright install --with-deps

      - name: Run migrations
        run: go run cmd/migrate/main.go up

      - name: Run tests (subset)
        working-directory: ./frontend
        run: npx playwright test demo.spec.ts

  # Performance testing job
  performance-tests:
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: enhanced_pastebin
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.21'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        run: |
          go mod download
          cd frontend && npm ci

      - name: Install Playwright browsers
        working-directory: ./frontend
        run: npx playwright install --with-deps

      - name: Run migrations
        run: go run cmd/migrate/main.go up

      - name: Run performance tests
        working-directory: ./frontend
        run: |
          # Run tests with performance monitoring
          npx playwright test --grep "performance|large" --reporter=json > performance-results.json

      - name: Upload performance results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-results
          path: frontend/performance-results.json
