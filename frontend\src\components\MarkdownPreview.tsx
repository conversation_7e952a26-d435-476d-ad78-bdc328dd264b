import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Eye, Code, Split } from 'lucide-react';

interface MarkdownPreviewProps {
  content: string;
  title?: string;
}

export default function MarkdownPreview({ content, title = "Markdown Preview" }: MarkdownPreviewProps) {
  const [viewMode, setViewMode] = useState<'preview' | 'source' | 'split'>('preview');

  // Simple markdown to HTML converter (basic implementation)
  const convertMarkdownToHtml = useMemo(() => {
    if (!content) return '';

    let html = content;

    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

    // Bold
    html = html.replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>');
    html = html.replace(/__(.*?)__/gim, '<strong>$1</strong>');

    // Italic
    html = html.replace(/\*(.*?)\*/gim, '<em>$1</em>');
    html = html.replace(/_(.*?)_/gim, '<em>$1</em>');

    // Code blocks
    html = html.replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>');
    
    // Inline code
    html = html.replace(/`(.*?)`/gim, '<code>$1</code>');

    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

    // Lists
    html = html.replace(/^\* (.*$)/gim, '<li>$1</li>');
    html = html.replace(/^\- (.*$)/gim, '<li>$1</li>');
    html = html.replace(/^(\d+)\. (.*$)/gim, '<li>$2</li>');

    // Wrap consecutive list items in ul/ol
    html = html.replace(/(<li>.*<\/li>)/gims, (match) => {
      if (match.includes('<li>')) {
        return `<ul>${match}</ul>`;
      }
      return match;
    });

    // Line breaks
    html = html.replace(/\n\n/gim, '</p><p>');
    html = html.replace(/\n/gim, '<br>');

    // Wrap in paragraphs
    html = `<p>${html}</p>`;

    // Clean up empty paragraphs
    html = html.replace(/<p><\/p>/gim, '');
    html = html.replace(/<p><br><\/p>/gim, '');

    // Blockquotes
    html = html.replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>');

    // Horizontal rules
    html = html.replace(/^---$/gim, '<hr>');
    html = html.replace(/^\*\*\*$/gim, '<hr>');

    return html;
  }, [content]);

  const renderPreview = () => (
    <div 
      className="prose prose-sm max-w-none dark:prose-invert"
      dangerouslySetInnerHTML={{ __html: convertMarkdownToHtml }}
      style={{
        lineHeight: '1.6',
        color: 'inherit'
      }}
    />
  );

  const renderSource = () => (
    <pre className="whitespace-pre-wrap text-sm font-mono bg-muted p-4 rounded-md overflow-auto max-h-96">
      {content}
    </pre>
  );

  const renderSplit = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <h4 className="text-sm font-medium mb-2">Source</h4>
        {renderSource()}
      </div>
      <div>
        <h4 className="text-sm font-medium mb-2">Preview</h4>
        <div className="bg-muted p-4 rounded-md overflow-auto max-h-96">
          {renderPreview()}
        </div>
      </div>
    </div>
  );

  if (!content.trim()) {
    return null;
  }

  return (
    <Card className="mt-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{title}</CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'preview' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('preview')}
            >
              <Eye className="h-4 w-4 mr-1" />
              Preview
            </Button>
            <Button
              variant={viewMode === 'source' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('source')}
            >
              <Code className="h-4 w-4 mr-1" />
              Source
            </Button>
            <Button
              variant={viewMode === 'split' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('split')}
            >
              <Split className="h-4 w-4 mr-1" />
              Split
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {viewMode === 'preview' && renderPreview()}
        {viewMode === 'source' && renderSource()}
        {viewMode === 'split' && renderSplit()}
      </CardContent>
    </Card>
  );
}
