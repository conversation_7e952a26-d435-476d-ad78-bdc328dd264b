package models

import (
	"time"
)

type PasteVersion struct {
	ID            string    `json:"id" db:"id"`
	PasteID       string    `json:"paste_id" db:"paste_id"`
	VersionNumber int       `json:"version_number" db:"version_number"`
	Content       string    `json:"content" db:"content"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
	CreatedBy     *string   `json:"created_by,omitempty" db:"created_by"`
	
	// Joined fields for display
	CreatedByUsername string `json:"created_by_username,omitempty" db:"created_by_username"`
}

type CreateVersionRequest struct {
	PasteID string `json:"paste_id" binding:"required"`
	Content string `json:"content" binding:"required"`
}

type VersionResponse struct {
	ID            string    `json:"id"`
	PasteID       string    `json:"paste_id"`
	VersionNumber int       `json:"version_number"`
	Content       string    `json:"content"`
	CreatedAt     time.Time `json:"created_at"`
	CreatedBy     *string   `json:"created_by,omitempty"`
	CreatedByUsername string `json:"created_by_username,omitempty"`
}