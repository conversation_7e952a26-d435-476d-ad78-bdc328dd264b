package handlers

import (
	"encoding/json"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"enhanced-pastebin/internal/models"
	"enhanced-pastebin/internal/services"
	websocketPkg "enhanced-pastebin/internal/websocket"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

func TestWebSocketCollaborativeEditing(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)
	
	hub := websocketPkg.NewHub()
	go hub.Run()
	
	collaborationService := services.NewCollaborationService()
	handler := NewWebSocketHandler(hub, collaborationService)
	
	router := gin.New()
	router.GET("/ws", handler.HandleWebSocket)
	
	// Create test server
	server := httptest.NewServer(router)
	defer server.Close()
	
	// Convert HTTP URL to WebSocket URL
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/ws"
	
	// Connect first client
	conn1, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		t.Fatalf("Failed to connect first client: %v", err)
	}
	defer conn1.Close()
	
	// Connect second client
	conn2, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		t.Fatalf("Failed to connect second client: %v", err)
	}
	defer conn2.Close()
	
	// Test joining a room
	pasteID := "test-paste-123"
	
	// Client 1 joins room
	joinMessage1 := models.WebSocketMessage{
		Type:      models.MessageTypeJoin,
		PasteID:   pasteID,
		Timestamp: time.Now(),
	}
	
	if err := conn1.WriteJSON(joinMessage1); err != nil {
		t.Fatalf("Failed to send join message from client 1: %v", err)
	}
	
	// Client 2 joins room
	joinMessage2 := models.WebSocketMessage{
		Type:      models.MessageTypeJoin,
		PasteID:   pasteID,
		Timestamp: time.Now(),
	}
	
	if err := conn2.WriteJSON(joinMessage2); err != nil {
		t.Fatalf("Failed to send join message from client 2: %v", err)
	}
	
	// Give some time for messages to be processed
	time.Sleep(100 * time.Millisecond)
	
	// Test sending an operation from client 1
	operation := models.Operation{
		ID:      "op-1",
		PasteID: pasteID,
		Operations: []models.TextOperation{
			{
				Type:     models.OperationTypeInsert,
				Position: 0,
				Content:  "Hello, World!",
			},
		},
		Version: 0,
	}
	
	operationData, _ := json.Marshal(operation)
	operationMessage := models.WebSocketMessage{
		Type:      models.MessageTypeOperation,
		PasteID:   pasteID,
		Data:      json.RawMessage(operationData),
		Timestamp: time.Now(),
	}
	
	if err := conn1.WriteJSON(operationMessage); err != nil {
		t.Fatalf("Failed to send operation from client 1: %v", err)
	}
	
	// Read messages from both clients (they might receive join/presence messages first)
	// We need to read until we get the expected messages or timeout
	
	// Read messages from client 1 until we get ACK or timeout
	conn1.SetReadDeadline(time.Now().Add(2 * time.Second))
	var ackReceived bool
	for !ackReceived {
		var message models.WebSocketMessage
		if err := conn1.ReadJSON(&message); err != nil {
			t.Logf("Client 1 read error or timeout: %v", err)
			break
		}
		t.Logf("Client 1 received message type: %s", message.Type)
		if message.Type == models.MessageTypeAck {
			ackReceived = true
		}
	}
	
	// Read messages from client 2 until we get operation or timeout
	conn2.SetReadDeadline(time.Now().Add(2 * time.Second))
	var operationReceived bool
	for !operationReceived {
		var message models.WebSocketMessage
		if err := conn2.ReadJSON(&message); err != nil {
			t.Logf("Client 2 read error or timeout: %v", err)
			break
		}
		t.Logf("Client 2 received message type: %s", message.Type)
		if message.Type == models.MessageTypeOperation {
			operationReceived = true
		}
	}
	
	// Test cursor update
	cursor := models.CursorPosition{
		Line:   1,
		Column: 5,
		Offset: 5,
	}
	
	cursorData, _ := json.Marshal(cursor)
	cursorMessage := models.WebSocketMessage{
		Type:      models.MessageTypeCursor,
		PasteID:   pasteID,
		Data:      json.RawMessage(cursorData),
		Timestamp: time.Now(),
	}
	
	if err := conn1.WriteJSON(cursorMessage); err != nil {
		t.Fatalf("Failed to send cursor update from client 1: %v", err)
	}
	
	// Read messages from client 2 until we get cursor update or timeout
	conn2.SetReadDeadline(time.Now().Add(2 * time.Second))
	var cursorReceived bool
	for !cursorReceived {
		var message models.WebSocketMessage
		if err := conn2.ReadJSON(&message); err != nil {
			t.Logf("Client 2 cursor read error or timeout: %v", err)
			break
		}
		t.Logf("Client 2 received cursor message type: %s", message.Type)
		if message.Type == models.MessageTypeCursor {
			cursorReceived = true
		}
	}
	
	t.Log("Collaborative editing test completed successfully")
}