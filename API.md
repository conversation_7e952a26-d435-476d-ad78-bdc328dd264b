# Enhanced Pastebin API Documentation

This document provides comprehensive documentation for the Enhanced Pastebin REST API.

## Base URL

```
http://localhost:8080/api/v1
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow a consistent format:

**Success Response:**
```json
{
  "data": { ... },
  "message": "Success message (optional)"
}
```

**Error Response:**
```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": { ... } // Optional additional details
}
```

## Status Codes

- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Unprocessable Entity
- `429` - Too Many Requests
- `500` - Internal Server Error

## Endpoints

### Authentication

#### Register User
```http
POST /users/register
```

**Request Body:**
```json
{
  "username": "string (3-50 chars, alphanumeric)",
  "email": "string (valid email)",
  "password": "string (min 6 chars)"
}
```

**Response:**
```json
{
  "id": "uuid",
  "username": "string",
  "email": "string",
  "reputation_score": 0,
  "created_at": "2023-01-01T00:00:00Z"
}
```

#### Login User
```http
POST /users/login
```

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "token": "jwt-token",
  "user": {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "reputation_score": 100,
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

### User Management

#### Get User Profile
```http
GET /users/profile
```
*Requires authentication*

**Response:**
```json
{
  "user": {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "reputation_score": 100,
    "created_at": "2023-01-01T00:00:00Z"
  },
  "stats": {
    "total_pastes": 10,
    "total_views": 150,
    "most_used_language": "javascript",
    "language_count": {
      "javascript": 5,
      "python": 3,
      "go": 2
    }
  },
  "recent_pastes": [...]
}
```

#### Get Public User Profile
```http
GET /users/{username}/public
```

**Response:**
```json
{
  "username": "string",
  "reputation_score": 100,
  "created_at": "2023-01-01T00:00:00Z",
  "last_active": "2023-01-01T00:00:00Z",
  "stats": { ... },
  "recent_pastes": [...]
}
```

#### Get User Pastes
```http
GET /users/profile/pastes?limit=20&offset=0
```
*Requires authentication*

**Query Parameters:**
- `limit` (optional): Number of pastes to return (default: 20, max: 100)
- `offset` (optional): Number of pastes to skip (default: 0)

### Paste Management

#### Create Paste
```http
POST /pastes
```

**Request Body:**
```json
{
  "title": "string (1-500 chars)",
  "content": "string (required)",
  "language": "string (optional)",
  "custom_url": "string (optional, 3-255 chars, alphanumeric)",
  "expires_at": "2023-12-31T23:59:59Z (optional)",
  "max_views": 100, // optional
  "is_encrypted": false, // optional
  "password": "string (required if is_encrypted=true)",
  "is_watermarked": false // optional
}
```

**Response:**
```json
{
  "id": "uuid",
  "custom_url": "string",
  "title": "string",
  "content": "string",
  "language": "string",
  "is_encrypted": false,
  "expires_at": "2023-12-31T23:59:59Z",
  "view_count": 0,
  "max_views": 100,
  "edit_count": 0,
  "is_watermarked": false,
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z",
  "user_id": "uuid",
  "username": "string"
}
```

#### Get Paste
```http
GET /pastes/{id}
```

**Query Parameters:**
- `password` (optional): Required for encrypted pastes

#### Get Paste by Custom URL
```http
GET /p/{customUrl}
```

**Query Parameters:**
- `password` (optional): Required for encrypted pastes

#### Update Paste
```http
PUT /pastes/{id}
```
*Requires authentication and ownership*

**Request Body:**
```json
{
  "title": "string (optional)",
  "content": "string (optional)",
  "language": "string (optional)",
  "expires_at": "2023-12-31T23:59:59Z (optional)",
  "max_views": 100, // optional
  "is_watermarked": false // optional
}
```

#### Delete Paste
```http
DELETE /pastes/{id}
```
*Requires authentication and ownership*

### Chat System

#### Send Message
```http
POST /chat/messages
```
*Requires authentication*

**Request Body:**
```json
{
  "paste_id": "uuid",
  "content": "string (1-1000 chars)",
  "line_references": [1, 2, 3] // optional array of line numbers
}
```

#### Get Messages
```http
GET /chat/pastes/{pasteId}/messages?limit=50&offset=0
```

**Query Parameters:**
- `limit` (optional): Number of messages to return (default: 50, max: 100)
- `offset` (optional): Number of messages to skip (default: 0)

#### Delete Message
```http
DELETE /chat/messages/{messageId}
```
*Requires authentication and ownership*

### Version Control

#### Get Paste Versions
```http
GET /versions/pastes/{pasteId}?limit=20&offset=0
```

#### Get Specific Version
```http
GET /versions/{versionId}
```

#### Compare Versions
```http
GET /versions/compare?version1={id1}&version2={id2}
```

**Response:**
```json
{
  "version1": { ... },
  "version2": { ... },
  "differences": [
    {
      "type": "added|removed|unchanged",
      "line_num": 1,
      "content": "string"
    }
  ]
}
```

### Audit & Security

#### Get Access Logs
```http
GET /audit/pastes/{pasteId}/logs?limit=50&offset=0
```
*Requires authentication and ownership*

#### Get User Access Logs
```http
GET /audit/user/logs?limit=50&offset=0
```
*Requires authentication*

#### Get Security Events
```http
GET /audit/security-events?limit=50&offset=0
```
*Requires authentication*

#### Get Watermarks
```http
GET /audit/pastes/{pasteId}/watermarks
```
*Requires authentication and ownership*

#### Extract Watermark
```http
POST /audit/watermark/extract
```
*Requires authentication*

**Request Body:**
```json
{
  "content": "string"
}
```

### Expiration Management

#### Get Expiring Pastes
```http
GET /expiration/expiring?hours=24
```

#### Trigger Cleanup
```http
POST /expiration/cleanup
```
*Requires authentication*

### WebSocket

#### Connect to WebSocket
```
ws://localhost:8080/api/v1/ws
```

**Authentication:**
Send JWT token as query parameter: `?token=your-jwt-token`

**Message Format:**
```json
{
  "type": "join_paste|leave_paste|paste_update|chat_message",
  "paste_id": "uuid",
  "data": { ... }
}
```

### Health Check

#### Health Status
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2023-01-01T00:00:00Z",
  "version": "1.0.0",
  "database": "connected",
  "redis": "connected"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **General endpoints**: 100 requests per minute per IP
- **Authentication endpoints**: 10 requests per minute per IP
- **Paste creation**: 20 requests per minute per user
- **Chat messages**: 30 requests per minute per user

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `AUTHENTICATION_REQUIRED` | Authentication token required |
| `INVALID_CREDENTIALS` | Invalid username/password |
| `UNAUTHORIZED` | Insufficient permissions |
| `RESOURCE_NOT_FOUND` | Requested resource not found |
| `RESOURCE_CONFLICT` | Resource already exists |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `PASTE_EXPIRED` | Paste has expired |
| `PASTE_VIEW_LIMIT_EXCEEDED` | Paste view limit reached |
| `INVALID_PASSWORD` | Invalid paste password |
| `INTERNAL_ERROR` | Internal server error |

## Examples

### Creating an Encrypted Paste

```bash
curl -X POST http://localhost:8080/api/v1/pastes \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "title": "Secret Code",
    "content": "console.log(\"secret\");",
    "language": "javascript",
    "is_encrypted": true,
    "password": "mypassword123"
  }'
```

### Viewing an Encrypted Paste

```bash
curl "http://localhost:8080/api/v1/pastes/paste-id?password=mypassword123"
```

### Real-time Collaboration

```javascript
const ws = new WebSocket('ws://localhost:8080/api/v1/ws?token=your-jwt-token');

// Join a paste for collaboration
ws.send(JSON.stringify({
  type: 'join_paste',
  paste_id: 'paste-uuid'
}));

// Send a paste update
ws.send(JSON.stringify({
  type: 'paste_update',
  paste_id: 'paste-uuid',
  data: {
    content: 'updated content',
    cursor_position: 42
  }
}));
```

## SDKs and Libraries

Official SDKs are available for:
- JavaScript/TypeScript (npm: `enhanced-pastebin-js`)
- Python (pip: `enhanced-pastebin-py`)
- Go (go get: `github.com/enhanced-pastebin/go-sdk`)

## Support

For API support, please:
1. Check this documentation
2. Review the [GitHub Issues](https://github.com/enhanced-pastebin/issues)
3. Contact <NAME_EMAIL>
