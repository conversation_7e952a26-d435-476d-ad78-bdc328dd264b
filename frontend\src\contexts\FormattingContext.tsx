import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { FormatOptions } from '@/services/codeFormatter'

interface FormattingSettings {
  autoFormatOnSave: boolean
  showLintingHints: boolean
  formatOnPaste: boolean
  formatOptions: FormatOptions
}

interface FormattingContextType {
  settings: FormattingSettings
  updateSettings: (newSettings: Partial<FormattingSettings>) => void
  updateFormatOptions: (newOptions: Partial<FormatOptions>) => void
  resetToDefaults: () => void
}

const defaultSettings: FormattingSettings = {
  autoFormatOnSave: true,
  showLintingHints: true,
  formatOnPaste: false,
  formatOptions: {
    tabWidth: 2,
    useTabs: false,
    semicolons: true,
    singleQuote: false,
    trailingComma: 'es5',
    bracketSpacing: true,
    arrowParens: 'avoid'
  }
}

const FormattingContext = createContext<FormattingContextType | undefined>(undefined)

interface FormattingProviderProps {
  children: ReactNode
}

export function FormattingProvider({ children }: FormattingProviderProps) {
  const [settings, setSettings] = useState<FormattingSettings>(() => {
    // Load settings from localStorage
    const saved = localStorage.getItem('formatting-settings')
    if (saved) {
      try {
        return { ...defaultSettings, ...JSON.parse(saved) }
      } catch (error) {
        console.warn('Failed to parse formatting settings from localStorage:', error)
      }
    }
    return defaultSettings
  })

  // Save settings to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('formatting-settings', JSON.stringify(settings))
  }, [settings])

  const updateSettings = (newSettings: Partial<FormattingSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }))
  }

  const updateFormatOptions = (newOptions: Partial<FormatOptions>) => {
    setSettings(prev => ({
      ...prev,
      formatOptions: { ...prev.formatOptions, ...newOptions }
    }))
  }

  const resetToDefaults = () => {
    setSettings(defaultSettings)
  }

  const value: FormattingContextType = {
    settings,
    updateSettings,
    updateFormatOptions,
    resetToDefaults
  }

  return (
    <FormattingContext.Provider value={value}>
      {children}
    </FormattingContext.Provider>
  )
}

export function useFormatting() {
  const context = useContext(FormattingContext)
  if (context === undefined) {
    throw new Error('useFormatting must be used within a FormattingProvider')
  }
  return context
}