import * as prettier from 'prettier'
import { js_beautify, css_beautify, html_beautify } from 'js-beautify'

export interface FormatOptions {
  tabWidth?: number
  useTabs?: boolean
  semicolons?: boolean
  singleQuote?: boolean
  trailingComma?: 'none' | 'es5' | 'all'
  bracketSpacing?: boolean
  arrowParens?: 'avoid' | 'always'
}

export interface FormatResult {
  success: boolean
  formattedCode?: string
  error?: string
}

export interface LintResult {
  errors: LintError[]
  warnings: LintWarning[]
}

export interface LintError {
  line: number
  column: number
  message: string
  severity: 'error' | 'warning'
}

export interface LintWarning {
  line: number
  column: number
  message: string
  severity: 'warning'
}

class CodeFormatterService {
  private defaultOptions: FormatOptions = {
    tabWidth: 2,
    useTabs: false,
    semicolons: true,
    singleQuote: false,
    trailingComma: 'es5',
    bracketSpacing: true,
    arrowParens: 'avoid'
  }

  /**
   * Format code based on language
   */
  async formatCode(code: string, language: string, options?: FormatOptions): Promise<FormatResult> {
    const formatOptions = { ...this.defaultOptions, ...options }

    try {
      switch (language.toLowerCase()) {
        case 'javascript':
        case 'js':
          return await this.formatJavaScript(code, formatOptions)
        
        case 'typescript':
        case 'ts':
          return await this.formatTypeScript(code, formatOptions)
        
        case 'json':
          return this.formatJSON(code, formatOptions)
        
        case 'css':
          return this.formatCSS(code, formatOptions)
        
        case 'scss':
        case 'sass':
          return await this.formatSCSS(code, formatOptions)
        
        case 'html':
          return this.formatHTML(code, formatOptions)
        
        case 'markdown':
        case 'md':
          return await this.formatMarkdown(code, formatOptions)
        
        case 'python':
        case 'py':
          return this.formatPython(code, formatOptions)
        
        case 'go':
          return this.formatGo(code, formatOptions)
        
        case 'java':
          return this.formatJava(code, formatOptions)
        
        case 'cpp':
        case 'c++':
        case 'c':
          return this.formatCpp(code, formatOptions)
        
        case 'xml':
          return this.formatXML(code, formatOptions)
        
        case 'yaml':
        case 'yml':
          return this.formatYAML(code, formatOptions)
        
        default:
          return {
            success: false,
            error: `Formatting not supported for language: ${language}`
          }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown formatting error'
      }
    }
  }

  /**
   * Basic linting for common syntax errors
   */
  lintCode(code: string, language: string): LintResult {
    const errors: LintError[] = []
    const warnings: LintWarning[] = []

    try {
      switch (language.toLowerCase()) {
        case 'javascript':
        case 'js':
        case 'typescript':
        case 'ts':
          return this.lintJavaScript(code)
        
        case 'json':
          return this.lintJSON(code)
        
        case 'python':
        case 'py':
          return this.lintPython(code)
        
        case 'go':
          return this.lintGo(code)
        
        case 'html':
          return this.lintHTML(code)
        
        case 'css':
        case 'scss':
          return this.lintCSS(code)
        
        default:
          return { errors, warnings }
      }
    } catch (error) {
      errors.push({
        line: 1,
        column: 1,
        message: error instanceof Error ? error.message : 'Linting error',
        severity: 'error'
      })
      return { errors, warnings }
    }
  }

  private async formatJavaScript(code: string, options: FormatOptions): Promise<FormatResult> {
    try {
      const formatted = await prettier.format(code, {
        parser: 'babel',
        tabWidth: options.tabWidth,
        useTabs: options.useTabs,
        semi: options.semicolons,
        singleQuote: options.singleQuote,
        trailingComma: options.trailingComma,
        bracketSpacing: options.bracketSpacing,
        arrowParens: options.arrowParens
      })
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'JavaScript formatting failed'
      }
    }
  }

  private async formatTypeScript(code: string, options: FormatOptions): Promise<FormatResult> {
    try {
      const formatted = await prettier.format(code, {
        parser: 'typescript',
        tabWidth: options.tabWidth,
        useTabs: options.useTabs,
        semi: options.semicolons,
        singleQuote: options.singleQuote,
        trailingComma: options.trailingComma,
        bracketSpacing: options.bracketSpacing,
        arrowParens: options.arrowParens
      })
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'TypeScript formatting failed'
      }
    }
  }

  private formatJSON(code: string, options: FormatOptions): FormatResult {
    try {
      const parsed = JSON.parse(code)
      const formatted = JSON.stringify(parsed, null, options.useTabs ? '\t' : ' '.repeat(options.tabWidth || 2))
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: 'Invalid JSON syntax'
      }
    }
  }

  private formatCSS(code: string, options: FormatOptions): FormatResult {
    try {
      const formatted = css_beautify(code, {
        indent_size: options.tabWidth,
        indent_char: options.useTabs ? '\t' : ' ',
        max_preserve_newlines: 2,
        preserve_newlines: true,
        end_with_newline: true
      })
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'CSS formatting failed'
      }
    }
  }

  private async formatSCSS(code: string, options: FormatOptions): Promise<FormatResult> {
    try {
      const formatted = await prettier.format(code, {
        parser: 'scss',
        tabWidth: options.tabWidth,
        useTabs: options.useTabs,
        singleQuote: options.singleQuote
      })
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'SCSS formatting failed'
      }
    }
  }

  private formatHTML(code: string, options: FormatOptions): FormatResult {
    try {
      const formatted = html_beautify(code, {
        indent_size: options.tabWidth,
        indent_char: options.useTabs ? '\t' : ' ',
        max_preserve_newlines: 2,
        preserve_newlines: true,
        end_with_newline: true,
        wrap_line_length: 120,
        indent_inner_html: true
      })
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'HTML formatting failed'
      }
    }
  }

  private async formatMarkdown(code: string, options: FormatOptions): Promise<FormatResult> {
    try {
      const formatted = await prettier.format(code, {
        parser: 'markdown',
        tabWidth: options.tabWidth,
        useTabs: options.useTabs,
        proseWrap: 'preserve'
      })
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Markdown formatting failed'
      }
    }
  }

  private formatPython(code: string, _options: FormatOptions): FormatResult {
    // Basic Python formatting - indentation and line cleanup
    try {
      const lines = code.split('\n')
      const formatted = lines
        .map(line => line.trimEnd()) // Remove trailing whitespace
        .join('\n')
        .replace(/\n{3,}/g, '\n\n') // Limit consecutive empty lines to 2
      
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: 'Python formatting failed'
      }
    }
  }

  private formatGo(code: string, _options: FormatOptions): FormatResult {
    // Basic Go formatting - mainly cleanup
    try {
      const lines = code.split('\n')
      const formatted = lines
        .map(line => line.trimEnd())
        .join('\n')
        .replace(/\n{3,}/g, '\n\n')
      
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: 'Go formatting failed'
      }
    }
  }

  private formatJava(code: string, options: FormatOptions): FormatResult {
    // Basic Java formatting
    try {
      const formatted = js_beautify(code, {
        indent_size: options.tabWidth,
        indent_char: options.useTabs ? '\t' : ' ',
        max_preserve_newlines: 2,
        preserve_newlines: true,
        keep_array_indentation: false,
        break_chained_methods: false,
        brace_style: 'collapse',
        space_before_conditional: true,
        unescape_strings: false,
        jslint_happy: false,
        end_with_newline: true
      })
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: 'Java formatting failed'
      }
    }
  }

  private formatCpp(code: string, options: FormatOptions): FormatResult {
    // Basic C++ formatting
    try {
      const formatted = js_beautify(code, {
        indent_size: options.tabWidth,
        indent_char: options.useTabs ? '\t' : ' ',
        max_preserve_newlines: 2,
        preserve_newlines: true,
        keep_array_indentation: false,
        break_chained_methods: false,
        brace_style: 'collapse',
        space_before_conditional: true,
        unescape_strings: false,
        jslint_happy: false,
        end_with_newline: true
      })
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: 'C++ formatting failed'
      }
    }
  }

  private formatXML(code: string, options: FormatOptions): FormatResult {
    try {
      const formatted = html_beautify(code, {
        indent_size: options.tabWidth,
        indent_char: options.useTabs ? '\t' : ' ',
        max_preserve_newlines: 1,
        preserve_newlines: true,
        end_with_newline: true,
        wrap_line_length: 120,
        indent_inner_html: true
      })
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: 'XML formatting failed'
      }
    }
  }

  private formatYAML(code: string, _options: FormatOptions): FormatResult {
    // Basic YAML formatting - mainly indentation cleanup
    try {
      const lines = code.split('\n')
      const formatted = lines
        .map(line => line.trimEnd())
        .join('\n')
        .replace(/\n{3,}/g, '\n\n')
      
      return { success: true, formattedCode: formatted }
    } catch (error) {
      return {
        success: false,
        error: 'YAML formatting failed'
      }
    }
  }

  private lintJavaScript(code: string): LintResult {
    const errors: LintError[] = []
    const warnings: LintWarning[] = []
    const lines = code.split('\n')

    lines.forEach((line, index) => {
      const lineNum = index + 1
      
      // Check for common syntax errors
      if (line.includes('console.log') && !line.includes('//')) {
        warnings.push({
          line: lineNum,
          column: line.indexOf('console.log') + 1,
          message: 'Consider removing console.log statements',
          severity: 'warning'
        })
      }

      // Check for missing semicolons (basic check)
      if (line.trim().match(/^(var|let|const|return|throw)\s+.*[^;{}\s]$/)) {
        warnings.push({
          line: lineNum,
          column: line.length,
          message: 'Missing semicolon',
          severity: 'warning'
        })
      }

      // Check for unmatched brackets
      const openBrackets = (line.match(/[{[(]/g) || []).length
      const closeBrackets = (line.match(/[}\])]/g) || []).length
      if (openBrackets !== closeBrackets && !line.trim().endsWith(',')) {
        warnings.push({
          line: lineNum,
          column: 1,
          message: 'Potentially unmatched brackets',
          severity: 'warning'
        })
      }
    })

    return { errors, warnings }
  }

  private lintJSON(code: string): LintResult {
    const errors: LintError[] = []
    const warnings: LintWarning[] = []

    try {
      JSON.parse(code)
    } catch (error) {
      if (error instanceof SyntaxError) {
        const match = error.message.match(/at position (\d+)/)
        const position = match ? parseInt(match[1]) : 0
        const lines = code.substring(0, position).split('\n')
        const line = lines.length
        const column = lines[lines.length - 1].length + 1

        errors.push({
          line,
          column,
          message: error.message,
          severity: 'error'
        })
      }
    }

    return { errors, warnings }
  }

  private lintPython(code: string): LintResult {
    const errors: LintError[] = []
    const warnings: LintWarning[] = []
    const lines = code.split('\n')

    lines.forEach((line, index) => {
      const lineNum = index + 1
      
      // Check for mixed tabs and spaces
      if (line.includes('\t') && line.includes('  ')) {
        warnings.push({
          line: lineNum,
          column: 1,
          message: 'Mixed tabs and spaces for indentation',
          severity: 'warning'
        })
      }

      // Check for common syntax patterns
      if (line.trim().match(/^(if|for|while|def|class).*[^:]$/)) {
        errors.push({
          line: lineNum,
          column: line.length,
          message: 'Missing colon',
          severity: 'error'
        })
      }
    })

    return { errors, warnings }
  }

  private lintGo(code: string): LintResult {
    const errors: LintError[] = []
    const warnings: LintWarning[] = []
    const lines = code.split('\n')

    lines.forEach((line, index) => {
      const lineNum = index + 1
      
      // Check for unused variables (basic pattern)
      if (line.includes(':=') && !line.includes('_')) {
        const varMatch = line.match(/(\w+)\s*:=/)
        if (varMatch) {
          const varName = varMatch[1]
          const restOfCode = lines.slice(index + 1).join('\n')
          if (!restOfCode.includes(varName)) {
            warnings.push({
              line: lineNum,
              column: line.indexOf(varName) + 1,
              message: `Variable '${varName}' might be unused`,
              severity: 'warning'
            })
          }
        }
      }
    })

    return { errors, warnings }
  }

  private lintHTML(code: string): LintResult {
    const errors: LintError[] = []
    const warnings: LintWarning[] = []
    const lines = code.split('\n')

    lines.forEach((line, index) => {
      const lineNum = index + 1
      
      // Check for unclosed tags (basic check)
      const openTags = line.match(/<(\w+)[^>]*>/g) || []
      const closeTags = line.match(/<\/(\w+)>/g) || []
      
      if (openTags.length > closeTags.length) {
        warnings.push({
          line: lineNum,
          column: 1,
          message: 'Potentially unclosed HTML tags',
          severity: 'warning'
        })
      }
    })

    return { errors, warnings }
  }

  private lintCSS(code: string): LintResult {
    const errors: LintError[] = []
    const warnings: LintWarning[] = []
    const lines = code.split('\n')

    lines.forEach((line, index) => {
      const lineNum = index + 1
      
      // Check for missing semicolons in CSS properties
      if (line.includes(':') && !line.includes(';') && !line.includes('{') && !line.includes('}') && line.trim() !== '') {
        warnings.push({
          line: lineNum,
          column: line.length,
          message: 'Missing semicolon in CSS property',
          severity: 'warning'
        })
      }
    })

    return { errors, warnings }
  }
}

export const codeFormatterService = new CodeFormatterService()