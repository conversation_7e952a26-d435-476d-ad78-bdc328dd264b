import React, { useEffect, useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Activity, Clock, Zap, AlertTriangle } from 'lucide-react';

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  status: 'good' | 'warning' | 'poor';
  threshold: { good: number; warning: number };
}

interface RenderMetric {
  component: string;
  renderTime: number;
  timestamp: number;
}

export default function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [renderMetrics, setRenderMetrics] = useState<RenderMetric[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  const measurePerformance = useCallback(() => {
    const newMetrics: PerformanceMetric[] = [];

    // Core Web Vitals and other performance metrics
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        // First Contentful Paint
        const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0];
        if (fcpEntry) {
          newMetrics.push({
            name: 'First Contentful Paint',
            value: Math.round(fcpEntry.startTime),
            unit: 'ms',
            status: fcpEntry.startTime < 1800 ? 'good' : fcpEntry.startTime < 3000 ? 'warning' : 'poor',
            threshold: { good: 1800, warning: 3000 }
          });
        }

        // Largest Contentful Paint
        const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
        if (lcpEntries.length > 0) {
          const lcp = lcpEntries[lcpEntries.length - 1] as any;
          newMetrics.push({
            name: 'Largest Contentful Paint',
            value: Math.round(lcp.startTime),
            unit: 'ms',
            status: lcp.startTime < 2500 ? 'good' : lcp.startTime < 4000 ? 'warning' : 'poor',
            threshold: { good: 2500, warning: 4000 }
          });
        }

        // DOM Content Loaded
        newMetrics.push({
          name: 'DOM Content Loaded',
          value: Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart),
          unit: 'ms',
          status: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart < 1000 ? 'good' : 
                  navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart < 2000 ? 'warning' : 'poor',
          threshold: { good: 1000, warning: 2000 }
        });

        // Load Complete
        newMetrics.push({
          name: 'Load Complete',
          value: Math.round(navigation.loadEventEnd - navigation.loadEventStart),
          unit: 'ms',
          status: navigation.loadEventEnd - navigation.loadEventStart < 500 ? 'good' : 
                  navigation.loadEventEnd - navigation.loadEventStart < 1000 ? 'warning' : 'poor',
          threshold: { good: 500, warning: 1000 }
        });
      }

      // Memory usage (if available)
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usedMemory = memory.usedJSHeapSize / 1024 / 1024; // Convert to MB
        
        newMetrics.push({
          name: 'Memory Usage',
          value: Math.round(usedMemory),
          unit: 'MB',
          status: usedMemory < 50 ? 'good' : usedMemory < 100 ? 'warning' : 'poor',
          threshold: { good: 50, warning: 100 }
        });
      }
    }

    // Network connection info
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection) {
        newMetrics.push({
          name: 'Network Speed',
          value: connection.downlink,
          unit: 'Mbps',
          status: connection.downlink > 10 ? 'good' : connection.downlink > 1 ? 'warning' : 'poor',
          threshold: { good: 10, warning: 1 }
        });
      }
    }

    setMetrics(newMetrics);
  }, []);

  // Measure component render times
  const measureRenderTime = useCallback((componentName: string, startTime: number) => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    setRenderMetrics(prev => [
      ...prev.slice(-9), // Keep only last 10 measurements
      {
        component: componentName,
        renderTime: Math.round(renderTime * 100) / 100,
        timestamp: Date.now()
      }
    ]);
  }, []);

  useEffect(() => {
    // Initial measurement
    measurePerformance();

    // Set up periodic measurements
    const interval = setInterval(measurePerformance, 5000);

    // Measure when page becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        measurePerformance();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [measurePerformance]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600 bg-green-50 border-green-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'poor': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return <Zap className="h-4 w-4 text-green-600" />;
      case 'warning': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'poor': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  if (!isVisible) {
    return (
      <Button
        size="sm"
        variant="outline"
        className="fixed bottom-4 left-4 z-50"
        onClick={() => setIsVisible(true)}
      >
        <Activity className="h-4 w-4 mr-2" />
        Performance
      </Button>
    );
  }

  return (
    <Card className="fixed bottom-4 left-4 w-96 max-h-96 z-50 shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Performance Monitor
          </CardTitle>
          <Button 
            size="sm" 
            variant="ghost"
            onClick={() => setIsVisible(false)}
          >
            ×
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <ScrollArea className="h-64">
          <div className="space-y-3">
            {/* Core Metrics */}
            <div>
              <h4 className="text-xs font-medium mb-2">Core Web Vitals</h4>
              <div className="space-y-2">
                {metrics.map((metric, index) => (
                  <div key={index} className={`border rounded p-2 text-xs ${getStatusColor(metric.status)}`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(metric.status)}
                        <span className="font-medium">{metric.name}</span>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {metric.value}{metric.unit}
                      </Badge>
                    </div>
                    <div className="mt-1 text-xs opacity-75">
                      Good: &lt;{metric.threshold.good}{metric.unit} | 
                      Warning: &lt;{metric.threshold.warning}{metric.unit}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Render Metrics */}
            {renderMetrics.length > 0 && (
              <div>
                <h4 className="text-xs font-medium mb-2">Recent Renders</h4>
                <div className="space-y-1">
                  {renderMetrics.slice(-5).map((render, index) => (
                    <div key={index} className="flex items-center justify-between text-xs p-1 border rounded">
                      <span>{render.component}</span>
                      <Badge variant="outline" className="text-xs">
                        {render.renderTime}ms
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-2">
              <Button size="sm" variant="outline" onClick={measurePerformance}>
                Refresh
              </Button>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => {
                  if ('performance' in window) {
                    performance.mark('manual-measurement');
                    console.log('Performance entries:', performance.getEntries());
                  }
                }}
              >
                Log to Console
              </Button>
            </div>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

// Hook to measure component render times
export function usePerformanceMonitor(componentName: string) {
  const startTime = React.useRef<number>(0);

  React.useEffect(() => {
    startTime.current = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime.current;
      
      if (renderTime > 16) { // Only log slow renders (>16ms)
        console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`);
      }
    };
  });

  const measureAsync = React.useCallback(async (operation: string, fn: () => Promise<any>) => {
    const start = performance.now();
    try {
      const result = await fn();
      const end = performance.now();
      console.log(`${componentName} ${operation}: ${(end - start).toFixed(2)}ms`);
      return result;
    } catch (error) {
      const end = performance.now();
      console.log(`${componentName} ${operation} (error): ${(end - start).toFixed(2)}ms`);
      throw error;
    }
  }, [componentName]);

  return { measureAsync };
}
