export interface TestUser {
  id?: string;
  username: string;
  email: string;
  password: string;
  token?: string;
}

export interface TestPaste {
  id?: string;
  title: string;
  content: string;
  language: string;
  custom_url?: string;
  is_public: boolean;
  expires_at?: string;
}

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  status: number;
}

export class TestApiClient {
  private baseUrl: string;
  private authToken?: string;

  constructor(baseUrl: string = 'http://localhost:8080/api/v1') {
    this.baseUrl = baseUrl;
  }

  setAuthToken(token: string): void {
    this.authToken = token;
  }

  clearAuthToken(): void {
    this.authToken = undefined;
  }

  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...((options.headers as Record<string, string>) || {})
    };

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers
      });

      const data = await response.json().catch(() => null);

      return {
        data,
        status: response.status,
        error: !response.ok ? data?.error || 'Request failed' : undefined
      };
    } catch (error) {
      return {
        status: 0,
        error: error instanceof Error ? error.message : 'Network error'
      };
    }
  }

  // Health check
  async health(): Promise<ApiResponse> {
    return this.request('/health');
  }

  // Authentication
  async register(user: Omit<TestUser, 'id' | 'token'>): Promise<ApiResponse<TestUser>> {
    return this.request('/users/register', {
      method: 'POST',
      body: JSON.stringify(user)
    });
  }

  async login(credentials: { username: string; password: string }): Promise<ApiResponse<{ token: string; user: TestUser }>> {
    const response = await this.request('/users/login', {
      method: 'POST',
      body: JSON.stringify(credentials)
    });

    if (response.data?.token) {
      this.setAuthToken(response.data.token);
    }

    return response;
  }

  async getProfile(): Promise<ApiResponse<TestUser>> {
    return this.request('/users/profile');
  }

  async getUserPastes(limit: number = 20, offset: number = 0): Promise<ApiResponse<{ pastes: TestPaste[] }>> {
    return this.request(`/users/profile/pastes?limit=${limit}&offset=${offset}`);
  }

  async getPublicProfile(username: string): Promise<ApiResponse<TestUser>> {
    return this.request(`/users/${username}/public`);
  }

  // Pastes
  async createPaste(paste: Omit<TestPaste, 'id'>): Promise<ApiResponse<TestPaste>> {
    return this.request('/pastes', {
      method: 'POST',
      body: JSON.stringify(paste)
    });
  }

  async getPaste(id: string): Promise<ApiResponse<TestPaste>> {
    return this.request(`/pastes/${id}`);
  }

  async getPasteByCustomUrl(customUrl: string): Promise<ApiResponse<TestPaste>> {
    return this.request(`/p/${customUrl}`);
  }

  async updatePaste(id: string, paste: Partial<TestPaste>): Promise<ApiResponse<TestPaste>> {
    return this.request(`/pastes/${id}`, {
      method: 'PUT',
      body: JSON.stringify(paste)
    });
  }

  async deletePaste(id: string): Promise<ApiResponse> {
    return this.request(`/pastes/${id}`, {
      method: 'DELETE'
    });
  }

  async checkCustomUrlAvailability(customUrl: string): Promise<ApiResponse<{ available: boolean }>> {
    return this.request(`/pastes/check-url?custom_url=${encodeURIComponent(customUrl)}`);
  }

  // Utility methods for test data creation
  async createTestUser(overrides: Partial<TestUser> = {}): Promise<TestUser> {
    const timestamp = Date.now();
    const user: Omit<TestUser, 'id' | 'token'> = {
      username: `testuser_${timestamp}`,
      email: `test_${timestamp}@example.com`,
      password: 'TestPassword123!',
      ...overrides
    };

    const response = await this.register(user);
    if (response.error) {
      throw new Error(`Failed to create test user: ${response.error}`);
    }

    return { ...user, ...response.data };
  }

  async createTestPaste(overrides: Partial<TestPaste> = {}): Promise<TestPaste> {
    const timestamp = Date.now();
    const paste: Omit<TestPaste, 'id'> = {
      title: `Test Paste ${timestamp}`,
      content: `console.log('Test paste content ${timestamp}');`,
      language: 'javascript',
      is_public: true,
      ...overrides
    };

    const response = await this.createPaste(paste);
    if (response.error) {
      throw new Error(`Failed to create test paste: ${response.error}`);
    }

    return { ...paste, ...response.data };
  }

  async loginAsTestUser(userOverrides: Partial<TestUser> = {}): Promise<TestUser> {
    const user = await this.createTestUser(userOverrides);
    const loginResponse = await this.login({
      username: user.username,
      password: user.password
    });

    if (loginResponse.error) {
      throw new Error(`Failed to login test user: ${loginResponse.error}`);
    }

    return { ...user, token: loginResponse.data?.token };
  }
}
