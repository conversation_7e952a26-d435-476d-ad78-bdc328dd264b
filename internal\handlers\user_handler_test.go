package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"enhanced-pastebin/internal/models"

	"github.com/gin-gonic/gin"
)

// Mock user service for testing
type mockUserService struct {
	users map[string]*models.User
}

func newMockUserService() *mockUserService {
	return &mockUserService{
		users: make(map[string]*models.User),
	}
}

func (m *mockUserService) Register(req *models.CreateUserRequest) (*models.User, error) {
	// Check for existing users
	for _, user := range m.users {
		if user.Email == req.Email {
			return nil, &mockError{"user with this email already exists"}
		}
		if user.Username == req.Username {
			return nil, &mockError{"user with this username already exists"}
		}
	}

	user := &models.User{
		ID:       "test-id-" + req.Username,
		Username: req.Username,
		Email:    req.Email,
		PasswordHash: "hashed-" + req.Password,
	}

	m.users[user.ID] = user
	return user, nil
}

func (m *mockUserService) Login(req *models.LoginRequest) (*models.LoginResponse, error) {
	for _, user := range m.users {
		if user.Email == req.Email {
			// Simple password check for testing
			if user.PasswordHash == "hashed-"+req.Password {
				return &models.LoginResponse{
					Token: "test-jwt-token",
					User:  *user,
				}, nil
			}
		}
	}
	return nil, &mockError{"invalid credentials"}
}

func (m *mockUserService) GetByID(id string) (*models.User, error) {
	if user, exists := m.users[id]; exists {
		return user, nil
	}
	return nil, &mockError{"user not found"}
}

type mockError struct {
	message string
}

func (e *mockError) Error() string {
	return e.message
}

func TestUserHandler_Register_Success(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	// Create mock service and handler
	userService := newMockUserService()
	handler := NewUserHandler(userService)

	// Create router
	router := gin.New()
	router.POST("/register", handler.Register)

	// Create request
	reqData := models.CreateUserRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "password123",
	}
	
	jsonData, _ := json.Marshal(reqData)
	req := httptest.NewRequest("POST", "/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusCreated {
		t.Errorf("Expected status 201, got %d", w.Code)
	}

	// Parse response
	var user models.User
	if err := json.Unmarshal(w.Body.Bytes(), &user); err != nil {
		t.Fatalf("Failed to parse response: %v", err)
	}

	if user.Username != reqData.Username {
		t.Errorf("Expected username %s, got %s", reqData.Username, user.Username)
	}

	if user.Email != reqData.Email {
		t.Errorf("Expected email %s, got %s", reqData.Email, user.Email)
	}
}

func TestUserHandler_Register_InvalidJSON(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	// Create mock service and handler
	userService := newMockUserService()
	handler := NewUserHandler(userService)

	// Create router
	router := gin.New()
	router.POST("/register", handler.Register)

	// Create request with invalid JSON
	req := httptest.NewRequest("POST", "/register", bytes.NewBufferString("invalid json"))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status 400, got %d", w.Code)
	}
}

func TestUserHandler_Register_DuplicateEmail(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	// Create mock service and handler
	userService := newMockUserService()
	handler := NewUserHandler(userService)

	// Create router
	router := gin.New()
	router.POST("/register", handler.Register)

	// Register first user
	reqData1 := models.CreateUserRequest{
		Username: "testuser1",
		Email:    "<EMAIL>",
		Password: "password123",
	}
	
	jsonData1, _ := json.Marshal(reqData1)
	req1 := httptest.NewRequest("POST", "/register", bytes.NewBuffer(jsonData1))
	req1.Header.Set("Content-Type", "application/json")

	w1 := httptest.NewRecorder()
	router.ServeHTTP(w1, req1)

	if w1.Code != http.StatusCreated {
		t.Fatalf("First registration should succeed, got %d", w1.Code)
	}

	// Try to register second user with same email
	reqData2 := models.CreateUserRequest{
		Username: "testuser2",
		Email:    "<EMAIL>", // Same email
		Password: "password456",
	}
	
	jsonData2, _ := json.Marshal(reqData2)
	req2 := httptest.NewRequest("POST", "/register", bytes.NewBuffer(jsonData2))
	req2.Header.Set("Content-Type", "application/json")

	w2 := httptest.NewRecorder()
	router.ServeHTTP(w2, req2)

	// Check response
	if w2.Code != http.StatusBadRequest {
		t.Errorf("Expected status 400 for duplicate email, got %d", w2.Code)
	}
}

func TestUserHandler_Login_Success(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	// Create mock service and handler
	userService := newMockUserService()
	handler := NewUserHandler(userService)

	// Create router
	router := gin.New()
	router.POST("/register", handler.Register)
	router.POST("/login", handler.Login)

	// First register a user
	registerData := models.CreateUserRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "password123",
	}
	
	registerJSON, _ := json.Marshal(registerData)
	registerReq := httptest.NewRequest("POST", "/register", bytes.NewBuffer(registerJSON))
	registerReq.Header.Set("Content-Type", "application/json")

	registerW := httptest.NewRecorder()
	router.ServeHTTP(registerW, registerReq)

	if registerW.Code != http.StatusCreated {
		t.Fatalf("Registration should succeed, got %d", registerW.Code)
	}

	// Now test login
	loginData := models.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}
	
	loginJSON, _ := json.Marshal(loginData)
	loginReq := httptest.NewRequest("POST", "/login", bytes.NewBuffer(loginJSON))
	loginReq.Header.Set("Content-Type", "application/json")

	loginW := httptest.NewRecorder()
	router.ServeHTTP(loginW, loginReq)

	// Check response
	if loginW.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", loginW.Code)
	}

	// Parse response
	var loginResponse models.LoginResponse
	if err := json.Unmarshal(loginW.Body.Bytes(), &loginResponse); err != nil {
		t.Fatalf("Failed to parse login response: %v", err)
	}

	if loginResponse.Token == "" {
		t.Error("Expected token in response, got empty string")
	}

	if loginResponse.User.Email != loginData.Email {
		t.Errorf("Expected email %s, got %s", loginData.Email, loginResponse.User.Email)
	}
}

func TestUserHandler_Login_InvalidCredentials(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	// Create mock service and handler
	userService := newMockUserService()
	handler := NewUserHandler(userService)

	// Create router
	router := gin.New()
	router.POST("/login", handler.Login)

	// Test login with non-existent user
	loginData := models.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}
	
	loginJSON, _ := json.Marshal(loginData)
	loginReq := httptest.NewRequest("POST", "/login", bytes.NewBuffer(loginJSON))
	loginReq.Header.Set("Content-Type", "application/json")

	loginW := httptest.NewRecorder()
	router.ServeHTTP(loginW, loginReq)

	// Check response
	if loginW.Code != http.StatusUnauthorized {
		t.Errorf("Expected status 401, got %d", loginW.Code)
	}
}