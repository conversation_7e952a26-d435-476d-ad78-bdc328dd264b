package handlers

import (
	"net/http"
	"strconv"

	"enhanced-pastebin/internal/models"
	"enhanced-pastebin/internal/services"

	"github.com/gin-gonic/gin"
)

type Paste<PERSON>andler struct {
	pasteService       services.PasteService
	dependencyService  services.DependencyService
	securityService    services.SecurityService
}

func NewPasteHandler(pasteService services.PasteService, dependencyService services.DependencyService, securityService services.SecurityService) *PasteHandler {
	return &PasteHandler{
		pasteService:      pasteService,
		dependencyService: dependencyService,
		securityService:   securityService,
	}
}

func (h *PasteHandler) CreatePaste(c *gin.Context) {
	var req models.CreatePasteRequest
	if err := c.ShouldBindJ<PERSON>N(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var userID *string
	if id, exists := c.Get("user_id"); exists {
		if idStr, ok := id.(string); ok {
			userID = &idStr
		}
	}

	paste, err := h.pasteService.CreatePaste(&req, userID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, paste)
}

func (h *PasteHandler) GetPaste(c *gin.Context) {
	id := c.Param("id")
	
	paste, err := h.pasteService.GetPaste(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, paste)
}

func (h *PasteHandler) UpdatePaste(c *gin.Context) {
	id := c.Param("id")
	userID := c.GetString("user_id")

	var req models.UpdatePasteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	paste, err := h.pasteService.UpdatePaste(id, &req, userID)
	if err != nil {
		if err.Error() == "unauthorized" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, paste)
}

func (h *PasteHandler) DeletePaste(c *gin.Context) {
	id := c.Param("id")
	userID := c.GetString("user_id")

	err := h.pasteService.DeletePaste(id, userID)
	if err != nil {
		if err.Error() == "unauthorized" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

func (h *PasteHandler) GetPasteByCustomURL(c *gin.Context) {
	customURL := c.Param("customUrl")
	
	paste, err := h.pasteService.GetPasteByCustomURL(customURL)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, paste)
}

func (h *PasteHandler) GetUserPastes(c *gin.Context) {
	userID := c.GetString("user_id")
	
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	pastes, err := h.pasteService.GetUserPastes(userID, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, pastes)
}

func (h *PasteHandler) CheckCustomURLAvailability(c *gin.Context) {
	customURL := c.Query("url")
	if customURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "url parameter is required"})
		return
	}

	available, err := h.pasteService.CheckCustomURLAvailability(customURL)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error(), "available": false})
		return
	}

	c.JSON(http.StatusOK, gin.H{"available": available})
}

func (h *PasteHandler) GetPasteDependencies(c *gin.Context) {
	id := c.Param("id")
	
	paste, err := h.pasteService.GetPaste(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Check if security scanning is requested
	includeSecurity := c.Query("security") == "true"
	
	if includeSecurity {
		// Extract dependencies with security scanning
		dependencies, err := h.dependencyService.ExtractDependenciesWithSecurity(c.Request.Context(), paste.Content, paste.Language)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dependencies)
	} else {
		// Extract basic dependencies without security scanning
		dependencies, err := h.dependencyService.ExtractDependencies(paste.Content, paste.Language)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dependencies)
	}
}

func (h *PasteHandler) GetPasteSecurityScan(c *gin.Context) {
	id := c.Param("id")
	
	paste, err := h.pasteService.GetPaste(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Extract dependencies with security scanning
	dependencies, err := h.dependencyService.ExtractDependenciesWithSecurity(c.Request.Context(), paste.Content, paste.Language)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only security-related information
	securitySummary := map[string]interface{}{
		"paste_id":        id,
		"language":        paste.Language,
		"total_dependencies": dependencies.TotalCount,
		"vulnerability_count": dependencies.VulnCount,
		"critical_count":  dependencies.CriticalCount,
		"high_count":      dependencies.HighCount,
		"moderate_count":  dependencies.ModerateCount,
		"low_count":       dependencies.LowCount,
		"security_score":  dependencies.SecurityScore,
		"last_scanned":    dependencies.LastScanned,
		"vulnerable_dependencies": func() []map[string]interface{} {
			var vulnDeps []map[string]interface{}
			for _, dep := range dependencies.Dependencies {
				if dep.Security != nil && dep.Security.HasVulns {
					vulnDeps = append(vulnDeps, map[string]interface{}{
						"name":            dep.Name,
						"version":         dep.Version,
						"language":        dep.Language,
						"vulnerabilities": dep.Security.Vulnerabilities,
						"security_score":  dep.Security.SecurityScore,
					})
				}
			}
			return vulnDeps
		}(),
	}

	c.JSON(http.StatusOK, securitySummary)
}