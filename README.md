# Enhanced Pastebin

A modern code sharing platform built with React frontend and Go backend, featuring real-time collaboration, advanced security, and intelligent content management.

## Features

### Core Functionality
- **Paste Management**: Create, view, edit, and delete code snippets
- **Custom URLs**: Create memorable URLs for your pastes
- **Syntax Highlighting**: Support for 100+ programming languages
- **Expiration Control**: Set time-based or view-based expiration
- **Encryption**: Client-side encryption for sensitive content

### Advanced Features
- **Real-time Collaboration**: Live editing with WebSocket support
- **Chat System**: Discuss code with line-specific comments
- **Version Tracking**: Complete history of paste modifications
- **User Profiles**: Public profiles with statistics and activity
- **Access Logging**: Comprehensive audit trails
- **Digital Watermarking**: Invisible content protection
- **Dependency Analysis**: Security scanning for code dependencies
- **Multi-format Support**: View content as JSON, CSV, or raw text

### Security & Privacy
- **User Authentication**: Secure JWT-based authentication
- **Rate Limiting**: Protection against abuse
- **Content Encryption**: AES encryption for sensitive pastes
- **Access Control**: Private and public paste visibility
- **Security Scanning**: Automated vulnerability detection

### User Experience
- **Responsive Design**: Mobile-first, accessible interface
- **Dark/Light Theme**: Automatic theme switching
- **Keyboard Navigation**: Full keyboard accessibility
- **Loading States**: Skeleton loading and progress indicators
- **Error Handling**: Comprehensive error boundaries
- **Performance Monitoring**: Real-time performance metrics (dev mode)

## Tech Stack

### Backend
- **Language**: Go 1.21+
- **Framework**: Gin (HTTP router)
- **Database**: PostgreSQL 14+
- **Authentication**: JWT tokens
- **WebSockets**: Gorilla WebSocket
- **Migration**: golang-migrate

### Frontend
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI primitives
- **State Management**: React Context
- **Routing**: React Router v6
- **Code Editor**: Monaco Editor
- **Build Tool**: Vite

### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Database**: PostgreSQL with connection pooling
- **Caching**: In-memory caching for performance
- **Monitoring**: Built-in performance and accessibility tools

## Quick Start

### Prerequisites
- Go 1.21 or higher
- Node.js 18 or higher
- Docker and Docker Compose
- Make (optional, for convenience commands)

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd enhanced-pastebin
   ```

2. **Set up the development environment**
   ```bash
   make setup
   ```
   This will:
   - Copy environment files
   - Start PostgreSQL and Redis with Docker
   - Install Go and Node.js dependencies

3. **Run database migrations**
   ```bash
   make migrate-up
   ```

4. **Start development servers**
   ```bash
   make dev
   ```
   This starts both backend (port 8080) and frontend (port 3000) servers.

### Manual Setup (without Make)

1. **Start services**
   ```bash
   docker-compose up -d
   ```

2. **Set up environment**
   ```bash
   cp .env.example .env
   cp frontend/.env.example frontend/.env
   ```

3. **Install dependencies**
   ```bash
   go mod tidy
   cd frontend && npm install
   ```

4. **Start backend**
   ```bash
   go run cmd/server/main.go
   ```

5. **Start frontend** (in another terminal)
   ```bash
   cd frontend && npm run dev
   ```

## Development

### Available Commands

- `make setup` - Set up development environment
- `make dev` - Start both backend and frontend servers
- `make dev-backend` - Start only backend server
- `make dev-frontend` - Start only frontend server
- `make migrate-up` - Run database migrations
- `make migrate-down` - Rollback database migrations
- `make build` - Build production artifacts
- `make test` - Run tests
- `make clean` - Clean up build artifacts
- `make db-reset` - Reset database (WARNING: deletes all data)

### Project Structure

```
enhanced-pastebin/
├── cmd/
│   └── server/          # Main application entry point
├── internal/
│   ├── config/          # Configuration management
│   ├── database/        # Database connection
│   ├── handlers/        # HTTP handlers
│   ├── middleware/      # HTTP middleware
│   ├── models/          # Data models
│   ├── repositories/    # Data access layer
│   ├── services/        # Business logic
│   └── utils/           # Utility functions
├── migrations/          # Database migrations
├── frontend/
│   ├── src/
│   │   ├── components/  # React components
│   │   ├── contexts/    # React contexts
│   │   ├── hooks/       # Custom hooks
│   │   ├── lib/         # Utilities and API client
│   │   └── pages/       # Page components
│   └── public/          # Static assets
└── docker-compose.yml   # Development services
```

## API Documentation

### Authentication Endpoints
- `POST /api/v1/users/register` - Register new user
- `POST /api/v1/users/login` - User login
- `GET /api/v1/users/profile` - Get user profile
- `GET /api/v1/users/profile/pastes` - Get user's pastes
- `GET /api/v1/users/:username/public` - Get public user profile

### Paste Endpoints
- `POST /api/v1/pastes` - Create new paste
- `GET /api/v1/pastes/:id` - Get paste by ID
- `PUT /api/v1/pastes/:id` - Update paste
- `DELETE /api/v1/pastes/:id` - Delete paste
- `GET /api/v1/p/:customUrl` - Get paste by custom URL

### Chat Endpoints
- `POST /api/v1/chat/messages` - Send chat message
- `GET /api/v1/chat/pastes/:pasteId/messages` - Get chat messages
- `DELETE /api/v1/chat/messages/:messageId` - Delete message

### Version Endpoints
- `GET /api/v1/versions/pastes/:pasteId` - Get paste versions
- `GET /api/v1/versions/:versionId` - Get specific version
- `GET /api/v1/versions/compare` - Compare two versions

### Audit Endpoints
- `GET /api/v1/audit/pastes/:pasteId/logs` - Get access logs
- `GET /api/v1/audit/security-events` - Get security events
- `GET /api/v1/audit/pastes/:pasteId/watermarks` - Get watermarks

### Expiration Endpoints
- `GET /api/v1/expiration/expiring` - Get expiring pastes
- `POST /api/v1/expiration/cleanup` - Trigger cleanup

### WebSocket Endpoints
- `/api/v1/ws` - WebSocket connection for real-time features

### Health Check
- `GET /api/v1/health` - Health check endpoint

## Environment Variables

### Backend (.env)
```env
DATABASE_URL=postgres://username:password@localhost/enhanced_pastebin?sslmode=disable
JWT_SECRET=your-super-secret-jwt-key
PORT=8080
GIN_MODE=debug
```

### Frontend (frontend/.env)
```env
VITE_API_URL=http://localhost:8080/api/v1
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
go test ./...
```

### Frontend Tests
```bash
cd frontend
npm test
```

### Integration Tests
```bash
# Start test environment
docker-compose -f docker-compose.test.yml up -d

# Run integration tests
npm run test:integration
```

### Test Coverage
```bash
# Backend coverage
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out

# Frontend coverage
npm run test:coverage
```

## 🚀 Deployment

### Production Build

1. **Build frontend**
   ```bash
   cd frontend
   npm run build
   ```

2. **Build backend**
   ```bash
   cd backend
   go build -o bin/server cmd/server/main.go
   ```

3. **Deploy with Docker**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

### Environment-specific Configurations

- **Development**: Full debugging, hot reload, dev tools
- **Staging**: Production-like with additional logging
- **Production**: Optimized builds, security hardening

## Database Schema

The application uses PostgreSQL with the following main tables:
- `users` - User accounts and profiles
- `pastes` - Code snippets and metadata
- `paste_versions` - Version history tracking
- `chat_messages` - Chat messages for collaboration
- `access_logs` - Access and audit logging
- `watermarks` - Digital watermarking data

## 🔒 Security Considerations

- **Input Validation**: All inputs are validated and sanitized
- **SQL Injection**: Using parameterized queries
- **XSS Protection**: Content Security Policy headers
- **CSRF Protection**: SameSite cookies and CSRF tokens
- **Rate Limiting**: Configurable rate limits per endpoint
- **Encryption**: AES-256 for sensitive content
- **Audit Logging**: Comprehensive access and security logs

## ⚡ Performance Features

- **Caching**: In-memory caching for frequently accessed data
- **Connection Pooling**: Optimized database connections
- **Lazy Loading**: Components and routes loaded on demand
- **Code Splitting**: Optimized bundle sizes
- **Image Optimization**: Responsive images and lazy loading
- **Performance Monitoring**: Real-time metrics in development

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow Go and TypeScript best practices
- Write tests for new features
- Update documentation for API changes
- Ensure accessibility compliance
- Test on multiple browsers and devices

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Roadmap

This is the initial implementation focusing on core infrastructure. Future tasks include:
- Real-time collaboration with WebSocket
- Monaco Editor integration
- Client-side encryption
- Dependency detection and security scanning
- Advanced user profiles and statistics
- Comprehensive testing suite

See the [tasks.md](.kiro/specs/enhanced-pastebin/tasks.md) file for the complete implementation plan.