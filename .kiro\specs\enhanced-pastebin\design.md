# Design Document

## Overview

The Enhanced Pastebin is a modern code sharing platform built with React frontend and Go backend, featuring real-time collaboration, advanced security, and intelligent content management. The architecture emphasizes scalability, security, and user experience through microservices design, WebSocket-based real-time communication, and client-side encryption capabilities.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        React[React Frontend]
        PWA[Progressive Web App]
        Crypto[Client-side Crypto]
    end
    
    subgraph "API Gateway"
        Gateway[API Gateway/Load Balancer]
        Auth[Authentication Service]
        RateLimit[Rate Limiting]
    end
    
    subgraph "Core Services"
        PasteAPI[Paste Service]
        CollabAPI[Collaboration Service]
        ChatAPI[Chat Service]
        UserAPI[User Service]
        AnalyticsAPI[Analytics Service]
    end
    
    subgraph "Real-time Layer"
        WebSocket[WebSocket Server]
        Redis[Redis Pub/Sub]
        OT[Operational Transform Engine]
    end
    
    subgraph "Storage Layer"
        PostgreSQL[(PostgreSQL)]
        S3[(Object Storage)]
        Cache[(Redis Cache)]
    end
    
    subgraph "External Services"
        CDN[CDN/Static Assets]
        PackageAPI[Package Registry APIs]
        SecurityAPI[Security Scanning]
    end
    
    React --> Gateway
    Gateway --> PasteAPI
    Gateway --> CollabAPI
    Gateway --> ChatAPI
    Gateway --> UserAPI
    Gateway --> AnalyticsAPI
    
    CollabAPI --> WebSocket
    ChatAPI --> WebSocket
    WebSocket --> Redis
    WebSocket --> OT
    
    PasteAPI --> PostgreSQL
    PasteAPI --> S3
    PasteAPI --> Cache
    
    PasteAPI --> PackageAPI
    PasteAPI --> SecurityAPI
```

### Technology Stack

**Frontend:**
- React 18 with TypeScript
- Radix UI for accessible components
- Monaco Editor for code editing
- Socket.io-client for real-time communication
- Web Crypto API for client-side encryption
- Vite for build tooling

**Backend:**
- Go 1.21+ with Gin framework
- WebSocket support via Gorilla WebSocket
- PostgreSQL for primary data storage
- Redis for caching and pub/sub
- AWS S3 or compatible for file storage
- Docker for containerization

## Components and Interfaces

### Frontend Components

#### Core Editor Component
```typescript
interface EditorProps {
  content: string;
  language: string;
  theme: string;
  readOnly: boolean;
  collaborators: Collaborator[];
  onContentChange: (content: string, operation: Operation) => void;
  onCursorChange: (position: Position) => void;
}

interface Collaborator {
  id: string;
  name: string;
  color: string;
  cursor: Position;
  selection: Range;
}
```

#### Real-time Collaboration Manager
```typescript
interface CollaborationManager {
  connect(pasteId: string): Promise<void>;
  disconnect(): void;
  sendOperation(operation: Operation): void;
  onRemoteOperation(callback: (operation: Operation) => void): void;
  onUserJoin(callback: (user: Collaborator) => void): void;
  onUserLeave(callback: (userId: string) => void): void;
}
```

#### Encryption Service
```typescript
interface EncryptionService {
  generateKey(): Promise<CryptoKey>;
  encrypt(content: string, key: CryptoKey): Promise<EncryptedData>;
  decrypt(encryptedData: EncryptedData, key: CryptoKey): Promise<string>;
  exportKey(key: CryptoKey): Promise<string>;
  importKey(keyData: string): Promise<CryptoKey>;
}
```

### Backend Services

#### Paste Service API
```go
type PasteService interface {
    CreatePaste(ctx context.Context, req CreatePasteRequest) (*Paste, error)
    GetPaste(ctx context.Context, id string) (*Paste, error)
    UpdatePaste(ctx context.Context, id string, req UpdatePasteRequest) (*Paste, error)
    DeletePaste(ctx context.Context, id string) error
    ListPastes(ctx context.Context, filters PasteFilters) ([]*Paste, error)
    GetPasteVersions(ctx context.Context, id string) ([]*PasteVersion, error)
}

type Paste struct {
    ID          string    `json:"id"`
    CustomURL   string    `json:"custom_url,omitempty"`
    Title       string    `json:"title"`
    Content     string    `json:"content"`
    Language    string    `json:"language"`
    IsEncrypted bool      `json:"is_encrypted"`
    ExpiresAt   time.Time `json:"expires_at,omitempty"`
    ViewCount   int       `json:"view_count"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    UserID      string    `json:"user_id"`
}
```

#### Collaboration Service
```go
type CollaborationService interface {
    JoinSession(ctx context.Context, pasteId, userId string) error
    LeaveSession(ctx context.Context, pasteId, userId string) error
    BroadcastOperation(ctx context.Context, pasteId string, op Operation) error
    GetActiveUsers(ctx context.Context, pasteId string) ([]*ActiveUser, error)
}

type Operation struct {
    Type      string      `json:"type"`
    Position  int         `json:"position"`
    Content   string      `json:"content,omitempty"`
    Length    int         `json:"length,omitempty"`
    UserID    string      `json:"user_id"`
    Timestamp time.Time   `json:"timestamp"`
}
```

#### WebSocket Handler
```go
type WebSocketHandler struct {
    hub            *Hub
    collaborationSvc CollaborationService
    chatSvc        ChatService
}

type Hub struct {
    clients    map[*Client]bool
    broadcast  chan []byte
    register   chan *Client
    unregister chan *Client
    rooms      map[string]map[*Client]bool
}
```

## Data Models

### Database Schema

#### Pastes Table
```sql
CREATE TABLE pastes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    custom_url VARCHAR(255) UNIQUE,
    title VARCHAR(500) NOT NULL,
    content TEXT,
    language VARCHAR(50),
    is_encrypted BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP,
    view_count INTEGER DEFAULT 0,
    max_views INTEGER,
    is_watermarked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    user_id UUID REFERENCES users(id),
    
    INDEX idx_custom_url (custom_url),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);
```

#### Paste Versions Table
```sql
CREATE TABLE paste_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    paste_id UUID REFERENCES pastes(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    content TEXT NOT NULL,
    diff_data JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    
    UNIQUE(paste_id, version_number),
    INDEX idx_paste_id (paste_id)
);
```

#### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    reputation_score INTEGER DEFAULT 0,
    badges JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT NOW(),
    last_active TIMESTAMP DEFAULT NOW(),
    
    INDEX idx_username (username),
    INDEX idx_email (email)
);
```

#### Chat Messages Table
```sql
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    paste_id UUID REFERENCES pastes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    content TEXT NOT NULL,
    line_references INTEGER[],
    created_at TIMESTAMP DEFAULT NOW(),
    
    INDEX idx_paste_id (paste_id),
    INDEX idx_created_at (created_at)
);
```

#### Snippet Packs Table
```sql
CREATE TABLE snippet_packs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    tags TEXT[],
    is_public BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    
    INDEX idx_created_by (created_by),
    INDEX idx_tags USING GIN (tags)
);

CREATE TABLE snippet_pack_items (
    pack_id UUID REFERENCES snippet_packs(id) ON DELETE CASCADE,
    paste_id UUID REFERENCES pastes(id) ON DELETE CASCADE,
    order_index INTEGER NOT NULL,
    
    PRIMARY KEY (pack_id, paste_id)
);
```

### Redis Data Structures

#### Active Sessions
```
Key: session:{paste_id}
Type: Hash
Fields:
  - user:{user_id} -> {name, color, cursor_position, last_seen}
  - operation_count -> integer
  - created_at -> timestamp
```

#### Operation Log
```
Key: operations:{paste_id}
Type: List
Value: JSON serialized operations with timestamps
TTL: 24 hours
```

## Error Handling

### Error Response Format
```go
type ErrorResponse struct {
    Error   string            `json:"error"`
    Code    string            `json:"code"`
    Details map[string]string `json:"details,omitempty"`
}
```

### Error Categories

1. **Validation Errors (400)**
   - Invalid paste content
   - Custom URL already taken
   - Invalid expiration settings

2. **Authentication Errors (401)**
   - Invalid credentials
   - Expired tokens
   - Missing authentication

3. **Authorization Errors (403)**
   - Access denied to private paste
   - Insufficient permissions for operation

4. **Not Found Errors (404)**
   - Paste not found
   - Expired paste accessed
   - User not found

5. **Conflict Errors (409)**
   - Concurrent modification conflicts
   - Custom URL conflicts

6. **Rate Limiting (429)**
   - Too many requests
   - Paste creation limits exceeded

### Error Recovery Strategies

- **Operational Transform Conflicts**: Implement automatic conflict resolution using operational transformation algorithms
- **WebSocket Disconnections**: Automatic reconnection with exponential backoff
- **Encryption Key Loss**: Clear error messaging with recovery options
- **Storage Failures**: Graceful degradation with temporary storage fallback

## Testing Strategy

### Unit Testing

**Frontend Testing:**
- React component testing with React Testing Library
- Encryption service testing with mock Web Crypto API
- Operational transform logic testing
- WebSocket client testing with mock servers

**Backend Testing:**
- Service layer unit tests with mocked dependencies
- Repository pattern testing with test databases
- WebSocket handler testing with mock connections
- Operational transform server-side logic

### Integration Testing

- API endpoint testing with test database
- WebSocket real-time communication testing
- End-to-end paste creation and collaboration flows
- Authentication and authorization flows
- File upload and storage integration

### Performance Testing

- Load testing for concurrent users on same paste
- WebSocket connection scaling tests
- Database query performance under load
- Client-side encryption/decryption performance
- Memory usage testing for large pastes

### Security Testing

- Penetration testing for encrypted pastes
- XSS and injection vulnerability scanning
- Rate limiting effectiveness testing
- Access control verification
- Data leakage prevention testing

### Accessibility Testing

- Screen reader compatibility testing
- Keyboard navigation testing
- Color contrast validation
- ARIA attribute verification
- Focus management testing

## Security Considerations

### Client-Side Encryption
- AES-GCM encryption with 256-bit keys
- Key derivation using PBKDF2 or Argon2
- Keys never transmitted to server
- Secure key sharing via URL fragments

### Access Control
- JWT-based authentication with refresh tokens
- Role-based access control for administrative features
- Rate limiting per user and IP address
- CORS configuration for API security

### Data Protection
- Input sanitization and validation
- SQL injection prevention with parameterized queries
- XSS prevention with content security policy
- Secure headers implementation

### Audit Logging
- Comprehensive access logging for watermarked pastes
- Security event logging and monitoring
- GDPR compliance for user data handling
- Data retention policies implementation