/**
 * Client-side encryption service using Web Crypto API
 * Implements AES-GCM encryption for zero-knowledge paste encryption
 */

export interface EncryptedData {
  encryptedContent: string;
  iv: string;
  salt: string;
}

export interface EncryptionKey {
  key: CryptoKey;
  exportedKey: string;
}

export class EncryptionService {
  private static readonly ALGORITHM = 'AES-GCM';
  private static readonly KEY_LENGTH = 256;
  private static readonly IV_LENGTH = 12; // 96 bits for GCM
  private static readonly SALT_LENGTH = 16; // 128 bits

  /**
   * Generate a new AES-GCM encryption key
   */
  static async generateKey(): Promise<EncryptionKey> {
    const key = await crypto.subtle.generateKey(
      {
        name: this.ALGORITHM,
        length: this.KEY_LENGTH,
      },
      true, // extractable
      ['encrypt', 'decrypt']
    );

    const exportedKey = await this.exportKey(key);
    
    return {
      key,
      exportedKey
    };
  }

  /**
   * Export a CryptoKey to a base64 string for sharing
   */
  static async exportKey(key: CryptoKey): Promise<string> {
    const exported = await crypto.subtle.exportKey('raw', key);
    return this.arrayBufferToBase64(exported);
  }

  /**
   * Import a key from a base64 string
   */
  static async importKey(keyData: string): Promise<CryptoKey> {
    const keyBuffer = this.base64ToArrayBuffer(keyData);
    
    return await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      {
        name: this.ALGORITHM,
        length: this.KEY_LENGTH,
      },
      true,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Encrypt content using AES-GCM
   */
  static async encrypt(content: string, key: CryptoKey): Promise<EncryptedData> {
    // Generate random IV and salt
    const iv = crypto.getRandomValues(new Uint8Array(this.IV_LENGTH));
    const salt = crypto.getRandomValues(new Uint8Array(this.SALT_LENGTH));
    
    // Convert content to ArrayBuffer
    const contentBuffer = new TextEncoder().encode(content);
    
    // Encrypt the content
    const encryptedBuffer = await crypto.subtle.encrypt(
      {
        name: this.ALGORITHM,
        iv: iv,
      },
      key,
      contentBuffer
    );

    return {
      encryptedContent: this.arrayBufferToBase64(encryptedBuffer),
      iv: this.arrayBufferToBase64(iv.buffer),
      salt: this.arrayBufferToBase64(salt.buffer),
    };
  }

  /**
   * Decrypt content using AES-GCM
   */
  static async decrypt(encryptedData: EncryptedData, key: CryptoKey): Promise<string> {
    try {
      // Convert base64 strings back to ArrayBuffers
      const encryptedBuffer = this.base64ToArrayBuffer(encryptedData.encryptedContent);
      const iv = this.base64ToArrayBuffer(encryptedData.iv);
      
      // Decrypt the content
      const decryptedBuffer = await crypto.subtle.decrypt(
        {
          name: this.ALGORITHM,
          iv: iv,
        },
        key,
        encryptedBuffer
      );

      // Convert back to string
      return new TextDecoder().decode(decryptedBuffer);
    } catch (error) {
      throw new Error('Failed to decrypt content. Invalid key or corrupted data.');
    }
  }

  /**
   * Generate a key from a password using PBKDF2
   */
  static async deriveKeyFromPassword(password: string, salt?: Uint8Array): Promise<EncryptionKey> {
    const passwordBuffer = new TextEncoder().encode(password);
    const saltBuffer = salt || crypto.getRandomValues(new Uint8Array(this.SALT_LENGTH));
    
    // Import password as key material
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      passwordBuffer,
      'PBKDF2',
      false,
      ['deriveKey']
    );
    
    // Derive key using PBKDF2
    const key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: saltBuffer as BufferSource,
        iterations: 100000,
        hash: 'SHA-256',
      },
      keyMaterial,
      {
        name: this.ALGORITHM,
        length: this.KEY_LENGTH,
      },
      true,
      ['encrypt', 'decrypt']
    );

    const exportedKey = await this.exportKey(key);
    
    return {
      key,
      exportedKey
    };
  }

  /**
   * Extract encryption key from URL fragment
   */
  static extractKeyFromUrl(): string | null {
    const fragment = window.location.hash.substring(1);
    if (fragment.startsWith('key=')) {
      return decodeURIComponent(fragment.substring(4));
    }
    return null;
  }

  /**
   * Add encryption key to URL fragment
   */
  static addKeyToUrl(key: string): string {
    const currentUrl = new URL(window.location.href);
    currentUrl.hash = `key=${encodeURIComponent(key)}`;
    return currentUrl.toString();
  }

  /**
   * Remove encryption key from URL fragment
   */
  static removeKeyFromUrl(): void {
    if (window.location.hash.startsWith('#key=')) {
      history.replaceState(null, '', window.location.pathname + window.location.search);
    }
  }

  /**
   * Check if Web Crypto API is supported
   */
  static isSupported(): boolean {
    return typeof crypto !== 'undefined' && 
           typeof crypto.subtle !== 'undefined' &&
           typeof crypto.getRandomValues !== 'undefined';
  }

  // Utility methods for base64 conversion
  private static arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  private static base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
  }
}

export default EncryptionService;