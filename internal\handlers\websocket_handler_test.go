package handlers

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"enhanced-pastebin/internal/services"
	websocketPkg "enhanced-pastebin/internal/websocket"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
)

func TestWebSocketHandler_GetRoomStats(t *testing.T) {
	// Create a new hub
	hub := websocketPkg.NewHub()
	collaborationService := services.NewCollaborationService()
	handler := NewWebSocketHandler(hub, collaborationService)

	// Create a test router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/ws/stats", handler.GetRoomStats)

	// Create a test request
	req, _ := http.NewRequest("GET", "/ws/stats", nil)
	w := httptest.NewRecorder()

	// Perform the request
	router.ServeHTTP(w, req)

	// Assert the response
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), "total_clients")
	assert.Contains(t, w.Body.String(), "total_rooms")
	assert.Contains(t, w.Body.String(), "room_stats")
}

func TestWebSocketHandler_HandleWebSocket(t *testing.T) {
	// Create a new hub and start it
	hub := websocketPkg.NewHub()
	go hub.Run()

	collaborationService := services.NewCollaborationService()
	handler := NewWebSocketHandler(hub, collaborationService)

	// Create a test server
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/ws/connect", handler.HandleWebSocket)

	server := httptest.NewServer(router)
	defer server.Close()

	// Convert HTTP URL to WebSocket URL
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/ws/connect"

	// Test WebSocket connection
	dialer := websocket.Dialer{}
	conn, _, err := dialer.Dial(wsURL, nil)
	assert.NoError(t, err)
	defer conn.Close()

	// Test sending a join message
	joinMessage := map[string]interface{}{
		"type":      "join",
		"paste_id":  "test-paste-123",
		"timestamp": time.Now().Format(time.RFC3339),
	}

	err = conn.WriteJSON(joinMessage)
	assert.NoError(t, err)

	// Wait a bit for message processing
	time.Sleep(100 * time.Millisecond)

	// Test sending a leave message
	leaveMessage := map[string]interface{}{
		"type":      "leave",
		"paste_id":  "test-paste-123",
		"timestamp": time.Now().Format(time.RFC3339),
	}

	err = conn.WriteJSON(leaveMessage)
	assert.NoError(t, err)

	// Wait a bit for message processing
	time.Sleep(100 * time.Millisecond)

	// The test passes if we can successfully connect and send messages
	// without errors
}

func TestWebSocketHandler_HandleWebSocket_WithAuth(t *testing.T) {
	// Create a new hub and start it
	hub := websocketPkg.NewHub()
	go hub.Run()

	collaborationService := services.NewCollaborationService()
	handler := NewWebSocketHandler(hub, collaborationService)

	// Create a test server
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/ws/connect", handler.HandleWebSocket)

	server := httptest.NewServer(router)
	defer server.Close()

	// Convert HTTP URL to WebSocket URL
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/ws/connect"

	// Test WebSocket connection with Authorization header
	headers := http.Header{}
	headers.Set("Authorization", "Bearer invalid-token")

	dialer := websocket.Dialer{}
	conn, _, err := dialer.Dial(wsURL, headers)
	assert.NoError(t, err) // Should still connect even with invalid token
	defer conn.Close()

	// The connection should work even with invalid auth
	// (anonymous users are allowed)
}