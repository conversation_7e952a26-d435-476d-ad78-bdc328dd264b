package models

import (
	"encoding/json"
	"time"

	"github.com/gorilla/websocket"
)

// MessageType represents different types of WebSocket messages
type MessageType string

const (
	MessageTypeJoin           MessageType = "join"
	MessageTypeLeave          MessageType = "leave"
	MessageTypePresence       MessageType = "presence"
	MessageTypeBroadcast      MessageType = "broadcast"
	MessageTypeError          MessageType = "error"
	MessageTypeChat           MessageType = "chat"
	MessageTypeChatDelete     MessageType = "chat_delete"
	MessageTypeOperation      MessageType = "operation"
	MessageTypeCursor         MessageType = "cursor"
	MessageTypeSelection      MessageType = "selection"
	MessageTypeSync           MessageType = "sync"
	MessageTypeAck            MessageType = "ack"
	MessageTypeConflict       MessageType = "conflict"
	MessageTypeDocumentState  MessageType = "document_state"
)

// WebSocketMessage represents a message sent over WebSocket
type WebSocketMessage struct {
	Type      MessageType     `json:"type"`
	PasteID   string          `json:"paste_id,omitempty"`
	UserID    string          `json:"user_id,omitempty"`
	Username  string          `json:"username,omitempty"`
	Data      json.RawMessage `json:"data,omitempty"`
	Timestamp time.Time       `json:"timestamp"`
}

// Client represents a WebSocket client connection
type Client struct {
	ID       string
	UserID   string
	Username string
	Conn     *websocket.Conn
	Send     chan []byte
	Hub      *Hub
	Rooms    map[string]bool // Track which rooms this client is in
}

// Hub maintains the set of active clients and broadcasts messages to them
type Hub struct {
	// Registered clients by room (paste ID)
	Rooms map[string]map[*Client]bool

	// All registered clients
	Clients map[*Client]bool

	// Inbound messages from the clients
	Broadcast chan []byte

	// Register requests from the clients
	Register chan *Client

	// Unregister requests from clients
	Unregister chan *Client

	// Room-specific broadcasts
	RoomBroadcast chan RoomMessage
}

// RoomMessage represents a message to be broadcast to a specific room
type RoomMessage struct {
	Room    string `json:"room"`
	Message []byte `json:"message"`
}

// ActiveUser represents a user currently active in a paste
type ActiveUser struct {
	UserID    string    `json:"user_id"`
	Username  string    `json:"username"`
	JoinedAt  time.Time `json:"joined_at"`
	LastSeen  time.Time `json:"last_seen"`
}

// PresenceData represents presence information for a room
type PresenceData struct {
	ActiveUsers []ActiveUser `json:"active_users"`
	UserCount   int          `json:"user_count"`
}