import { useEffect, useRef, useState, useCallback } from 'react';
import { CollaborativeEditor } from '../services/collaborativeEditor';
import { useWebSocket } from './useWebSocket';
import { CollaboratorState } from '../types/websocket';

interface UseCollaborativeEditorOptions {
  pasteId: string;
  initialContent: string;
  enabled?: boolean;
}

interface UseCollaborativeEditorReturn {
  content: string;
  version: number;
  collaborators: CollaboratorState[];
  isReady: boolean;
  error: string | null;
  insertText: (position: number, text: string) => void;
  deleteText: (position: number, length: number) => void;
  updateCursor: (line: number, column: number, offset: number) => void;
  updateSelection: (startLine: number, startColumn: number, startOffset: number, 
                   endLine: number, endColumn: number, endOffset: number) => void;
}

export const useCollaborativeEditor = (
  options: UseCollaborativeEditorOptions
): UseCollaborativeEditorReturn => {
  const { pasteId, initialContent, enabled = true } = options;
  
  const editorRef = useRef<CollaborativeEditor | null>(null);
  const [content, setContent] = useState(initialContent);
  const [version, setVersion] = useState(0);
  const [collaborators, setCollaborators] = useState<CollaboratorState[]>([]);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { isConnected, websocketClient } = useWebSocket({
    pasteId,
    autoConnect: enabled
  });

  // Initialize collaborative editor when WebSocket is connected
  useEffect(() => {
    if (!enabled || !isConnected || !websocketClient) {
      return;
    }

    const editor = new CollaborativeEditor({
      pasteId,
      initialContent,
      websocketClient,
      onContentChange: (newContent: string) => {
        setContent(newContent);
        setVersion(editorRef.current?.getVersion() || 0);
      },
      onCollaboratorsChange: (newCollaborators: CollaboratorState[]) => {
        setCollaborators(newCollaborators);
      },
      onError: (errorMsg: string) => {
        setError(errorMsg);
        console.error('Collaborative editor error:', errorMsg);
      }
    });

    editorRef.current = editor;
    editor.initialize();
    setIsReady(true);

    return () => {
      editor.destroy();
      editorRef.current = null;
      setIsReady(false);
    };
  }, [pasteId, initialContent, isConnected, websocketClient, enabled]);

  // Clear error after some time
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  const insertText = useCallback((position: number, text: string) => {
    if (editorRef.current && isReady) {
      editorRef.current.insertText(position, text);
    }
  }, [isReady]);

  const deleteText = useCallback((position: number, length: number) => {
    if (editorRef.current && isReady) {
      editorRef.current.deleteText(position, length);
    }
  }, [isReady]);

  const updateCursor = useCallback((line: number, column: number, offset: number) => {
    if (editorRef.current && isReady) {
      editorRef.current.updateCursor({ line, column, offset });
    }
  }, [isReady]);

  const updateSelection = useCallback((
    startLine: number, startColumn: number, startOffset: number,
    endLine: number, endColumn: number, endOffset: number
  ) => {
    if (editorRef.current && isReady) {
      editorRef.current.updateSelection({
        start: { line: startLine, column: startColumn, offset: startOffset },
        end: { line: endLine, column: endColumn, offset: endOffset }
      });
    }
  }, [isReady]);

  return {
    content,
    version,
    collaborators,
    isReady: isReady && isConnected,
    error,
    insertText,
    deleteText,
    updateCursor,
    updateSelection
  };
};