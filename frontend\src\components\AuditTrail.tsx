import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Shield, Eye, Clock, User, Globe, Activity } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { auditAPI, AccessLog, WatermarkInfo } from '@/lib/api';

interface AuditTrailProps {
  pasteId: string;
  showWatermarks?: boolean;
}

export default function AuditTrail({ pasteId, showWatermarks = true }: AuditTrailProps) {
  const [accessLogs, setAccessLogs] = useState<AccessLog[]>([]);
  const [watermarks, setWatermarks] = useState<WatermarkInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'logs' | 'watermarks'>('logs');
  const { toast } = useToast();

  useEffect(() => {
    loadAuditData();
  }, [pasteId]);

  const loadAuditData = async () => {
    setIsLoading(true);
    try {
      // Load access logs
      const logsResponse = await auditAPI.getAccessLogs(pasteId);
      setAccessLogs(logsResponse.data.logs || []);

      // Load watermarks if enabled
      if (showWatermarks) {
        const watermarksResponse = await auditAPI.getWatermarks(pasteId);
        setWatermarks(watermarksResponse.data.watermarks || []);
      }
    } catch (error) {
      console.error('Error loading audit data:', error);
      toast({
        title: "Error",
        description: "Failed to load audit information",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getActionIcon = (action: string) => {
    switch (action.toLowerCase()) {
      case 'view':
        return <Eye className="h-4 w-4" />;
      case 'copy':
        return <Activity className="h-4 w-4" />;
      case 'security_event':
        return <Shield className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getActionBadgeVariant = (action: string) => {
    switch (action.toLowerCase()) {
      case 'view':
        return 'default';
      case 'copy':
        return 'secondary';
      case 'security_event':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const renderAccessLogs = () => (
    <div className="space-y-3">
      {accessLogs.length === 0 ? (
        <div className="text-center text-muted-foreground py-8">
          No access logs found
        </div>
      ) : (
        accessLogs.map((log) => (
          <div key={log.id} className="border rounded-lg p-4 space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getActionIcon(log.action)}
                <Badge variant={getActionBadgeVariant(log.action)}>
                  {log.action}
                </Badge>
                {log.username && (
                  <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                    <User className="h-3 w-3" />
                    <span>{log.username}</span>
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>{formatTimestamp(log.timestamp)}</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Globe className="h-3 w-3" />
                <span>{log.ip_address}</span>
              </div>
              {log.user_agent && (
                <div className="truncate max-w-xs">
                  <span title={log.user_agent}>
                    {log.user_agent.length > 50 
                      ? `${log.user_agent.substring(0, 50)}...` 
                      : log.user_agent}
                  </span>
                </div>
              )}
            </div>
            
            {log.details && (
              <div className="text-sm bg-muted p-2 rounded">
                <pre className="whitespace-pre-wrap text-xs">
                  {typeof log.details === 'string' 
                    ? log.details 
                    : JSON.stringify(log.details, null, 2)}
                </pre>
              </div>
            )}
          </div>
        ))
      )}
    </div>
  );

  const renderWatermarks = () => (
    <div className="space-y-3">
      {watermarks.length === 0 ? (
        <div className="text-center text-muted-foreground py-8">
          No watermarks found
        </div>
      ) : (
        watermarks.map((watermark) => (
          <div key={watermark.id} className="border rounded-lg p-4 space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-blue-500" />
                <Badge variant="outline">Watermark</Badge>
                <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                  <User className="h-3 w-3" />
                  <span>{watermark.username}</span>
                </div>
              </div>
              <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>{formatTimestamp(watermark.created_at)}</span>
              </div>
            </div>
            
            <div className="text-sm text-muted-foreground">
              <div>Watermark ID: <code className="bg-muted px-1 rounded">{watermark.watermark_id}</code></div>
            </div>
          </div>
        ))
      )}
    </div>
  );

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="text-center">Loading audit information...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mt-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Audit Trail
          </CardTitle>
          <div className="flex space-x-2">
            <Button
              variant={activeTab === 'logs' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('logs')}
            >
              Access Logs ({accessLogs.length})
            </Button>
            {showWatermarks && (
              <Button
                variant={activeTab === 'watermarks' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveTab('watermarks')}
              >
                Watermarks ({watermarks.length})
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <ScrollArea className="h-96">
          {activeTab === 'logs' ? renderAccessLogs() : renderWatermarks()}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
