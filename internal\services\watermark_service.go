package services

import (
	"crypto/md5"
	"fmt"
	"strings"
	"time"

	"enhanced-pastebin/internal/models"
	"enhanced-pastebin/internal/repositories"

	"github.com/google/uuid"
)

type WatermarkService interface {
	CreateWatermark(req *models.WatermarkRequest) (*models.WatermarkInfo, error)
	ApplyWatermark(content, userID, pasteID string) string
	ExtractWatermark(content string) (*WatermarkData, error)
	GetWatermarksByPaste(pasteID string) ([]*models.WatermarkInfo, error)
	GetWatermarksByUser(userID string) ([]*models.WatermarkInfo, error)
}

type WatermarkData struct {
	UserID      string    `json:"user_id"`
	PasteID     string    `json:"paste_id"`
	WatermarkID string    `json:"watermark_id"`
	Timestamp   time.Time `json:"timestamp"`
}

type watermarkService struct {
	watermarkRepo repositories.WatermarkRepository
}

func NewWatermarkService(watermarkRepo repositories.WatermarkRepository) WatermarkService {
	return &watermarkService{
		watermarkRepo: watermarkRepo,
	}
}

func (s *watermarkService) CreateWatermark(req *models.WatermarkRequest) (*models.WatermarkInfo, error) {
	watermarkID := s.generateWatermarkID(req.UserID, req.PasteID)
	
	watermark := &models.WatermarkInfo{
		ID:          uuid.New().String(),
		PasteID:     req.PasteID,
		UserID:      req.UserID,
		WatermarkID: watermarkID,
		CreatedAt:   time.Now(),
	}

	if err := s.watermarkRepo.Create(watermark); err != nil {
		return nil, err
	}

	// Get the watermark with username
	return s.watermarkRepo.GetByID(watermark.ID)
}

func (s *watermarkService) ApplyWatermark(content, userID, pasteID string) string {
	watermarkID := s.generateWatermarkID(userID, pasteID)
	timestamp := time.Now().Unix()
	
	// Create invisible watermark using zero-width characters
	watermark := s.encodeWatermark(watermarkID, timestamp)
	
	// Insert watermark at strategic locations in the content
	watermarkedContent := s.insertWatermark(content, watermark)
	
	return watermarkedContent
}

func (s *watermarkService) ExtractWatermark(content string) (*WatermarkData, error) {
	// Extract zero-width characters that form the watermark
	watermarkChars := s.extractWatermarkChars(content)
	if len(watermarkChars) == 0 {
		return nil, fmt.Errorf("no watermark found")
	}
	
	// Decode the watermark
	watermarkID, timestamp, err := s.decodeWatermark(watermarkChars)
	if err != nil {
		return nil, err
	}
	
	// Parse the watermark ID to extract user and paste information
	userID, pasteID, err := s.parseWatermarkID(watermarkID)
	if err != nil {
		return nil, err
	}
	
	return &WatermarkData{
		UserID:      userID,
		PasteID:     pasteID,
		WatermarkID: watermarkID,
		Timestamp:   time.Unix(timestamp, 0),
	}, nil
}

func (s *watermarkService) GetWatermarksByPaste(pasteID string) ([]*models.WatermarkInfo, error) {
	return s.watermarkRepo.GetByPasteID(pasteID)
}

func (s *watermarkService) GetWatermarksByUser(userID string) ([]*models.WatermarkInfo, error) {
	return s.watermarkRepo.GetByUserID(userID)
}

func (s *watermarkService) generateWatermarkID(userID, pasteID string) string {
	// Create a unique watermark ID based on user, paste, and timestamp
	data := fmt.Sprintf("%s:%s:%d", userID, pasteID, time.Now().Unix())
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x", hash)[:16] // Use first 16 characters
}

func (s *watermarkService) encodeWatermark(watermarkID string, timestamp int64) string {
	// Use zero-width characters to encode the watermark
	// Zero Width Space (U+200B), Zero Width Non-Joiner (U+200C), Zero Width Joiner (U+200D)
	zws := "\u200B"  // 0
	zwnj := "\u200C" // 1
	zwj := "\u200D"  // separator
	
	// Encode watermark ID and timestamp as binary using zero-width chars
	data := fmt.Sprintf("%s:%d", watermarkID, timestamp)
	encoded := ""
	
	for _, char := range data {
		if char == ':' {
			encoded += zwj
		} else {
			// Convert character to binary and encode
			binary := fmt.Sprintf("%08b", char)
			for _, bit := range binary {
				if bit == '0' {
					encoded += zws
				} else {
					encoded += zwnj
				}
			}
		}
	}
	
	return encoded
}

func (s *watermarkService) decodeWatermark(watermarkChars string) (string, int64, error) {
	zws := "\u200B"  // 0
	zwnj := "\u200C" // 1
	zwj := "\u200D"  // separator
	
	// Decode the zero-width characters back to the original data
	parts := strings.Split(watermarkChars, zwj)
	if len(parts) != 2 {
		return "", 0, fmt.Errorf("invalid watermark format")
	}
	
	// Decode watermark ID
	watermarkID := s.decodeBinaryString(parts[0], zws, zwnj)
	
	// Decode timestamp
	timestampStr := s.decodeBinaryString(parts[1], zws, zwnj)
	var timestamp int64
	if _, err := fmt.Sscanf(timestampStr, "%d", &timestamp); err != nil {
		return "", 0, fmt.Errorf("invalid timestamp in watermark")
	}
	
	return watermarkID, timestamp, nil
}

func (s *watermarkService) decodeBinaryString(encoded, zws, zwnj string) string {
	result := ""
	
	// Process in chunks of 8 (binary representation of each character)
	for i := 0; i < len(encoded); i += 8 {
		if i+8 > len(encoded) {
			break
		}
		
		chunk := encoded[i : i+8]
		binary := ""
		
		for j := 0; j < len(chunk); {
			if strings.HasPrefix(chunk[j:], zws) {
				binary += "0"
				j += len(zws)
			} else if strings.HasPrefix(chunk[j:], zwnj) {
				binary += "1"
				j += len(zwnj)
			} else {
				j++
			}
		}
		
		if len(binary) == 8 {
			var char rune
			fmt.Sscanf(binary, "%b", &char)
			result += string(char)
		}
	}
	
	return result
}

func (s *watermarkService) insertWatermark(content, watermark string) string {
	if len(content) == 0 {
		return watermark
	}
	
	lines := strings.Split(content, "\n")
	watermarkParts := s.splitWatermark(watermark, len(lines))
	
	// Insert watermark parts at the end of random lines
	for i, part := range watermarkParts {
		if i < len(lines) {
			lines[i] += part
		}
	}
	
	return strings.Join(lines, "\n")
}

func (s *watermarkService) splitWatermark(watermark string, parts int) []string {
	if parts <= 0 {
		return []string{watermark}
	}
	
	partSize := len(watermark) / parts
	if partSize == 0 {
		return []string{watermark}
	}
	
	var result []string
	for i := 0; i < len(watermark); i += partSize {
		end := i + partSize
		if end > len(watermark) {
			end = len(watermark)
		}
		result = append(result, watermark[i:end])
	}
	
	return result
}

func (s *watermarkService) extractWatermarkChars(content string) string {
	// Extract all zero-width characters from the content
	zws := "\u200B"
	zwnj := "\u200C"
	zwj := "\u200D"
	
	watermark := ""
	for _, char := range content {
		charStr := string(char)
		if charStr == zws || charStr == zwnj || charStr == zwj {
			watermark += charStr
		}
	}
	
	return watermark
}

func (s *watermarkService) parseWatermarkID(watermarkID string) (string, string, error) {
	// This is a simplified approach - in a real implementation,
	// you'd want to store the mapping in the database
	return "unknown", "unknown", fmt.Errorf("watermark ID parsing not implemented")
}
