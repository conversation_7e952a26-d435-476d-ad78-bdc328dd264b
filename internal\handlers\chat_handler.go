package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"

	"enhanced-pastebin/internal/models"
	"enhanced-pastebin/internal/services"
	websocketPkg "enhanced-pastebin/internal/websocket"

	"github.com/gin-gonic/gin"
)

type ChatHandler struct {
	chatService services.ChatService
	hub         *websocketPkg.Hub
}

func NewChatHandler(chatService services.ChatService, hub *websocketPkg.Hub) *ChatHandler {
	return &ChatHandler{
		chatService: chatService,
		hub:         hub,
	}
}

func (h *<PERSON>t<PERSON>and<PERSON>) CreateMessage(c *gin.Context) {
	var req models.CreateChatMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	message, err := h.chatService.CreateMessage(&req, userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Broadcast the new message to all clients in the paste room
	h.broadcastChatMessage(message)

	c.JSON(http.StatusCreated, message)
}

func (h *ChatHandler) GetMessages(c *gin.Context) {
	pasteID := c.Param("pasteId")
	
	// Parse pagination parameters
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")
	
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 50
	}
	
	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	messages, err := h.chatService.GetMessagesByPasteID(pasteID, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Get total count for pagination
	totalCount, err := h.chatService.GetMessageCount(pasteID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	response := gin.H{
		"messages":    messages,
		"total_count": totalCount,
		"limit":       limit,
		"offset":      offset,
	}

	c.JSON(http.StatusOK, response)
}

func (h *ChatHandler) DeleteMessage(c *gin.Context) {
	messageID := c.Param("messageId")
	
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get message to find paste ID for broadcasting
	message, err := h.chatService.GetMessageByID(messageID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Message not found"})
		return
	}

	err = h.chatService.DeleteMessage(messageID, userIDStr)
	if err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		return
	}

	// Broadcast message deletion to all clients in the paste room
	h.broadcastMessageDeletion(message.PasteID, messageID)

	c.JSON(http.StatusOK, gin.H{"message": "Message deleted successfully"})
}

func (h *ChatHandler) broadcastChatMessage(message *models.ChatMessage) {
	// Create WebSocket message for chat
	wsMessage := models.WebSocketMessage{
		Type:      models.MessageTypeChat,
		PasteID:   message.PasteID,
		UserID:    message.UserID,
		Username:  message.Username,
		Data:      mustMarshal(message),
		Timestamp: message.CreatedAt,
	}

	// Broadcast to all clients in the paste room
	if messageBytes, err := json.Marshal(wsMessage); err == nil {
		h.hub.BroadcastToRoom(message.PasteID, messageBytes)
	}
}

func (h *ChatHandler) broadcastMessageDeletion(pasteID, messageID string) {
	// Create WebSocket message for chat deletion
	wsMessage := models.WebSocketMessage{
		Type:    models.MessageTypeChatDelete,
		PasteID: pasteID,
		Data:    mustMarshal(gin.H{"message_id": messageID}),
	}

	// Broadcast to all clients in the paste room
	if messageBytes, err := json.Marshal(wsMessage); err == nil {
		h.hub.BroadcastToRoom(pasteID, messageBytes)
	}
}

func mustMarshal(v interface{}) []byte {
	data, err := json.Marshal(v)
	if err != nil {
		panic(err)
	}
	return data
}
