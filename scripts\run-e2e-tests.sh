#!/bin/bash

# Enhanced Pastebin E2E Test Runner
# This script sets up the environment and runs Playwright tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_PORT=8080
FRONTEND_PORT=3000
DATABASE_URL="postgres://postgres:password@localhost:5432/enhanced_pastebin?sslmode=disable"
JWT_SECRET="test-jwt-secret-key-for-e2e-tests"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" >/dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within timeout"
    return 1
}

# Function to cleanup processes
cleanup() {
    print_status "Cleaning up..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        print_status "Stopping backend server (PID: $BACKEND_PID)"
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        print_status "Stopping frontend server (PID: $FRONTEND_PID)"
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # Stop Docker services if we started them
    if [ "$STARTED_DOCKER" = "true" ]; then
        print_status "Stopping Docker services"
        docker-compose down >/dev/null 2>&1 || true
    fi
}

# Set up cleanup trap
trap cleanup EXIT

# Parse command line arguments
HEADLESS=true
UI_MODE=false
DEBUG_MODE=false
SPECIFIC_TEST=""
SKIP_SETUP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --headed)
            HEADLESS=false
            shift
            ;;
        --ui)
            UI_MODE=true
            shift
            ;;
        --debug)
            DEBUG_MODE=true
            shift
            ;;
        --test)
            SPECIFIC_TEST="$2"
            shift 2
            ;;
        --skip-setup)
            SKIP_SETUP=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --headed      Run tests in headed mode (show browser)"
            echo "  --ui          Run tests in UI mode"
            echo "  --debug       Run tests in debug mode"
            echo "  --test FILE   Run specific test file"
            echo "  --skip-setup  Skip environment setup"
            echo "  --help        Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "Starting Enhanced Pastebin E2E Tests"

# Check prerequisites
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    exit 1
fi

if ! command -v go &> /dev/null; then
    print_error "Go is not installed"
    exit 1
fi

if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed"
    exit 1
fi

# Setup environment if not skipped
if [ "$SKIP_SETUP" = "false" ]; then
    print_status "Setting up environment..."
    
    # Check if database services are running
    if ! check_port 5432; then
        print_status "Starting database services..."
        docker-compose up -d postgres redis
        STARTED_DOCKER=true
        
        # Wait for PostgreSQL to be ready
        wait_for_service "postgresql://postgres:password@localhost:5432/enhanced_pastebin" "PostgreSQL"
    else
        print_status "Database services already running"
    fi
    
    # Run database migrations
    print_status "Running database migrations..."
    export DATABASE_URL="$DATABASE_URL"
    go run cmd/migrate/main.go up
    
    # Check if backend is running
    if ! check_port $BACKEND_PORT; then
        print_status "Starting backend server..."
        export DATABASE_URL="$DATABASE_URL"
        export JWT_SECRET="$JWT_SECRET"
        export PORT="$BACKEND_PORT"
        export GIN_MODE="release"
        
        go run cmd/server/main.go &
        BACKEND_PID=$!
        
        wait_for_service "http://localhost:$BACKEND_PORT/api/v1/health" "Backend server"
    else
        print_status "Backend server already running"
    fi
    
    # Install frontend dependencies if needed
    if [ ! -d "frontend/node_modules" ]; then
        print_status "Installing frontend dependencies..."
        cd frontend
        npm install
        cd ..
    fi
    
    # Install Playwright browsers if needed
    if [ ! -d "frontend/node_modules/@playwright" ]; then
        print_status "Installing Playwright browsers..."
        cd frontend
        npx playwright install
        cd ..
    fi
else
    print_status "Skipping environment setup"
fi

# Change to frontend directory
cd frontend

# Build test command
TEST_CMD="npx playwright test"

if [ "$UI_MODE" = "true" ]; then
    TEST_CMD="$TEST_CMD --ui"
elif [ "$DEBUG_MODE" = "true" ]; then
    TEST_CMD="$TEST_CMD --debug"
elif [ "$HEADLESS" = "false" ]; then
    TEST_CMD="$TEST_CMD --headed"
fi

if [ ! -z "$SPECIFIC_TEST" ]; then
    TEST_CMD="$TEST_CMD $SPECIFIC_TEST"
fi

# Run tests
print_status "Running Playwright tests..."
print_status "Command: $TEST_CMD"

if eval $TEST_CMD; then
    print_success "All tests passed!"
    exit 0
else
    print_error "Some tests failed!"
    
    # Show report if available
    if [ -f "playwright-report/index.html" ]; then
        print_status "Test report available at: frontend/playwright-report/index.html"
        print_status "Run 'npm run test:e2e:report' to view the report"
    fi
    
    exit 1
fi
