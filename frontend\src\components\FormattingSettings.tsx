
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useFormatting } from '@/contexts/FormattingContext'
import { Settings, RotateCcw } from 'lucide-react'

interface FormattingSettingsProps {
  isOpen: boolean
  onClose: () => void
}

export default function FormattingSettings({ isOpen, onClose }: FormattingSettingsProps) {
  const { settings, updateSettings, updateFormatOptions, resetToDefaults } = useFormatting()

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Code Formatting Settings
              </CardTitle>
              <CardDescription>
                Configure how your code is formatted and linted
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={onClose}>
              ×
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* General Settings */}
          <div>
            <h3 className="text-lg font-semibold mb-3">General Settings</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Auto-format on save</label>
                  <p className="text-xs text-muted-foreground">
                    Automatically format code when saving pastes
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.autoFormatOnSave}
                  onChange={(e) => updateSettings({ autoFormatOnSave: e.target.checked })}
                  className="rounded"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Show linting hints</label>
                  <p className="text-xs text-muted-foreground">
                    Display syntax errors and warnings in the editor
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.showLintingHints}
                  onChange={(e) => updateSettings({ showLintingHints: e.target.checked })}
                  className="rounded"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Format on paste</label>
                  <p className="text-xs text-muted-foreground">
                    Automatically format code when pasting content
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.formatOnPaste}
                  onChange={(e) => updateSettings({ formatOnPaste: e.target.checked })}
                  className="rounded"
                />
              </div>
            </div>
          </div>

          {/* Format Options */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Format Options</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Tab Width</label>
                <input
                  type="range"
                  min="2"
                  max="8"
                  value={settings.formatOptions.tabWidth}
                  onChange={(e) => updateFormatOptions({ tabWidth: parseInt(e.target.value) })}
                  className="w-full mt-1"
                />
                <div className="text-xs text-muted-foreground mt-1">
                  {settings.formatOptions.tabWidth} spaces
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Use tabs</label>
                  <p className="text-xs text-muted-foreground">Use tabs instead of spaces</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.formatOptions.useTabs}
                  onChange={(e) => updateFormatOptions({ useTabs: e.target.checked })}
                  className="rounded"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Semicolons</label>
                  <p className="text-xs text-muted-foreground">Add semicolons at end of statements</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.formatOptions.semicolons}
                  onChange={(e) => updateFormatOptions({ semicolons: e.target.checked })}
                  className="rounded"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Single quotes</label>
                  <p className="text-xs text-muted-foreground">Use single quotes instead of double</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.formatOptions.singleQuote}
                  onChange={(e) => updateFormatOptions({ singleQuote: e.target.checked })}
                  className="rounded"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Trailing Comma</label>
                <select
                  value={settings.formatOptions.trailingComma}
                  onChange={(e) => updateFormatOptions({ trailingComma: e.target.value as 'none' | 'es5' | 'all' })}
                  className="w-full mt-1 flex h-8 rounded-md border border-input bg-background px-2 py-1 text-xs"
                >
                  <option value="none">None</option>
                  <option value="es5">ES5</option>
                  <option value="all">All</option>
                </select>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Bracket spacing</label>
                  <p className="text-xs text-muted-foreground">Add spaces inside object brackets</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.formatOptions.bracketSpacing}
                  onChange={(e) => updateFormatOptions({ bracketSpacing: e.target.checked })}
                  className="rounded"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Arrow Parentheses</label>
                <select
                  value={settings.formatOptions.arrowParens}
                  onChange={(e) => updateFormatOptions({ arrowParens: e.target.value as 'avoid' | 'always' })}
                  className="w-full mt-1 flex h-8 rounded-md border border-input bg-background px-2 py-1 text-xs"
                >
                  <option value="avoid">Avoid</option>
                  <option value="always">Always</option>
                </select>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-between pt-4 border-t">
            <Button
              variant="outline"
              onClick={resetToDefaults}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset to Defaults
            </Button>
            <div className="space-x-2">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={onClose}>
                Save Settings
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}