package services

import (
	"context"
	"fmt"
	"log"
	"time"

	"enhanced-pastebin/internal/repositories"
)

type CleanupService interface {
	StartCleanupWorker(ctx context.Context)
	CleanupExpiredPastes() error
	GetExpiringPastes(hours int) ([]ExpiringPaste, error)
}

type ExpiringPaste struct {
	ID           string    `json:"id"`
	Title        string    `json:"title"`
	ExpiresAt    *time.Time `json:"expires_at,omitempty"`
	ViewCount    int       `json:"view_count"`
	MaxViews     *int      `json:"max_views,omitempty"`
	TimeToExpiry string    `json:"time_to_expiry"`
	ViewsLeft    *int      `json:"views_left,omitempty"`
}

type cleanupService struct {
	pasteRepo repositories.PasteRepository
	chatRepo  repositories.ChatRepository
}

func NewCleanupService(pasteRepo repositories.PasteRepository, chatRepo repositories.ChatRepository) CleanupService {
	return &cleanupService{
		pasteRepo: pasteRepo,
		chatRepo:  chatRepo,
	}
}

func (s *cleanupService) StartCleanupWorker(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Hour) // Run cleanup every hour
	defer ticker.Stop()

	log.Println("Starting cleanup worker...")

	for {
		select {
		case <-ctx.Done():
			log.Println("Cleanup worker stopped")
			return
		case <-ticker.C:
			if err := s.CleanupExpiredPastes(); err != nil {
				log.Printf("Error during cleanup: %v", err)
			}
		}
	}
}

func (s *cleanupService) CleanupExpiredPastes() error {
	log.Println("Starting cleanup of expired pastes...")

	// Get all expired pastes
	expiredPastes, err := s.pasteRepo.GetExpiredPastes()
	if err != nil {
		return err
	}

	if len(expiredPastes) == 0 {
		log.Println("No expired pastes found")
		return nil
	}

	log.Printf("Found %d expired pastes to clean up", len(expiredPastes))

	for _, paste := range expiredPastes {
		if err := s.cleanupPasteData(paste.ID); err != nil {
			log.Printf("Error cleaning up paste %s: %v", paste.ID, err)
			continue
		}
		log.Printf("Successfully cleaned up paste %s", paste.ID)
	}

	log.Printf("Cleanup completed. Removed %d expired pastes", len(expiredPastes))
	return nil
}

func (s *cleanupService) cleanupPasteData(pasteID string) error {
	// Delete associated chat messages first
	if err := s.chatRepo.DeleteByPasteID(pasteID); err != nil {
		log.Printf("Warning: Failed to delete chat messages for paste %s: %v", pasteID, err)
		// Continue with paste deletion even if chat cleanup fails
	}

	// Delete the paste itself
	return s.pasteRepo.Delete(pasteID)
}

func (s *cleanupService) GetExpiringPastes(hours int) ([]ExpiringPaste, error) {
	// Get pastes that will expire within the specified hours
	cutoffTime := time.Now().Add(time.Duration(hours) * time.Hour)
	
	pastes, err := s.pasteRepo.GetExpiringPastes(cutoffTime)
	if err != nil {
		return nil, err
	}

	var expiringPastes []ExpiringPaste
	now := time.Now()

	for _, paste := range pastes {
		expiringPaste := ExpiringPaste{
			ID:        paste.ID,
			Title:     paste.Title,
			ExpiresAt: paste.ExpiresAt,
			ViewCount: paste.ViewCount,
			MaxViews:  paste.MaxViews,
		}

		// Calculate time to expiry
		if paste.ExpiresAt != nil {
			timeLeft := paste.ExpiresAt.Sub(now)
			if timeLeft > 0 {
				expiringPaste.TimeToExpiry = formatDuration(timeLeft)
			} else {
				expiringPaste.TimeToExpiry = "Expired"
			}
		}

		// Calculate views left
		if paste.MaxViews != nil {
			viewsLeft := *paste.MaxViews - paste.ViewCount
			if viewsLeft > 0 {
				expiringPaste.ViewsLeft = &viewsLeft
			} else {
				viewsLeft = 0
				expiringPaste.ViewsLeft = &viewsLeft
			}
		}

		expiringPastes = append(expiringPastes, expiringPaste)
	}

	return expiringPastes, nil
}

func formatDuration(d time.Duration) string {
	if d < time.Hour {
		return d.Round(time.Minute).String()
	}
	if d < 24*time.Hour {
		return d.Round(time.Hour).String()
	}
	days := int(d.Hours() / 24)
	if days == 1 {
		return "1 day"
	}
	return fmt.Sprintf("%d days", days)
}
