import { 
  WebSocketMessage, 
  WebSocketClientOptions, 
  WebSocketEventHandlers,
  PresenceData,
  ActiveUser,
  Operation,
  CursorPosition,
  Selection,
  DocumentState,
  OperationResult
} from '../types/websocket';

export class WebSocketClient {
  private ws: WebSocket | null = null;
  private url: string;
  private token?: string;
  private reconnectInterval: number;
  private maxReconnectAttempts: number;
  private reconnectAttempts: number = 0;
  private reconnectTimeoutId: number | null = null;
  private isConnecting: boolean = false;
  private isManuallyDisconnected: boolean = false;
  private currentRooms: Set<string> = new Set();
  
  private handlers: WebSocketEventHandlers = {};

  constructor(options: WebSocketClientOptions) {
    this.url = options.url;
    this.token = options.token;
    this.reconnectInterval = options.reconnectInterval || 3000;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
  }

  // Set event handlers
  public setHandlers(handlers: WebSocketEventHandlers): void {
    this.handlers = { ...this.handlers, ...handlers };
  }

  // Connect to WebSocket server
  public connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        reject(new Error('Connection already in progress'));
        return;
      }

      this.isConnecting = true;
      this.isManuallyDisconnected = false;

      try {
        // Build WebSocket URL with token if available
        let wsUrl = this.url;
        if (this.token) {
          const separator = wsUrl.includes('?') ? '&' : '?';
          wsUrl += `${separator}token=${encodeURIComponent(this.token)}`;
        }

        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          
          // Rejoin all rooms after reconnection
          this.rejoinRooms();
          
          this.handlers.onConnect?.();
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.ws = null;
          
          this.handlers.onDisconnect?.();

          // Attempt to reconnect if not manually disconnected
          if (!this.isManuallyDisconnected && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          this.handlers.onError?.(error);
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  // Disconnect from WebSocket server
  public disconnect(): void {
    this.isManuallyDisconnected = true;
    
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.currentRooms.clear();
  }

  // Join a room (paste)
  public joinRoom(pasteId: string): void {
    if (!this.isConnected()) {
      console.warn('Cannot join room: WebSocket not connected');
      return;
    }

    this.currentRooms.add(pasteId);
    
    const message: WebSocketMessage = {
      type: 'join',
      paste_id: pasteId,
      timestamp: new Date().toISOString()
    };

    this.send(message);
  }

  // Leave a room (paste)
  public leaveRoom(pasteId: string): void {
    if (!this.isConnected()) {
      console.warn('Cannot leave room: WebSocket not connected');
      return;
    }

    this.currentRooms.delete(pasteId);
    
    const message: WebSocketMessage = {
      type: 'leave',
      paste_id: pasteId,
      timestamp: new Date().toISOString()
    };

    this.send(message);
  }

  // Broadcast a message to current rooms
  public broadcast(data: any): void {
    if (!this.isConnected()) {
      console.warn('Cannot broadcast: WebSocket not connected');
      return;
    }

    const message: WebSocketMessage = {
      type: 'broadcast',
      data,
      timestamp: new Date().toISOString()
    };

    this.send(message);
  }

  // Send a text operation for collaborative editing
  public sendOperation(operation: Operation): void {
    if (!this.isConnected()) {
      console.warn('Cannot send operation: WebSocket not connected');
      return;
    }

    const message: WebSocketMessage = {
      type: 'operation',
      paste_id: operation.paste_id,
      data: operation,
      timestamp: new Date().toISOString()
    };

    this.send(message);
  }

  // Send cursor position update
  public sendCursorUpdate(pasteId: string, cursor: CursorPosition): void {
    if (!this.isConnected()) {
      console.warn('Cannot send cursor update: WebSocket not connected');
      return;
    }

    const message: WebSocketMessage = {
      type: 'cursor',
      paste_id: pasteId,
      data: cursor,
      timestamp: new Date().toISOString()
    };

    this.send(message);
  }

  // Send selection update
  public sendSelectionUpdate(pasteId: string, selection: Selection): void {
    if (!this.isConnected()) {
      console.warn('Cannot send selection update: WebSocket not connected');
      return;
    }

    const message: WebSocketMessage = {
      type: 'selection',
      paste_id: pasteId,
      data: selection,
      timestamp: new Date().toISOString()
    };

    this.send(message);
  }

  // Request document synchronization
  public requestSync(pasteId: string): void {
    if (!this.isConnected()) {
      console.warn('Cannot request sync: WebSocket not connected');
      return;
    }

    const message: WebSocketMessage = {
      type: 'sync',
      paste_id: pasteId,
      timestamp: new Date().toISOString()
    };

    this.send(message);
  }

  // Check if WebSocket is connected
  public isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  // Get current connection state
  public getConnectionState(): string {
    if (!this.ws) return 'CLOSED';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING';
      case WebSocket.OPEN: return 'OPEN';
      case WebSocket.CLOSING: return 'CLOSING';
      case WebSocket.CLOSED: return 'CLOSED';
      default: return 'UNKNOWN';
    }
  }

  // Update authentication token
  public updateToken(token: string): void {
    this.token = token;
    
    // Reconnect with new token if currently connected
    if (this.isConnected()) {
      this.disconnect();
      setTimeout(() => this.connect(), 100);
    }
  }

  // Private methods

  private send(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('Cannot send message: WebSocket not connected');
    }
  }

  private handleMessage(message: WebSocketMessage): void {
    console.log('Received WebSocket message:', message);

    switch (message.type) {
      case 'presence':
        if (message.data) {
          const presenceData: PresenceData = message.data;
          this.handlers.onPresenceUpdate?.(presenceData);
        }
        break;

      case 'join':
        if (message.user_id && message.username) {
          const user: ActiveUser = {
            user_id: message.user_id,
            username: message.username,
            joined_at: message.timestamp,
            last_seen: message.timestamp
          };
          this.handlers.onUserJoin?.(user);
        }
        break;

      case 'leave':
        if (message.user_id) {
          this.handlers.onUserLeave?.(message.user_id);
        }
        break;

      case 'broadcast':
        this.handlers.onMessage?.(message);
        break;

      case 'operation':
        if (message.data) {
          const operation: Operation = message.data;
          this.handlers.onOperation?.(operation);
        }
        break;

      case 'cursor':
        if (message.user_id && message.data) {
          const cursor: CursorPosition = message.data;
          this.handlers.onCursorUpdate?.(message.user_id, cursor);
        }
        break;

      case 'selection':
        if (message.user_id && message.data) {
          const selection: Selection = message.data;
          this.handlers.onSelectionUpdate?.(message.user_id, selection);
        }
        break;

      case 'document_state':
        if (message.data) {
          const state: DocumentState = message.data;
          this.handlers.onDocumentState?.(state);
        }
        break;

      case 'ack':
        if (message.data) {
          const result: OperationResult = message.data;
          this.handlers.onOperationAck?.(result);
        }
        break;

      case 'error':
        console.error('WebSocket server error:', message.data);
        break;

      default:
        console.warn('Unknown message type:', message.type);
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
    }

    this.reconnectAttempts++;
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);

    this.reconnectTimeoutId = window.setTimeout(() => {
      this.handlers.onReconnect?.(this.reconnectAttempts);
      this.connect().catch((error) => {
        console.error('Reconnect failed:', error);
      });
    }, delay);
  }

  private rejoinRooms(): void {
    // Rejoin all rooms after reconnection
    for (const pasteId of this.currentRooms) {
      const message: WebSocketMessage = {
        type: 'join',
        paste_id: pasteId,
        timestamp: new Date().toISOString()
      };
      this.send(message);
    }
  }
}