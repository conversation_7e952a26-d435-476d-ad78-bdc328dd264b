.PHONY: help dev setup migrate-up migrate-down build clean test test-backend test-frontend test-integration lint format docker-build docker-run security-scan

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

setup: ## Set up the development environment
	@echo "Setting up development environment..."
	@cp .env.example .env
	@cp frontend/.env.example frontend/.env
	@echo "Starting database..."
	@docker-compose up -d postgres redis
	@echo "Installing Go dependencies..."
	@go mod tidy
	@echo "Installing frontend dependencies..."
	@cd frontend && npm install
	@echo "Setup complete! Run 'make dev' to start development servers"

migrate-up: ## Run database migrations
	@echo "Running database migrations..."
	@go run cmd/migrate/main.go up

migrate-down: ## Rollback database migrations
	@echo "Rolling back database migrations..."
	@go run cmd/migrate/main.go down

dev: ## Start development servers
	@echo "Starting development servers..."
	@make -j2 dev-backend dev-frontend

dev-backend: ## Start backend development server
	@echo "Starting Go backend..."
	@go run cmd/server/main.go

dev-frontend: ## Start frontend development server
	@echo "Starting React frontend..."
	@cd frontend && npm run dev

build: ## Build the application
	@echo "Building backend..."
	@go build -o bin/server cmd/server/main.go
	@echo "Building frontend..."
	@cd frontend && npm run build

test: test-backend test-frontend ## Run all tests

test-backend: ## Run backend tests with coverage
	@echo "Running Go tests with coverage..."
	@go test -v -race -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "Backend tests completed. Coverage report: coverage.html"

test-frontend: ## Run frontend tests
	@echo "Running frontend tests..."
	@cd frontend && npm test -- --coverage --watchAll=false

test-integration: ## Run integration tests
	@echo "Starting integration test environment..."
	@docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit
	@docker-compose -f docker-compose.test.yml down

clean: ## Clean up build artifacts and stop services
	@echo "Cleaning up..."
	@rm -rf bin/
	@rm -rf frontend/dist/
	@docker-compose down

db-reset: ## Reset the database (WARNING: This will delete all data)
	@echo "Resetting database..."
	@docker-compose down postgres
	@docker volume rm enhanced-pastebin_postgres_data || true
	@docker-compose up -d postgres
	@sleep 5
	@make migrate-up

# Code quality commands
lint: lint-backend lint-frontend ## Run linting for both backend and frontend

lint-backend: ## Run backend linting
	@echo "Running backend linting..."
	@golangci-lint run ./... || echo "Install golangci-lint: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"

lint-frontend: ## Run frontend linting
	@echo "Running frontend linting..."
	@cd frontend && npm run lint

format: format-backend format-frontend ## Format code for both backend and frontend

format-backend: ## Format backend code
	@echo "Formatting backend code..."
	@gofmt -w .

format-frontend: ## Format frontend code
	@echo "Formatting frontend code..."
	@cd frontend && npm run format

# Docker commands
docker-build: ## Build Docker images
	@echo "Building Docker images..."
	@docker-compose build

docker-run: ## Run application with Docker
	@echo "Starting application with Docker..."
	@docker-compose up -d

docker-logs: ## View Docker logs
	@docker-compose logs -f

# Security commands
security-scan: ## Run security scans
	@echo "Running security scans..."
	@gosec ./... || echo "Install gosec: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest"
	@cd frontend && npm audit

# Development utilities
install-tools: ## Install development tools
	@echo "Installing development tools..."
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	@echo "Development tools installed."

# Health check
health: ## Check application health
	@echo "Checking application health..."
	@curl -f http://localhost:8080/api/v1/health || echo "Backend not responding"
	@curl -f http://localhost:3000 || echo "Frontend not responding"