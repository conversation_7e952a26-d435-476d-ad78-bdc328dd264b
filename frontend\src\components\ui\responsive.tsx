import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

export function ResponsiveContainer({ 
  children, 
  className, 
  maxWidth = 'lg' 
}: ResponsiveContainerProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-full',
  };

  return (
    <div className={cn(
      'mx-auto px-4 sm:px-6 lg:px-8',
      maxWidthClasses[maxWidth],
      className
    )}>
      {children}
    </div>
  );
}

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: number;
}

export function ResponsiveGrid({ 
  children, 
  className, 
  cols = { default: 1, md: 2, lg: 3 },
  gap = 4 
}: ResponsiveGridProps) {
  const gridClasses = [];
  
  if (cols.default) gridClasses.push(`grid-cols-${cols.default}`);
  if (cols.sm) gridClasses.push(`sm:grid-cols-${cols.sm}`);
  if (cols.md) gridClasses.push(`md:grid-cols-${cols.md}`);
  if (cols.lg) gridClasses.push(`lg:grid-cols-${cols.lg}`);
  if (cols.xl) gridClasses.push(`xl:grid-cols-${cols.xl}`);

  return (
    <div className={cn(
      'grid',
      `gap-${gap}`,
      ...gridClasses,
      className
    )}>
      {children}
    </div>
  );
}

interface ResponsiveStackProps {
  children: React.ReactNode;
  className?: string;
  direction?: 'vertical' | 'horizontal';
  breakpoint?: 'sm' | 'md' | 'lg';
  spacing?: number;
}

export function ResponsiveStack({ 
  children, 
  className, 
  direction = 'vertical',
  breakpoint = 'md',
  spacing = 4 
}: ResponsiveStackProps) {
  const baseClasses = direction === 'vertical' 
    ? `flex flex-col space-y-${spacing}` 
    : `flex flex-row space-x-${spacing}`;
    
  const responsiveClasses = direction === 'vertical'
    ? `${breakpoint}:flex-row ${breakpoint}:space-y-0 ${breakpoint}:space-x-${spacing}`
    : `${breakpoint}:flex-col ${breakpoint}:space-x-0 ${breakpoint}:space-y-${spacing}`;

  return (
    <div className={cn(baseClasses, responsiveClasses, className)}>
      {children}
    </div>
  );
}

// Hook for responsive breakpoints
export function useBreakpoint() {
  const [breakpoint, setBreakpoint] = React.useState<'sm' | 'md' | 'lg' | 'xl' | '2xl'>('md');

  React.useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 640) setBreakpoint('sm');
      else if (width < 768) setBreakpoint('md');
      else if (width < 1024) setBreakpoint('lg');
      else if (width < 1280) setBreakpoint('xl');
      else setBreakpoint('2xl');
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return {
    breakpoint,
    isSm: breakpoint === 'sm',
    isMd: breakpoint === 'md',
    isLg: breakpoint === 'lg',
    isXl: breakpoint === 'xl',
    is2Xl: breakpoint === '2xl',
    isMobile: breakpoint === 'sm' || breakpoint === 'md',
    isDesktop: breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl',
  };
}

// Responsive text component
interface ResponsiveTextProps {
  children: React.ReactNode;
  className?: string;
  size?: {
    default?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl';
    sm?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl';
    md?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl';
    lg?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl';
  };
}

export function ResponsiveText({ 
  children, 
  className, 
  size = { default: 'base', lg: 'lg' } 
}: ResponsiveTextProps) {
  const sizeClasses = [];
  
  if (size.default) sizeClasses.push(`text-${size.default}`);
  if (size.sm) sizeClasses.push(`sm:text-${size.sm}`);
  if (size.md) sizeClasses.push(`md:text-${size.md}`);
  if (size.lg) sizeClasses.push(`lg:text-${size.lg}`);

  return (
    <span className={cn(...sizeClasses, className)}>
      {children}
    </span>
  );
}

// Show/hide components based on breakpoint
interface ShowProps {
  children: React.ReactNode;
  above?: 'sm' | 'md' | 'lg' | 'xl';
  below?: 'sm' | 'md' | 'lg' | 'xl';
}

export function Show({ children, above, below }: ShowProps) {
  let className = '';
  
  if (above) {
    className = `hidden ${above}:block`;
  } else if (below) {
    className = `block ${below}:hidden`;
  }

  return <div className={className}>{children}</div>;
}
