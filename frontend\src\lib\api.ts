import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api/v1'

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export interface User {
  id: string
  username: string
  email: string
  reputation_score: number
  created_at: string
  last_active: string
}

export interface Paste {
  id: string
  custom_url?: string
  title: string
  content: string
  language: string
  is_encrypted: boolean
  expires_at?: string
  view_count: number
  max_views?: number
  is_watermarked: boolean
  created_at: string
  updated_at: string
  user_id?: string
}

export interface CreatePasteRequest {
  custom_url?: string
  title: string
  content: string
  language: string
  is_encrypted: boolean
  expires_at?: string
  max_views?: number
  is_watermarked: boolean
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
}

export interface LoginResponse {
  token: string
  user: User
}

export interface ChatMessage {
  id: string
  paste_id: string
  user_id: string
  username: string
  content: string
  line_references: number[]
  created_at: string
}

export interface CreateChatMessageRequest {
  paste_id: string
  content: string
  line_references?: number[]
}

export interface ChatMessagesResponse {
  messages: ChatMessage[]
  total_count: number
  limit: number
  offset: number
}

export interface UserStats {
  total_pastes: number
  total_views: number
  most_used_language: string
  language_count: { [key: string]: number }
}

export interface UserProfile {
  user: User
  stats: UserStats
  recent_pastes: Paste[]
}

export interface PublicUserProfile {
  username: string
  reputation_score: number
  created_at: string
  last_active: string
  stats: UserStats
  recent_pastes: Paste[]
}

export interface UserPastesResponse {
  pastes: Paste[]
  limit: number
  offset: number
}

// Auth API
export const authAPI = {
  login: (data: LoginRequest) => api.post<LoginResponse>('/users/login', data),
  register: (data: RegisterRequest) => api.post<User>('/users/register', data),
}

// User API
export const userAPI = {
  getProfile: () => api.get<UserProfile>('/users/profile'),
  getPublicProfile: (username: string) => api.get<PublicUserProfile>(`/users/${username}/public`),
  getUserPastes: (limit = 20, offset = 0) =>
    api.get<UserPastesResponse>(`/users/profile/pastes?limit=${limit}&offset=${offset}`),
}

// Paste API
export const pasteAPI = {
  create: (data: CreatePasteRequest) => api.post<Paste>('/pastes', data),
  get: (id: string) => api.get<Paste>(`/pastes/${id}`),
  getByCustomURL: (customURL: string) => api.get<Paste>(`/p/${customURL}`),
  update: (id: string, data: Partial<CreatePasteRequest>) => api.put<Paste>(`/pastes/${id}`, data),
  delete: (id: string) => api.delete(`/pastes/${id}`),
  checkCustomURLAvailability: (url: string) => api.get<{available: boolean}>(`/pastes/check-url?url=${encodeURIComponent(url)}`),
}

// Chat API
export const chatAPI = {
  createMessage: (data: CreateChatMessageRequest) => api.post<ChatMessage>('/chat/messages', data),
  getMessages: (pasteId: string, limit = 50, offset = 0) =>
    api.get<ChatMessagesResponse>(`/chat/pastes/${pasteId}/messages?limit=${limit}&offset=${offset}`),
  deleteMessage: (messageId: string) => api.delete(`/chat/messages/${messageId}`),
}

export interface ExpiringPaste {
  id: string
  title: string
  expires_at?: string
  view_count: number
  max_views?: number
  time_to_expiry: string
  views_left?: number
}

export interface ExpiringPastesResponse {
  expiring_pastes: ExpiringPaste[]
  hours: number
  count: number
}

// Expiration API
export const expirationAPI = {
  getExpiringPastes: (hours = 24) =>
    api.get<ExpiringPastesResponse>(`/expiration/expiring?hours=${hours}`),
  triggerCleanup: () => api.post('/expiration/cleanup'),
}

export interface AccessLog {
  id: string
  paste_id: string
  user_id?: string
  username?: string
  ip_address: string
  user_agent: string
  action: string
  details?: string
  timestamp: string
}

export interface WatermarkInfo {
  id: string
  paste_id: string
  user_id: string
  username: string
  watermark_id: string
  created_at: string
}

export interface AccessLogsResponse {
  logs: AccessLog[]
  total_count: number
  limit: number
  offset: number
}

export interface WatermarksResponse {
  watermarks: WatermarkInfo[]
}

export interface SecurityEventsResponse {
  events: AccessLog[]
  limit: number
  offset: number
}

export interface PasteVersion {
  id: string
  paste_id: string
  version_number: number
  content: string
  created_at: string
  created_by?: string
  created_by_username?: string
}

export interface DiffLine {
  type: 'added' | 'removed' | 'unchanged'
  line_num: number
  content: string
}

export interface VersionComparison {
  version1: PasteVersion
  version2: PasteVersion
  differences: DiffLine[]
}

export interface VersionsResponse {
  versions: PasteVersion[]
  total_count: number
  limit: number
  offset: number
}

// Version API
export const versionAPI = {
  getVersions: (pasteId: string, limit = 20, offset = 0) =>
    api.get<VersionsResponse>(`/versions/pastes/${pasteId}?limit=${limit}&offset=${offset}`),
  getVersion: (versionId: string) =>
    api.get<PasteVersion>(`/versions/${versionId}`),
  getLatestVersion: (pasteId: string) =>
    api.get<PasteVersion>(`/versions/pastes/${pasteId}/latest`),
  compareVersions: (version1Id: string, version2Id: string) =>
    api.get<VersionComparison>(`/versions/compare?version1=${version1Id}&version2=${version2Id}`),
}

// Audit API
export const auditAPI = {
  getAccessLogs: (pasteId: string, limit = 50, offset = 0) =>
    api.get<AccessLogsResponse>(`/audit/pastes/${pasteId}/logs?limit=${limit}&offset=${offset}`),
  getUserAccessLogs: (limit = 50, offset = 0) =>
    api.get<AccessLogsResponse>(`/audit/user/logs?limit=${limit}&offset=${offset}`),
  getSecurityEvents: (limit = 50, offset = 0) =>
    api.get<SecurityEventsResponse>(`/audit/security-events?limit=${limit}&offset=${offset}`),
  getWatermarks: (pasteId: string) =>
    api.get<WatermarksResponse>(`/audit/pastes/${pasteId}/watermarks`),
  getUserWatermarks: () =>
    api.get<WatermarksResponse>(`/audit/user/watermarks`),
  extractWatermark: (content: string) =>
    api.post(`/audit/watermark/extract`, { content }),
}