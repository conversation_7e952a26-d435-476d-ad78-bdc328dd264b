import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Code, User, LogOut, Plus, Menu, X } from 'lucide-react'
import { useKeyboardNavigation } from '@/hooks/useKeyboardNavigation'
import AccessibilityChecker from '@/components/AccessibilityChecker'
import PerformanceMonitor from '@/components/PerformanceMonitor'

interface LayoutProps {
  children: React.ReactNode
}

export default function Layout({ children }: LayoutProps) {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const handleLogout = () => {
    logout()
    navigate('/')
    setIsMobileMenuOpen(false)
  }

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false)
  }

  // Close mobile menu on escape key
  useKeyboardNavigation({
    onEscape: () => setIsMobileMenuOpen(false),
    enabled: isMobileMenuOpen,
  })

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link
              to="/"
              className="flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-md p-1"
              onClick={closeMobileMenu}
            >
              <Code className="h-6 w-6" />
              <span className="text-xl font-bold">Enhanced Pastebin</span>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-4">
              {user ? (
                <>
                  <Button asChild variant="ghost">
                    <Link to="/create">
                      <Plus className="h-4 w-4 mr-2" />
                      New Paste
                    </Link>
                  </Button>
                  <Button asChild variant="ghost">
                    <Link to="/profile">
                      <User className="h-4 w-4 mr-2" />
                      Profile
                    </Link>
                  </Button>
                  <Button variant="ghost" onClick={handleLogout}>
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </Button>
                </>
              ) : (
                <>
                  <Button asChild variant="ghost">
                    <Link to="/login">Login</Link>
                  </Button>
                  <Button asChild>
                    <Link to="/register">Register</Link>
                  </Button>
                </>
              )}
            </nav>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle menu"
              aria-expanded={isMobileMenuOpen}
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>

          {/* Mobile Navigation */}
          {isMobileMenuOpen && (
            <nav className="md:hidden mt-4 pb-4 border-t pt-4">
              <div className="flex flex-col space-y-2">
                {user ? (
                  <>
                    <Button asChild variant="ghost" className="justify-start">
                      <Link to="/create" onClick={closeMobileMenu}>
                        <Plus className="h-4 w-4 mr-2" />
                        New Paste
                      </Link>
                    </Button>
                    <Button asChild variant="ghost" className="justify-start">
                      <Link to="/profile" onClick={closeMobileMenu}>
                        <User className="h-4 w-4 mr-2" />
                        Profile
                      </Link>
                    </Button>
                    <Button variant="ghost" onClick={handleLogout} className="justify-start">
                      <LogOut className="h-4 w-4 mr-2" />
                      Logout
                    </Button>
                  </>
                ) : (
                  <>
                    <Button asChild variant="ghost" className="justify-start">
                      <Link to="/login" onClick={closeMobileMenu}>Login</Link>
                    </Button>
                    <Button asChild className="justify-start">
                      <Link to="/register" onClick={closeMobileMenu}>Register</Link>
                    </Button>
                  </>
                )}
              </div>
            </nav>
          )}
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        {children}
      </main>

      {/* Development-only tools */}
      <AccessibilityChecker />
      <PerformanceMonitor />
    </div>
  )
}