import { chromium, FullConfig } from '@playwright/test';
import { spawn, ChildProcess } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

let backendProcess: ChildProcess | null = null;
let dbProcess: ChildProcess | null = null;

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup...');

  try {
    // Start database services using docker-compose
    console.log('📦 Starting database services...');
    await startDatabaseServices();

    // Wait for database to be ready
    await waitForDatabase();

    // Run database migrations
    console.log('🔄 Running database migrations...');
    await runMigrations();

    // Start backend server
    console.log('🖥️ Starting backend server...');
    await startBackendServer();

    // Wait for backend to be ready
    await waitForBackend();

    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    await cleanup();
    throw error;
  }
}

async function startDatabaseServices(): Promise<void> {
  return new Promise((resolve, reject) => {
    const projectRoot = path.resolve(__dirname, '../../..');
    
    dbProcess = spawn('docker-compose', ['up', '-d', 'postgres', 'redis'], {
      cwd: projectRoot,
      stdio: 'pipe',
      shell: true
    });

    let output = '';
    dbProcess.stdout?.on('data', (data) => {
      output += data.toString();
    });

    dbProcess.stderr?.on('data', (data) => {
      output += data.toString();
    });

    dbProcess.on('close', (code) => {
      if (code === 0) {
        console.log('Database services started successfully');
        resolve();
      } else {
        reject(new Error(`Database services failed to start: ${output}`));
      }
    });

    dbProcess.on('error', (error) => {
      reject(new Error(`Failed to start database services: ${error.message}`));
    });
  });
}

async function waitForDatabase(): Promise<void> {
  const maxAttempts = 30;
  const delay = 2000;

  for (let i = 0; i < maxAttempts; i++) {
    try {
      // Try to connect to PostgreSQL
      const { spawn } = require('child_process');
      const result = await new Promise<boolean>((resolve) => {
        const proc = spawn('docker', [
          'exec',
          'fullstack-pastify-postgres-1',
          'pg_isready',
          '-U', 'postgres'
        ], { stdio: 'pipe' });

        proc.on('close', (code) => {
          resolve(code === 0);
        });

        proc.on('error', () => {
          resolve(false);
        });
      });

      if (result) {
        console.log('Database is ready');
        return;
      }
    } catch (error) {
      // Continue trying
    }

    console.log(`Waiting for database... (attempt ${i + 1}/${maxAttempts})`);
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  throw new Error('Database failed to become ready within timeout');
}

async function runMigrations(): Promise<void> {
  return new Promise((resolve, reject) => {
    const projectRoot = path.resolve(__dirname, '../../..');
    
    const migrationProcess = spawn('go', ['run', 'cmd/migrate/main.go', 'up'], {
      cwd: projectRoot,
      stdio: 'pipe',
      shell: true,
      env: {
        ...process.env,
        DATABASE_URL: 'postgres://postgres:password@localhost:5432/enhanced_pastebin?sslmode=disable'
      }
    });

    let output = '';
    migrationProcess.stdout?.on('data', (data) => {
      output += data.toString();
    });

    migrationProcess.stderr?.on('data', (data) => {
      output += data.toString();
    });

    migrationProcess.on('close', (code) => {
      if (code === 0) {
        console.log('Database migrations completed successfully');
        resolve();
      } else {
        reject(new Error(`Database migrations failed: ${output}`));
      }
    });

    migrationProcess.on('error', (error) => {
      reject(new Error(`Failed to run migrations: ${error.message}`));
    });
  });
}

async function startBackendServer(): Promise<void> {
  return new Promise((resolve, reject) => {
    const projectRoot = path.resolve(__dirname, '../../..');
    
    backendProcess = spawn('go', ['run', 'cmd/server/main.go'], {
      cwd: projectRoot,
      stdio: 'pipe',
      shell: true,
      env: {
        ...process.env,
        DATABASE_URL: 'postgres://postgres:password@localhost:5432/enhanced_pastebin?sslmode=disable',
        JWT_SECRET: 'test-jwt-secret-key-for-playwright-tests',
        PORT: '8080',
        GIN_MODE: 'release'
      }
    });

    let output = '';
    backendProcess.stdout?.on('data', (data) => {
      const text = data.toString();
      output += text;
      if (text.includes('Server starting on port 8080')) {
        console.log('Backend server started successfully');
        resolve();
      }
    });

    backendProcess.stderr?.on('data', (data) => {
      output += data.toString();
    });

    backendProcess.on('close', (code) => {
      if (code !== 0 && code !== null) {
        reject(new Error(`Backend server failed to start: ${output}`));
      }
    });

    backendProcess.on('error', (error) => {
      reject(new Error(`Failed to start backend server: ${error.message}`));
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      if (backendProcess && !backendProcess.killed) {
        reject(new Error('Backend server failed to start within timeout'));
      }
    }, 30000);
  });
}

async function waitForBackend(): Promise<void> {
  const maxAttempts = 30;
  const delay = 1000;

  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch('http://localhost:8080/api/v1/health');
      if (response.ok) {
        console.log('Backend server is ready');
        return;
      }
    } catch (error) {
      // Continue trying
    }

    console.log(`Waiting for backend... (attempt ${i + 1}/${maxAttempts})`);
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  throw new Error('Backend server failed to become ready within timeout');
}

async function cleanup(): Promise<void> {
  console.log('🧹 Cleaning up processes...');

  if (backendProcess && !backendProcess.killed) {
    backendProcess.kill('SIGTERM');
    await new Promise(resolve => setTimeout(resolve, 2000));
    if (!backendProcess.killed) {
      backendProcess.kill('SIGKILL');
    }
  }

  // Note: We don't stop the database here as it might be needed for other tests
  // The global teardown will handle database cleanup
}

export default globalSetup;
