package models

import (
	"time"

	"github.com/lib/pq"
)

type ChatMessage struct {
	ID             string         `json:"id" db:"id"`
	PasteID        string         `json:"paste_id" db:"paste_id"`
	UserID         string         `json:"user_id" db:"user_id"`
	Content        string         `json:"content" db:"content"`
	LineReferences pq.Int64Array  `json:"line_references" db:"line_references"`
	CreatedAt      time.Time      `json:"created_at" db:"created_at"`
	
	// Joined fields for display
	Username       string         `json:"username,omitempty" db:"username"`
}

type CreateChatMessageRequest struct {
	PasteID        string  `json:"paste_id" binding:"required"`
	Content        string  `json:"content" binding:"required,max=1000"`
	LineReferences []int64 `json:"line_references,omitempty"`
}

type ChatMessageResponse struct {
	ID             string    `json:"id"`
	PasteID        string    `json:"paste_id"`
	UserID         string    `json:"user_id"`
	Username       string    `json:"username"`
	Content        string    `json:"content"`
	LineReferences []int64   `json:"line_references"`
	CreatedAt      time.Time `json:"created_at"`
}