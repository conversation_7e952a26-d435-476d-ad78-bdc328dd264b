import { test, expect } from '@playwright/test';
import { LoginPage, HomePage, RegisterPage } from '../../pages';
import { TestHelpers } from '../../utils/test-helpers';

test.describe('User Login', () => {
  let loginPage: LoginPage;
  let homePage: HomePage;
  let registerPage: RegisterPage;
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    homePage = new HomePage(page);
    registerPage = new RegisterPage(page);
    helpers = new TestHelpers();
    
    await loginPage.visit();
  });

  test.afterEach(async () => {
    await helpers.cleanupTestData();
  });

  test('should display login form', async () => {
    await loginPage.expectToBeOnLoginPage();
    await loginPage.expectLoginForm();
    await loginPage.expectRegisterLinkVisible();
  });

  test('should login with valid credentials', async () => {
    // Create a test user first
    const user = await helpers.api.createTestUser();

    await loginPage.loginAndWaitForRedirect(user.username, user.password);
    
    // Should be redirected to home page and logged in
    await homePage.expectToBeOnHomePage();
    await loginPage.expectToBeLoggedIn(user.username);
  });

  test('should show validation errors for empty form', async () => {
    await loginPage.submitForm();
    await loginPage.expectValidationErrors();
  });

  test('should show error for invalid credentials', async () => {
    await loginPage.login('nonexistentuser', 'wrongpassword');
    await loginPage.expectInvalidCredentialsError();
    await loginPage.expectLoginFailure();
  });

  test('should show error for empty username', async () => {
    await loginPage.fillPassword('password');
    await loginPage.submitForm();
    await loginPage.expectUsernameError(/required|empty/i);
  });

  test('should show error for empty password', async () => {
    await loginPage.fillUsername('username');
    await loginPage.submitForm();
    await loginPage.expectPasswordError(/required|empty/i);
  });

  test('should navigate to register page when clicking register link', async () => {
    await loginPage.clickRegisterLink();
    await registerPage.expectToBeOnRegisterPage();
  });

  test('should handle remember me functionality', async () => {
    const user = await helpers.api.createTestUser();

    await loginPage.fillUsername(user.username);
    await loginPage.fillPassword(user.password);
    await loginPage.checkRememberMe();
    await loginPage.submitForm();

    await homePage.expectToBeOnHomePage();
    await loginPage.expectToBeLoggedIn(user.username);

    // Verify remember me was checked
    const formData = await loginPage.getFormData();
    expect(formData.rememberMe).toBe(true);
  });

  test('should clear form when requested', async () => {
    await loginPage.fillUsername('testuser');
    await loginPage.fillPassword('password');

    await loginPage.clearForm();

    const formData = await loginPage.getFormData();
    expect(formData.username).toBe('');
    expect(formData.password).toBe('');
  });

  test('should validate form completion', async () => {
    // Empty form should be invalid
    expect(await loginPage.isFormValid()).toBe(false);

    // Username only should be invalid
    await loginPage.fillUsername('testuser');
    expect(await loginPage.isFormValid()).toBe(false);

    // Complete form should be valid
    await loginPage.fillPassword('password');
    expect(await loginPage.isFormValid()).toBe(true);
  });

  test('should handle case-insensitive username', async () => {
    const user = await helpers.api.createTestUser({
      username: 'TestUser123'
    });

    // Try logging in with different case
    await loginPage.loginAndWaitForRedirect('testuser123', user.password);
    
    await homePage.expectToBeOnHomePage();
    await loginPage.expectToBeLoggedIn();
  });

  test('should handle network errors gracefully', async ({ page }) => {
    // Simulate network failure
    await page.route('**/api/v1/users/login', route => route.abort());

    await loginPage.login('testuser', 'password');
    await loginPage.expectErrorMessage(/network|connection|failed/i);
  });

  test('should handle server errors gracefully', async ({ page }) => {
    // Simulate server error
    await page.route('**/api/v1/users/login', route => 
      route.fulfill({ status: 500, body: JSON.stringify({ error: 'Internal server error' }) })
    );

    await loginPage.login('testuser', 'password');
    await loginPage.expectErrorMessage(/server error|internal error/i);
  });

  test('should handle rate limiting', async ({ page }) => {
    // Simulate rate limiting
    await page.route('**/api/v1/users/login', route => 
      route.fulfill({ 
        status: 429, 
        body: JSON.stringify({ error: 'Too many login attempts' }),
        headers: { 'Retry-After': '60' }
      })
    );

    await loginPage.login('testuser', 'password');
    await loginPage.expectErrorMessage(/too many attempts|rate limit/i);
  });

  test('should redirect to intended page after login', async ({ page }) => {
    // Try to access a protected page while logged out
    await page.goto('/profile');
    
    // Should be redirected to login page
    await loginPage.expectToBeOnLoginPage();

    // Login with valid credentials
    const user = await helpers.api.createTestUser();
    await loginPage.login(user.username, user.password);

    // Should be redirected to the originally intended page
    await expect(page).toHaveURL('/profile');
  });

  test('should handle account lockout', async ({ page }) => {
    // Simulate account lockout
    await page.route('**/api/v1/users/login', route => 
      route.fulfill({ 
        status: 423, 
        body: JSON.stringify({ error: 'Account locked due to too many failed attempts' })
      })
    );

    const user = await helpers.api.createTestUser();
    await loginPage.login(user.username, user.password);
    await loginPage.expectAccountLockedError();
  });

  test('should show forgot password link', async () => {
    await loginPage.expectForgotPasswordLinkVisible();
  });

  test('should navigate to forgot password page', async () => {
    await loginPage.clickForgotPasswordLink();
    // Should navigate to forgot password page
    await expect(loginPage.page).toHaveURL(/\/forgot|\/reset/);
  });

  test('should persist login state across page refreshes', async ({ page }) => {
    const user = await helpers.api.createTestUser();
    
    await loginPage.loginAndWaitForRedirect(user.username, user.password);
    await loginPage.expectToBeLoggedIn(user.username);

    // Refresh the page
    await page.reload();
    
    // Should still be logged in
    await loginPage.expectToBeLoggedIn(user.username);
  });

  test('should handle concurrent login sessions', async ({ browser }) => {
    const user = await helpers.api.createTestUser();

    // Login in first tab
    const context1 = await browser.newContext();
    const page1 = await context1.newPage();
    const loginPage1 = new LoginPage(page1);
    await loginPage1.visit();
    await loginPage1.loginAndWaitForRedirect(user.username, user.password);

    // Login in second tab
    const context2 = await browser.newContext();
    const page2 = await context2.newPage();
    const loginPage2 = new LoginPage(page2);
    await loginPage2.visit();
    await loginPage2.loginAndWaitForRedirect(user.username, user.password);

    // Both sessions should be valid
    await loginPage1.expectToBeLoggedIn(user.username);
    await loginPage2.expectToBeLoggedIn(user.username);

    await context1.close();
    await context2.close();
  });
});
