package models

import "time"

// SecurityVulnerability represents a security vulnerability found in a dependency
type SecurityVulnerability struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Severity    string    `json:"severity"` // critical, high, moderate, low
	CVSS        float64   `json:"cvss,omitempty"`
	CVE         string    `json:"cve,omitempty"`
	CWE         string    `json:"cwe,omitempty"`
	URL         string    `json:"url,omitempty"`
	PublishedAt time.Time `json:"published_at"`
	PatchedIn   string    `json:"patched_in,omitempty"`
}

// DependencySecurityInfo represents security information for a dependency
type DependencySecurityInfo struct {
	PackageName     string                   `json:"package_name"`
	Version         string                   `json:"version,omitempty"`
	Vulnerabilities []SecurityVulnerability  `json:"vulnerabilities"`
	SecurityScore   int                      `json:"security_score"` // 0-100, higher is better
	LastScanned     time.Time                `json:"last_scanned"`
	HasVulns        bool                     `json:"has_vulnerabilities"`
}



// NPMAuditResponse represents the response from npm audit
type NPMAuditResponse struct {
	Vulnerabilities map[string]NPMVulnerability `json:"vulnerabilities"`
	Metadata        NPMAuditMetadata            `json:"metadata"`
}

type NPMVulnerability struct {
	Name     string                 `json:"name"`
	Severity string                 `json:"severity"`
	Via      []NPMVulnerabilityVia  `json:"via"`
	Effects  []string               `json:"effects"`
	Range    string                 `json:"range"`
	Nodes    []string               `json:"nodes"`
	FixAvailable interface{}        `json:"fixAvailable"`
}

type NPMVulnerabilityVia struct {
	Source int    `json:"source,omitempty"`
	Name   string `json:"name,omitempty"`
	Dependency string `json:"dependency,omitempty"`
	Title  string `json:"title,omitempty"`
	URL    string `json:"url,omitempty"`
	Severity string `json:"severity,omitempty"`
	CWE    []string `json:"cwe,omitempty"`
	CVSS   struct {
		Score  float64 `json:"score"`
		Vector string  `json:"vectorString"`
	} `json:"cvss,omitempty"`
	Range string `json:"range,omitempty"`
}

type NPMAuditMetadata struct {
	Vulnerabilities struct {
		Info     int `json:"info"`
		Low      int `json:"low"`
		Moderate int `json:"moderate"`
		High     int `json:"high"`
		Critical int `json:"critical"`
		Total    int `json:"total"`
	} `json:"vulnerabilities"`
	Dependencies struct {
		Prod         int `json:"prod"`
		Dev          int `json:"dev"`
		Optional     int `json:"optional"`
		Peer         int `json:"peer"`
		PeerOptional int `json:"peerOptional"`
		Total        int `json:"total"`
	} `json:"dependencies"`
}

// PyPISecurityResponse represents security data from PyPI or safety databases
type PyPISecurityResponse struct {
	Package         string                    `json:"package"`
	Vulnerabilities []PyPIVulnerability       `json:"vulnerabilities"`
}

type PyPIVulnerability struct {
	ID          string   `json:"id"`
	Specs       []string `json:"specs"`
	V           string   `json:"v"`
	Advisory    string   `json:"advisory"`
	CVE         string   `json:"cve,omitempty"`
	More_info_path string `json:"more_info_path,omitempty"`
}

// GoVulnResponse represents security data from Go vulnerability database
type GoVulnResponse struct {
	Vulns []GoVulnerability `json:"vulns"`
}

type GoVulnerability struct {
	ID       string `json:"id"`
	Details  string `json:"details"`
	Aliases  []string `json:"aliases"`
	Published time.Time `json:"published"`
	Modified  time.Time `json:"modified"`
	Withdrawn *time.Time `json:"withdrawn,omitempty"`
	Affected []struct {
		Package struct {
			Name      string `json:"name"`
			Ecosystem string `json:"ecosystem"`
		} `json:"package"`
		Ranges []struct {
			Type   string `json:"type"`
			Events []struct {
				Introduced string `json:"introduced,omitempty"`
				Fixed      string `json:"fixed,omitempty"`
			} `json:"events"`
		} `json:"ranges"`
		Versions []string `json:"versions,omitempty"`
		DatabaseSpecific interface{} `json:"database_specific,omitempty"`
		EcosystemSpecific interface{} `json:"ecosystem_specific,omitempty"`
	} `json:"affected"`
	References []struct {
		Type string `json:"type"`
		URL  string `json:"url"`
	} `json:"references"`
	DatabaseSpecific interface{} `json:"database_specific,omitempty"`
}