import { test, expect } from '@playwright/test';
import { ViewPastePage, CreatePastePage, HomePage } from '../../pages';
import { TestHelpers } from '../../utils/test-helpers';

test.describe('Edit and Delete Paste', () => {
  let viewPastePage: ViewPastePage;
  let createPastePage: CreatePastePage;
  let homePage: HomePage;
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    viewPastePage = new ViewPastePage(page);
    createPastePage = new CreatePastePage(page);
    homePage = new HomePage(page);
    helpers = new TestHelpers();
  });

  test.afterEach(async () => {
    await helpers.cleanupTestData();
  });

  test('should edit paste successfully', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste({
      title: 'Original Title',
      content: 'Original content',
      language: 'javascript'
    });

    await viewPastePage.visit(paste.id!);
    await viewPastePage.editPaste();

    // Should be on edit page
    await expect(page).toHaveURL(`/paste/${paste.id}/edit`);

    // Update the paste
    await createPastePage.fillTitle('Updated Title');
    await createPastePage.fillContent('Updated content');
    await createPastePage.selectLanguage('python');
    await createPastePage.submitForm();

    // Should be redirected back to view page
    await viewPastePage.expectToBeOnPastePage(paste.id);
    await viewPastePage.expectPasteTitle('Updated Title');
    await viewPastePage.expectPasteContent('Updated content');
    await viewPastePage.expectPasteLanguage('python');
  });

  test('should not show edit button for non-owner', async () => {
    // Create paste with different user
    const owner = await helpers.api.loginAsTestUser();
    const paste = await helpers.api.createTestPaste({ is_public: true });

    // View as anonymous user
    await viewPastePage.visit(paste.id!);
    await viewPastePage.expectEditButtonNotVisible();
  });

  test('should prevent editing by non-owner via direct URL', async ({ page }) => {
    // Create paste with different user
    const owner = await helpers.api.loginAsTestUser();
    const paste = await helpers.api.createTestPaste();

    // Try to access edit page directly
    await page.goto(`/paste/${paste.id}/edit`);

    // Should be redirected to login or show access denied
    const currentUrl = page.url();
    if (currentUrl.includes('/login')) {
      await expect(page).toHaveURL('/login');
    } else {
      await expect(page.locator('h1, .error')).toContainText(/access denied|unauthorized|forbidden/i);
    }
  });

  test('should delete paste successfully', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste({
      title: 'Paste to Delete',
      content: 'This will be deleted'
    });

    await viewPastePage.visit(paste.id!);
    await viewPastePage.deletePaste();

    // Should be redirected away from paste page
    await expect(page).not.toHaveURL(`/paste/${paste.id}`);
    
    // Try to access deleted paste
    await viewPastePage.visit(paste.id!);
    await viewPastePage.expectPasteNotFound();
  });

  test('should show delete confirmation dialog', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste();

    await viewPastePage.visit(paste.id!);
    
    // Click delete button
    await viewPastePage.deleteButton.click();

    // Should show confirmation dialog
    if (await viewPastePage.deleteConfirmDialog.isVisible()) {
      await expect(viewPastePage.deleteConfirmDialog).toBeVisible();
      
      // Cancel deletion
      await viewPastePage.cancelDeleteButton.click();
      
      // Should still be on paste page
      await viewPastePage.expectToBeOnPastePage(paste.id);
      
      // Try again and confirm
      await viewPastePage.deleteButton.click();
      await viewPastePage.confirmDeleteButton.click();
      
      // Should be deleted
      await expect(page).not.toHaveURL(`/paste/${paste.id}`);
    }
  });

  test('should not show delete button for non-owner', async () => {
    // Create paste with different user
    const owner = await helpers.api.loginAsTestUser();
    const paste = await helpers.api.createTestPaste({ is_public: true });

    // View as anonymous user
    await viewPastePage.visit(paste.id!);
    await viewPastePage.expectDeleteButtonNotVisible();
  });

  test('should handle edit form validation', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste();

    await viewPastePage.visit(paste.id!);
    await viewPastePage.editPaste();

    // Clear required fields
    await createPastePage.fillTitle('');
    await createPastePage.fillContent('');
    await createPastePage.submitForm();

    // Should show validation errors
    await createPastePage.expectValidationErrors();
    await createPastePage.expectTitleRequired();
    await createPastePage.expectContentRequired();
  });

  test('should preserve paste metadata during edit', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const customUrl = `edit-test-${Date.now()}`;
    const paste = await helpers.api.createTestPaste({
      title: 'Original Title',
      content: 'Original content',
      custom_url: customUrl,
      is_public: true
    });

    await viewPastePage.visit(paste.id!);
    await viewPastePage.editPaste();

    // Update only title and content
    await createPastePage.fillTitle('Updated Title');
    await createPastePage.fillContent('Updated content');
    await createPastePage.submitForm();

    // Custom URL should be preserved
    await viewPastePage.visitByCustomUrl(customUrl);
    await viewPastePage.expectPasteTitle('Updated Title');
    await viewPastePage.expectPasteContent('Updated content');
  });

  test('should handle edit conflicts gracefully', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste();

    await viewPastePage.visit(paste.id!);
    await viewPastePage.editPaste();

    // Simulate paste being updated by another session
    await helpers.api.updatePaste(paste.id!, {
      title: 'Conflicting Update',
      content: 'Updated by another session'
    });

    // Try to save changes
    await createPastePage.fillTitle('My Update');
    await createPastePage.submitForm();

    // Should handle conflict (show warning or merge changes)
    // Implementation depends on your conflict resolution strategy
  });

  test('should handle network errors during edit', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste();

    await viewPastePage.visit(paste.id!);
    await viewPastePage.editPaste();

    // Simulate network failure
    await page.route(`**/api/v1/pastes/${paste.id}`, route => route.abort());

    await createPastePage.fillTitle('Updated Title');
    await createPastePage.submitForm();

    // Should show error message
    await createPastePage.expectErrorMessage(/network|connection|failed/i);
  });

  test('should handle network errors during delete', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste();

    await viewPastePage.visit(paste.id!);

    // Simulate network failure
    await page.route(`**/api/v1/pastes/${paste.id}`, route => route.abort());

    await viewPastePage.deleteButton.click();
    
    if (await viewPastePage.deleteConfirmDialog.isVisible()) {
      await viewPastePage.confirmDeleteButton.click();
    }

    // Should show error message
    await viewPastePage.expectErrorMessage(/network|connection|failed/i);
  });

  test('should handle server errors during edit', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste();

    await viewPastePage.visit(paste.id!);
    await viewPastePage.editPaste();

    // Simulate server error
    await page.route(`**/api/v1/pastes/${paste.id}`, route => 
      route.fulfill({ status: 500, body: JSON.stringify({ error: 'Internal server error' }) })
    );

    await createPastePage.fillTitle('Updated Title');
    await createPastePage.submitForm();

    // Should show error message
    await createPastePage.expectErrorMessage(/server error|internal error/i);
  });

  test('should handle unauthorized edit attempt', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste();

    await viewPastePage.visit(paste.id!);
    await viewPastePage.editPaste();

    // Simulate unauthorized response (token expired, etc.)
    await page.route(`**/api/v1/pastes/${paste.id}`, route => 
      route.fulfill({ status: 401, body: JSON.stringify({ error: 'Unauthorized' }) })
    );

    await createPastePage.fillTitle('Updated Title');
    await createPastePage.submitForm();

    // Should redirect to login or show unauthorized error
    const currentUrl = page.url();
    if (currentUrl.includes('/login')) {
      await expect(page).toHaveURL('/login');
    } else {
      await createPastePage.expectErrorMessage(/unauthorized|login/i);
    }
  });

  test('should update paste visibility settings', async ({ page }) => {
    // Login and create public paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste({ is_public: true });

    await viewPastePage.visit(paste.id!);
    await viewPastePage.editPaste();

    // Change to private
    await createPastePage.setPublic(false);
    await createPastePage.submitForm();

    // Should be updated
    await viewPastePage.expectToBeOnPastePage(paste.id);
    await viewPastePage.expectPastePrivate();
  });

  test('should update paste expiration settings', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste();

    await viewPastePage.visit(paste.id!);
    await viewPastePage.editPaste();

    // Set expiration
    await createPastePage.setExpiration('1h');
    await createPastePage.submitForm();

    // Should show expiration warning
    await viewPastePage.expectToBeOnPastePage(paste.id);
    await viewPastePage.expectExpirationWarning();
  });

  test('should navigate back to paste after canceling edit', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste();

    await viewPastePage.visit(paste.id!);
    await viewPastePage.editPaste();

    // Navigate back without saving
    await page.goBack();

    // Should be back on view page
    await viewPastePage.expectToBeOnPastePage(paste.id);
  });

  test('should handle bulk delete operations', async ({ page }) => {
    // Login and create multiple pastes as owner
    const user = await helpers.loginAsTestUser(page);
    const pastes = await Promise.all([
      helpers.api.createTestPaste({ title: 'Paste 1' }),
      helpers.api.createTestPaste({ title: 'Paste 2' }),
      helpers.api.createTestPaste({ title: 'Paste 3' })
    ]);

    // Go to user pastes page
    await page.goto('/profile/pastes');

    // If bulk delete is supported, test it
    const bulkDeleteButton = page.locator('button:has-text("Delete Selected"), [data-testid="bulk-delete"]');
    if (await bulkDeleteButton.isVisible()) {
      // Select multiple pastes
      for (const paste of pastes) {
        const checkbox = page.locator(`input[type="checkbox"][value="${paste.id}"]`);
        if (await checkbox.isVisible()) {
          await checkbox.check();
        }
      }

      // Delete selected
      await bulkDeleteButton.click();

      // Confirm if dialog appears
      const confirmButton = page.locator('button:has-text("Confirm"), button:has-text("Yes")');
      if (await confirmButton.isVisible()) {
        await confirmButton.click();
      }

      // Pastes should be deleted
      for (const paste of pastes) {
        await viewPastePage.visit(paste.id!);
        await viewPastePage.expectPasteNotFound();
      }
    }
  });
});
