import { Page, Locator, expect } from '@playwright/test';

export abstract class BasePage {
  protected page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  // Common navigation elements
  get navigation(): Locator {
    return this.page.locator('nav, [role="navigation"]').first();
  }

  get homeLink(): Locator {
    return this.navigation.locator('a[href="/"], a:has-text("Home")').first();
  }

  get loginLink(): Locator {
    return this.navigation.locator('a[href="/login"], a:has-text("Login")').first();
  }

  get registerLink(): Locator {
    return this.navigation.locator('a[href="/register"], a:has-text("Register")').first();
  }

  get createPasteLink(): Locator {
    return this.navigation.locator('a[href="/create"], a:has-text("Create")').first();
  }

  get profileLink(): Locator {
    return this.navigation.locator('a[href="/profile"], a:has-text("Profile")').first();
  }

  get logoutButton(): Locator {
    return this.navigation.locator('button:has-text("Logout"), a:has-text("Logout")').first();
  }

  // Common UI elements
  get userMenu(): Locator {
    return this.page.locator('[data-testid="user-menu"], .user-menu').first();
  }

  get toast(): Locator {
    return this.page.locator('[data-testid="toast"], .toast, .notification').first();
  }

  get errorMessage(): Locator {
    return this.page.locator('.error, .alert-error, [data-testid="error"]').first();
  }

  get successMessage(): Locator {
    return this.page.locator('.success, .alert-success, [data-testid="success"]').first();
  }

  get loadingSpinner(): Locator {
    return this.page.locator('.loading, .spinner, [data-testid="loading"]').first();
  }

  // Common actions
  async navigateToHome(): Promise<void> {
    await this.homeLink.click();
    await this.page.waitForURL('/');
  }

  async navigateToLogin(): Promise<void> {
    await this.loginLink.click();
    await this.page.waitForURL('/login');
  }

  async navigateToRegister(): Promise<void> {
    await this.registerLink.click();
    await this.page.waitForURL('/register');
  }

  async navigateToCreatePaste(): Promise<void> {
    await this.createPasteLink.click();
    await this.page.waitForURL('/create');
  }

  async navigateToProfile(): Promise<void> {
    await this.profileLink.click();
    await this.page.waitForURL('/profile');
  }

  async logout(): Promise<void> {
    await this.logoutButton.click();
    await this.page.waitForURL(/\/(|login)/);
  }

  // Common assertions
  async expectToBeLoggedIn(username?: string): Promise<void> {
    await expect(this.userMenu).toBeVisible();
    if (username) {
      await expect(this.userMenu).toContainText(username);
    }
  }

  async expectToBeLoggedOut(): Promise<void> {
    await expect(this.loginLink).toBeVisible();
    await expect(this.registerLink).toBeVisible();
  }

  async expectToast(message: string): Promise<void> {
    await expect(this.toast).toBeVisible();
    await expect(this.toast).toContainText(message);
  }

  async expectErrorMessage(message: string): Promise<void> {
    await expect(this.errorMessage).toBeVisible();
    await expect(this.errorMessage).toContainText(message);
  }

  async expectSuccessMessage(message: string): Promise<void> {
    await expect(this.successMessage).toBeVisible();
    await expect(this.successMessage).toContainText(message);
  }

  async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle');
  }

  async waitForApiResponse(endpoint: string, timeout: number = 5000): Promise<void> {
    await this.page.waitForResponse(
      response => response.url().includes(endpoint) && response.status() === 200,
      { timeout }
    );
  }

  // Utility methods
  async takeScreenshot(name: string): Promise<void> {
    await this.page.screenshot({ path: `test-results/screenshots/${name}.png` });
  }

  async getPageTitle(): Promise<string> {
    return await this.page.title();
  }

  async getCurrentUrl(): Promise<string> {
    return this.page.url();
  }

  async isElementVisible(selector: string): Promise<boolean> {
    return await this.page.locator(selector).isVisible();
  }

  async waitForElement(selector: string, timeout: number = 5000): Promise<void> {
    await this.page.locator(selector).waitFor({ timeout });
  }

  async scrollToElement(locator: Locator): Promise<void> {
    await locator.scrollIntoViewIfNeeded();
  }

  async clickAndWaitForNavigation(locator: Locator, expectedUrl?: string | RegExp): Promise<void> {
    await Promise.all([
      expectedUrl ? this.page.waitForURL(expectedUrl) : this.page.waitForLoadState('networkidle'),
      locator.click()
    ]);
  }
}
