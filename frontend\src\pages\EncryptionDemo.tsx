import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { useEncryption } from '@/hooks/useEncryption';
import EncryptionToggle from '@/components/EncryptionToggle';
import { Shield, Copy, Key } from 'lucide-react';

export default function EncryptionDemo() {
  const [testContent, setTestContent] = useState('Hello, this is a test message for encryption!');
  const [encryptedResult, setEncryptedResult] = useState<string>('');
  const [decryptedResult, setDecryptedResult] = useState<string>('');
  const [shareableUrl, setShareableUrl] = useState<string>('');
  const { toast } = useToast();
  
  const {
    isEncryptionEnabled,
    isEncryptionSupported,
    encryptionKey,
    encryptContent,
    decryptContent,
    getShareableUrl,
    error
  } = useEncryption();

  const handleEncrypt = async () => {
    if (!encryptionKey) {
      toast({
        title: "Error",
        description: "Please generate an encryption key first",
        variant: "destructive",
      });
      return;
    }

    const encrypted = await encryptContent(testContent);
    if (encrypted) {
      setEncryptedResult(JSON.stringify(encrypted, null, 2));
      
      // Generate shareable URL
      const baseUrl = `${window.location.origin}/demo/encrypted`;
      const url = getShareableUrl(baseUrl);
      setShareableUrl(url);
      
      toast({
        title: "Success",
        description: "Content encrypted successfully",
      });
    }
  };

  const handleDecrypt = async () => {
    if (!encryptedResult) {
      toast({
        title: "Error",
        description: "No encrypted content to decrypt",
        variant: "destructive",
      });
      return;
    }

    try {
      const encryptedData = JSON.parse(encryptedResult);
      const decrypted = await decryptContent(encryptedData);
      
      if (decrypted) {
        setDecryptedResult(decrypted);
        toast({
          title: "Success",
          description: "Content decrypted successfully",
        });
      }
    } catch (err) {
      toast({
        title: "Error",
        description: "Invalid encrypted data format",
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied",
        description: `${label} copied to clipboard`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  if (!isEncryptionSupported) {
    return (
      <div className="max-w-4xl mx-auto">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-8">
            <div className="text-center space-y-4">
              <Shield className="h-16 w-16 text-red-400 mx-auto" />
              <div>
                <h3 className="text-lg font-medium text-red-900">Encryption Not Supported</h3>
                <p className="text-red-700 mt-2">
                  Your browser doesn't support the Web Crypto API required for client-side encryption.
                  Please use a modern browser like Chrome, Firefox, Safari, or Edge.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-6 w-6" />
            Client-side Encryption Demo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            This demo shows how client-side encryption works. Your content is encrypted in your browser 
            before being sent to the server, ensuring zero-knowledge security.
          </p>
        </CardContent>
      </Card>

      {/* Encryption Toggle */}
      <EncryptionToggle />

      {/* Test Content */}
      <Card>
        <CardHeader>
          <CardTitle>Test Content</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Content to Encrypt
            </label>
            <Textarea
              value={testContent}
              onChange={(e) => setTestContent(e.target.value)}
              placeholder="Enter content to encrypt..."
              rows={4}
            />
          </div>
          
          <div className="flex gap-2">
            <Button 
              onClick={handleEncrypt}
              disabled={!isEncryptionEnabled || !encryptionKey}
              className="flex items-center gap-2"
            >
              <Shield className="h-4 w-4" />
              Encrypt Content
            </Button>
            
            <Button 
              onClick={handleDecrypt}
              disabled={!encryptedResult || !encryptionKey}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Key className="h-4 w-4" />
              Decrypt Content
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Encrypted Result */}
      {encryptedResult && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Encrypted Data</CardTitle>
              <Button
                onClick={() => copyToClipboard(encryptedResult, 'Encrypted data')}
                variant="outline"
                size="sm"
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-auto">
              {encryptedResult}
            </pre>
          </CardContent>
        </Card>
      )}

      {/* Shareable URL */}
      {shareableUrl && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Shareable URL (with encryption key)</CardTitle>
              <Button
                onClick={() => copyToClipboard(shareableUrl, 'Shareable URL')}
                variant="outline"
                size="sm"
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy URL
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Input
              value={shareableUrl}
              readOnly
              className="font-mono text-sm"
            />
            <p className="text-sm text-amber-600 mt-2">
              ⚠️ This URL contains the encryption key. Share it only through secure channels.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Decrypted Result */}
      {decryptedResult && (
        <Card>
          <CardHeader>
            <CardTitle>Decrypted Content</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-green-50 border border-green-200 p-4 rounded-md">
              <p className="text-green-800">{decryptedResult}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-700">{error}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}