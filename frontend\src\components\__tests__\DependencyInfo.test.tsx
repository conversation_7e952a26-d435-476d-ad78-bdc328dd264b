import { render, screen, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import DependencyInfo from '../DependencyInfo'

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

// Mock fetch
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('DependencyInfo', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('shows loading state initially', () => {
    mockFetch.mockImplementation(() => new Promise(() => {})) // Never resolves
    
    render(<DependencyInfo pasteId="test-id" language="javascript" />)
    
    expect(screen.getByText('Analyzing dependencies...')).toBeInTheDocument()
  })

  it('displays dependencies when loaded successfully', async () => {
    const mockDependencies = {
      dependencies: [
        {
          name: 'react',
          type: 'import',
          language: 'javascript',
          registry: 'npm',
          registry_url: 'https://www.npmjs.com/package/react',
          install_cmd: 'npm install react',
        },
        {
          name: 'axios',
          type: 'import',
          language: 'javascript',
          registry: 'npm',
          registry_url: 'https://www.npmjs.com/package/axios',
          install_cmd: 'npm install axios',
        },
      ],
      language: 'javascript',
      total_count: 2,
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockDependencies,
    })

    render(<DependencyInfo pasteId="test-id" language="javascript" />)

    await waitFor(() => {
      expect(screen.getByText('Dependencies (2)')).toBeInTheDocument()
    })

    expect(screen.getByText('react')).toBeInTheDocument()
    expect(screen.getByText('axios')).toBeInTheDocument()
    expect(screen.getByText('npm install react')).toBeInTheDocument()
    expect(screen.getByText('npm install axios')).toBeInTheDocument()
  })

  it('shows no dependencies message when none found', async () => {
    const mockDependencies = {
      dependencies: [],
      language: 'javascript',
      total_count: 0,
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockDependencies,
    })

    render(<DependencyInfo pasteId="test-id" language="javascript" />)

    await waitFor(() => {
      expect(screen.getByText('No dependencies detected for javascript')).toBeInTheDocument()
    })
  })

  it('shows error message when fetch fails', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'))

    render(<DependencyInfo pasteId="test-id" language="javascript" />)

    await waitFor(() => {
      expect(screen.getByText(/Unable to analyze dependencies/)).toBeInTheDocument()
    })
  })

  it('shows error message when response is not ok', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 404,
    })

    render(<DependencyInfo pasteId="test-id" language="javascript" />)

    await waitFor(() => {
      expect(screen.getByText(/Unable to analyze dependencies/)).toBeInTheDocument()
    })
  })

  it('makes correct API call', async () => {
    const mockDependencies = {
      dependencies: [],
      language: 'javascript',
      total_count: 0,
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockDependencies,
    })

    render(<DependencyInfo pasteId="test-id" language="javascript" />)

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/v1/pastes/test-id/dependencies')
    })
  })

  it('shows limited dependencies initially and expands on click', async () => {
    const mockDependencies = {
      dependencies: Array.from({ length: 10 }, (_, i) => ({
        name: `package-${i}`,
        type: 'import',
        language: 'javascript',
        registry: 'npm',
        registry_url: `https://www.npmjs.com/package/package-${i}`,
        install_cmd: `npm install package-${i}`,
      })),
      language: 'javascript',
      total_count: 10,
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockDependencies,
    })

    render(<DependencyInfo pasteId="test-id" language="javascript" />)

    await waitFor(() => {
      expect(screen.getByText('Dependencies (10)')).toBeInTheDocument()
    })

    // Should show only first 5 dependencies initially
    expect(screen.getByText('package-0')).toBeInTheDocument()
    expect(screen.getByText('package-4')).toBeInTheDocument()
    expect(screen.queryByText('package-5')).not.toBeInTheDocument()

    // Should show expand button
    expect(screen.getByText('Show 5 more dependencies')).toBeInTheDocument()
  })
})