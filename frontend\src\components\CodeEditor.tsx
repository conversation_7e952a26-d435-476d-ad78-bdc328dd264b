import { useEffect, useRef, useState } from 'react'
import Editor from '@monaco-editor/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>, Moon, Settings, Copy, Download, Code, Wand2, Al<PERSON><PERSON>riangle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useFormatting } from '@/contexts/FormattingContext'
import { codeFormatterService, LintResult } from '@/services/codeFormatter'
import FormattingSettings from './FormattingSettings'

export interface CodeEditorProps {
  value: string
  onChange?: (value: string) => void
  language?: string
  onLanguageChange?: (language: string) => void
  readOnly?: boolean
  height?: string
  showToolbar?: boolean
  showLanguageSelector?: boolean
  title?: string
}

const SUPPORTED_LANGUAGES = [
  { value: 'text', label: 'Plain Text' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'go', label: 'Go' },
  { value: 'java', label: 'Java' },
  { value: 'cpp', label: 'C++' },
  { value: 'c', label: 'C' },
  { value: 'rust', label: 'Rust' },
  { value: 'php', label: 'PHP' },
  { value: 'ruby', label: 'Ruby' },
  { value: 'html', label: 'HTML' },
  { value: 'css', label: 'CSS' },
  { value: 'scss', label: 'SCSS' },
  { value: 'json', label: 'JSON' },
  { value: 'xml', label: 'XML' },
  { value: 'yaml', label: 'YAML' },
  { value: 'markdown', label: 'Markdown' },
  { value: 'sql', label: 'SQL' },
  { value: 'bash', label: 'Bash' },
  { value: 'shell', label: 'Shell' },
  { value: 'dockerfile', label: 'Dockerfile' },
  { value: 'kotlin', label: 'Kotlin' },
  { value: 'swift', label: 'Swift' },
  { value: 'csharp', label: 'C#' },
]

const THEME_OPTIONS = [
  { value: 'vs', label: 'Light' },
  { value: 'vs-dark', label: 'Dark' },
  { value: 'hc-black', label: 'High Contrast Dark' },
  { value: 'hc-light', label: 'High Contrast Light' },
]

export default function CodeEditor({
  value,
  onChange,
  language = 'text',
  onLanguageChange,
  readOnly = false,
  height = '400px',
  showToolbar = true,
  showLanguageSelector = true,
  title,
}: CodeEditorProps) {
  const [currentLanguage, setCurrentLanguage] = useState(language)
  const [theme, setTheme] = useState<string>(() => {
    // Get theme from localStorage or default to system preference
    const savedTheme = localStorage.getItem('monaco-theme')
    if (savedTheme) return savedTheme
    
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'vs-dark' : 'vs'
  })
  const [showSettings, setShowSettings] = useState(false)
  const [showFormattingSettings, setShowFormattingSettings] = useState(false)
  const [fontSize, setFontSize] = useState(14)
  const [showMinimap, setShowMinimap] = useState(true)
  const [wordWrap, setWordWrap] = useState<'on' | 'off'>('on')
  const [isFormatting, setIsFormatting] = useState(false)
  const [lintResults, setLintResults] = useState<LintResult>({ errors: [], warnings: [] })
  const editorRef = useRef<any>(null)
  const { toast } = useToast()
  const { settings } = useFormatting()

  // Update language when prop changes
  useEffect(() => {
    if (language !== currentLanguage) {
      setCurrentLanguage(language)
    }
  }, [language, currentLanguage])

  // Save preferences
  useEffect(() => {
    localStorage.setItem('monaco-theme', theme)
    localStorage.setItem('monaco-fontSize', fontSize.toString())
    localStorage.setItem('monaco-minimap', showMinimap.toString())
    localStorage.setItem('monaco-wordWrap', wordWrap)
  }, [theme, fontSize, showMinimap, wordWrap])

  // Load preferences on mount
  useEffect(() => {
    const savedFontSize = localStorage.getItem('monaco-fontSize')
    const savedMinimap = localStorage.getItem('monaco-minimap')
    const savedWordWrap = localStorage.getItem('monaco-wordWrap')
    
    if (savedFontSize) setFontSize(parseInt(savedFontSize))
    if (savedMinimap) setShowMinimap(savedMinimap === 'true')
    if (savedWordWrap) setWordWrap(savedWordWrap as 'on' | 'off')
  }, [])

  // Update editor options when settings change
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.updateOptions({
        fontSize,
        minimap: { enabled: showMinimap },
        wordWrap,
      })
    }
  }, [fontSize, showMinimap, wordWrap])

  // Lint code when content or language changes
  useEffect(() => {
    if (settings.showLintingHints && value && currentLanguage) {
      const results = codeFormatterService.lintCode(value, currentLanguage)
      setLintResults(results)
    } else {
      setLintResults({ errors: [], warnings: [] })
    }
  }, [value, currentLanguage, settings.showLintingHints])

  // Auto-format on paste
  useEffect(() => {
    if (settings.formatOnPaste && editorRef.current) {
      const editor = editorRef.current
      const disposable = editor.onDidPaste(() => {
        formatCode()
      })
      return () => disposable.dispose()
    }
  }, [settings.formatOnPaste])

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor
    
    // Configure editor options
    editor.updateOptions({
      fontSize: 14,
      lineNumbers: 'on',
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      automaticLayout: true,
      folding: true,
      bracketMatching: 'always',
      matchBrackets: 'always',
      renderWhitespace: 'selection',
      renderControlCharacters: true,
      smoothScrolling: true,
      cursorBlinking: 'smooth',
      cursorSmoothCaretAnimation: 'on',
      suggestOnTriggerCharacters: true,
      quickSuggestions: true,
      parameterHints: { enabled: true },
      formatOnPaste: true,
      formatOnType: true,
      autoIndent: 'full',
      tabSize: 2,
      insertSpaces: true,
      detectIndentation: true,
      trimAutoWhitespace: true,
      renderLineHighlight: 'all',
      selectionHighlight: true,
      occurrencesHighlight: true,
      codeLens: true,
      colorDecorators: true,
      links: true,
      contextmenu: true,
      mouseWheelZoom: true,
      multiCursorModifier: 'ctrlCmd',
      accessibilitySupport: 'auto',
    })

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave()
      return true
    })

    // Add format document shortcut
    editor.addCommand(monaco.KeyMod.Shift | monaco.KeyMod.Alt | monaco.KeyCode.KeyF, () => {
      formatCode()
    })

    // Add find shortcut
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
      editor.getAction('actions.find').run()
    })

    // Add linting markers
    if (settings.showLintingHints) {
      updateLintingMarkers(monaco, editor)
    }
  }

  const handleLanguageChange = (newLanguage: string) => {
    setCurrentLanguage(newLanguage)
    onLanguageChange?.(newLanguage)
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(value)
      toast({
        title: "Copied",
        description: "Code copied to clipboard",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      })
    }
  }

  const downloadFile = () => {
    const extension = getFileExtension(currentLanguage)
    const filename = `paste.${extension}`
    const blob = new Blob([value], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast({
      title: "Downloaded",
      description: `File saved as ${filename}`,
    })
  }

  const getFileExtension = (lang: string): string => {
    const extensions: Record<string, string> = {
      javascript: 'js',
      typescript: 'ts',
      python: 'py',
      go: 'go',
      java: 'java',
      cpp: 'cpp',
      c: 'c',
      rust: 'rs',
      php: 'php',
      ruby: 'rb',
      html: 'html',
      css: 'css',
      scss: 'scss',
      json: 'json',
      xml: 'xml',
      yaml: 'yml',
      markdown: 'md',
      sql: 'sql',
      bash: 'sh',
      shell: 'sh',
      dockerfile: 'dockerfile',
      kotlin: 'kt',
      swift: 'swift',
      csharp: 'cs',
    }
    return extensions[lang] || 'txt'
  }

  const toggleTheme = () => {
    setTheme(current => current === 'vs-dark' ? 'vs' : 'vs-dark')
  }

  const formatCode = async () => {
    if (!editorRef.current || !value) return

    setIsFormatting(true)
    try {
      const result = await codeFormatterService.formatCode(value, currentLanguage, settings.formatOptions)
      
      if (result.success && result.formattedCode) {
        onChange?.(result.formattedCode)
        toast({
          title: "Formatted",
          description: "Code has been formatted successfully",
        })
      } else {
        // Fallback to Monaco's built-in formatter
        editorRef.current.getAction('editor.action.formatDocument').run()
        toast({
          title: "Formatted",
          description: result.error || "Used built-in formatter",
        })
      }
    } catch (error) {
      toast({
        title: "Format Error",
        description: error instanceof Error ? error.message : "Failed to format code",
        variant: "destructive",
      })
    } finally {
      setIsFormatting(false)
    }
  }

  const handleSave = async () => {
    if (settings.autoFormatOnSave && !readOnly) {
      await formatCode()
    }
  }

  const updateLintingMarkers = (monaco: any, editor: any) => {
    if (!settings.showLintingHints) return

    const markers = [
      ...lintResults.errors.map(error => ({
        startLineNumber: error.line,
        startColumn: error.column,
        endLineNumber: error.line,
        endColumn: error.column + 10,
        message: error.message,
        severity: monaco.MarkerSeverity.Error
      })),
      ...lintResults.warnings.map(warning => ({
        startLineNumber: warning.line,
        startColumn: warning.column,
        endLineNumber: warning.line,
        endColumn: warning.column + 10,
        message: warning.message,
        severity: monaco.MarkerSeverity.Warning
      }))
    ]

    const model = editor.getModel()
    if (model) {
      monaco.editor.setModelMarkers(model, 'linting', markers)
    }
  }

  // Update linting markers when results change
  useEffect(() => {
    if (editorRef.current && (window as any).monaco) {
      updateLintingMarkers((window as any).monaco, editorRef.current)
    }
  }, [lintResults, settings.showLintingHints])

  return (
    <>
      <Card className="w-full">
        {(showToolbar || title) && (
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {title && <CardTitle className="text-lg">{title}</CardTitle>}
                {settings.showLintingHints && (lintResults.errors.length > 0 || lintResults.warnings.length > 0) && (
                  <div className="flex items-center gap-2 text-sm">
                    {lintResults.errors.length > 0 && (
                      <span className="flex items-center gap-1 text-red-600">
                        <AlertTriangle className="h-4 w-4" />
                        {lintResults.errors.length} error{lintResults.errors.length !== 1 ? 's' : ''}
                      </span>
                    )}
                    {lintResults.warnings.length > 0 && (
                      <span className="flex items-center gap-1 text-yellow-600">
                        <AlertTriangle className="h-4 w-4" />
                        {lintResults.warnings.length} warning{lintResults.warnings.length !== 1 ? 's' : ''}
                      </span>
                    )}
                  </div>
                )}
              </div>
              {showToolbar && (
              <div className="flex items-center space-x-2">
                {showLanguageSelector && (
                  <select
                    value={currentLanguage}
                    onChange={(e) => handleLanguageChange(e.target.value)}
                    className="flex h-8 rounded-md border border-input bg-background px-2 py-1 text-xs ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    disabled={readOnly}
                  >
                    {SUPPORTED_LANGUAGES.map((lang) => (
                      <option key={lang.value} value={lang.value}>
                        {lang.label}
                      </option>
                    ))}
                  </select>
                )}
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleTheme}
                  title={`Switch to ${theme === 'vs-dark' ? 'light' : 'dark'} theme`}
                >
                  {theme === 'vs-dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                </Button>
                
                {!readOnly && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={formatCode}
                    disabled={isFormatting}
                    title="Format code"
                  >
                    {isFormatting ? (
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    ) : (
                      <Code className="h-4 w-4" />
                    )}
                  </Button>
                )}
                
                {!readOnly && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFormattingSettings(true)}
                    title="Formatting settings"
                  >
                    <Wand2 className="h-4 w-4" />
                  </Button>
                )}
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyToClipboard}
                  title="Copy to clipboard"
                >
                  <Copy className="h-4 w-4" />
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadFile}
                  title="Download file"
                >
                  <Download className="h-4 w-4" />
                </Button>
                
                <div className="relative">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowSettings(!showSettings)}
                    title="Editor settings"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  
                  {showSettings && (
                    <div className="absolute right-0 top-full mt-2 w-64 bg-background border rounded-md shadow-lg z-50 p-4">
                      <div className="space-y-4">
                        <div>
                          <label className="text-xs font-medium">Theme</label>
                          <select
                            value={theme}
                            onChange={(e) => setTheme(e.target.value)}
                            className="w-full mt-1 flex h-8 rounded-md border border-input bg-background px-2 py-1 text-xs"
                          >
                            {THEME_OPTIONS.map((themeOption) => (
                              <option key={themeOption.value} value={themeOption.value}>
                                {themeOption.label}
                              </option>
                            ))}
                          </select>
                        </div>
                        
                        <div>
                          <label className="text-xs font-medium">Font Size</label>
                          <input
                            type="range"
                            min="10"
                            max="24"
                            value={fontSize}
                            onChange={(e) => setFontSize(parseInt(e.target.value))}
                            className="w-full mt-1"
                          />
                          <div className="text-xs text-muted-foreground mt-1">{fontSize}px</div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <label className="text-xs font-medium">Minimap</label>
                          <input
                            type="checkbox"
                            checked={showMinimap}
                            onChange={(e) => setShowMinimap(e.target.checked)}
                            className="rounded"
                          />
                        </div>
                        
                        <div>
                          <label className="text-xs font-medium">Word Wrap</label>
                          <select
                            value={wordWrap}
                            onChange={(e) => setWordWrap(e.target.value as 'on' | 'off')}
                            className="w-full mt-1 flex h-8 rounded-md border border-input bg-background px-2 py-1 text-xs"
                          >
                            <option value="on">On</option>
                            <option value="off">Off</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardHeader>
      )}
      
      <CardContent className="p-0">
        <div className="border rounded-b-lg overflow-hidden">
          <Editor
            height={height}
            language={currentLanguage}
            value={value}
            onChange={(newValue) => onChange?.(newValue || '')}
            theme={theme}
            onMount={handleEditorDidMount}
            options={{
              readOnly,
              selectOnLineNumbers: true,
              roundedSelection: false,
              scrollBeyondLastLine: false,
              automaticLayout: true,
            }}
          />
        </div>
      </CardContent>
    </Card>
    
    <FormattingSettings
      isOpen={showFormattingSettings}
      onClose={() => setShowFormattingSettings(false)}
    />
  </>
  )
}