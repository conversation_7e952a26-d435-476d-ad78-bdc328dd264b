import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export interface DatabaseConfig {
  url: string;
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
}

export const DEFAULT_DB_CONFIG: DatabaseConfig = {
  url: 'postgres://postgres:password@localhost:5432/enhanced_pastebin?sslmode=disable',
  host: 'localhost',
  port: 5432,
  database: 'enhanced_pastebin',
  username: 'postgres',
  password: 'password'
};

export class DatabaseManager {
  private config: DatabaseConfig;

  constructor(config: DatabaseConfig = DEFAULT_DB_CONFIG) {
    this.config = config;
  }

  async startServices(): Promise<void> {
    return new Promise((resolve, reject) => {
      const projectRoot = path.resolve(__dirname, '../../../..');
      
      const dbProcess = spawn('docker-compose', ['up', '-d', 'postgres', 'redis'], {
        cwd: projectRoot,
        stdio: 'pipe',
        shell: true
      });

      let output = '';
      dbProcess.stdout?.on('data', (data) => {
        output += data.toString();
      });

      dbProcess.stderr?.on('data', (data) => {
        output += data.toString();
      });

      dbProcess.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Database services failed to start: ${output}`));
        }
      });

      dbProcess.on('error', (error) => {
        reject(new Error(`Failed to start database services: ${error.message}`));
      });
    });
  }

  async stopServices(): Promise<void> {
    return new Promise((resolve) => {
      const projectRoot = path.resolve(__dirname, '../../../..');
      
      const stopProcess = spawn('docker-compose', ['down'], {
        cwd: projectRoot,
        stdio: 'pipe',
        shell: true
      });

      stopProcess.on('close', () => {
        resolve();
      });

      stopProcess.on('error', () => {
        resolve(); // Don't fail if services were already stopped
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        resolve();
      }, 30000);
    });
  }

  async waitForDatabase(maxAttempts: number = 30, delay: number = 2000): Promise<void> {
    for (let i = 0; i < maxAttempts; i++) {
      try {
        const result = await new Promise<boolean>((resolve) => {
          const proc = spawn('docker', [
            'exec',
            'fullstack-pastify-postgres-1',
            'pg_isready',
            '-U', this.config.username
          ], { stdio: 'pipe' });

          proc.on('close', (code) => {
            resolve(code === 0);
          });

          proc.on('error', () => {
            resolve(false);
          });
        });

        if (result) {
          return;
        }
      } catch (error) {
        // Continue trying
      }

      await new Promise(resolve => setTimeout(resolve, delay));
    }

    throw new Error('Database failed to become ready within timeout');
  }

  async runMigrations(): Promise<void> {
    return new Promise((resolve, reject) => {
      const projectRoot = path.resolve(__dirname, '../../../..');
      
      const migrationProcess = spawn('go', ['run', 'cmd/migrate/main.go', 'up'], {
        cwd: projectRoot,
        stdio: 'pipe',
        shell: true,
        env: {
          ...process.env,
          DATABASE_URL: this.config.url
        }
      });

      let output = '';
      migrationProcess.stdout?.on('data', (data) => {
        output += data.toString();
      });

      migrationProcess.stderr?.on('data', (data) => {
        output += data.toString();
      });

      migrationProcess.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Database migrations failed: ${output}`));
        }
      });

      migrationProcess.on('error', (error) => {
        reject(new Error(`Failed to run migrations: ${error.message}`));
      });
    });
  }

  async resetDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      const projectRoot = path.resolve(__dirname, '../../../..');
      
      const resetProcess = spawn('go', ['run', 'cmd/migrate/main.go', 'down'], {
        cwd: projectRoot,
        stdio: 'pipe',
        shell: true,
        env: {
          ...process.env,
          DATABASE_URL: this.config.url
        }
      });

      let output = '';
      resetProcess.stdout?.on('data', (data) => {
        output += data.toString();
      });

      resetProcess.stderr?.on('data', (data) => {
        output += data.toString();
      });

      resetProcess.on('close', async (code) => {
        if (code === 0) {
          try {
            await this.runMigrations();
            resolve();
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new Error(`Database reset failed: ${output}`));
        }
      });

      resetProcess.on('error', (error) => {
        reject(new Error(`Failed to reset database: ${error.message}`));
      });
    });
  }

  async isDatabaseReady(): Promise<boolean> {
    try {
      const result = await new Promise<boolean>((resolve) => {
        const proc = spawn('docker', [
          'exec',
          'fullstack-pastify-postgres-1',
          'pg_isready',
          '-U', this.config.username
        ], { stdio: 'pipe' });

        proc.on('close', (code) => {
          resolve(code === 0);
        });

        proc.on('error', () => {
          resolve(false);
        });
      });

      return result;
    } catch (error) {
      return false;
    }
  }
}
