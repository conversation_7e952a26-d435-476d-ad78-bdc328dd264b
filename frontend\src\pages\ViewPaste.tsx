import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate, Link, useLocation } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { SkeletonCard, SkeletonCodeEditor } from '@/components/ui/skeleton'
import { useToast } from '@/hooks/use-toast'
import { pasteAPI, Paste } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { Copy, Eye, Calendar, User, Edit, Trash2, Link as LinkIcon, Shield, AlertTriangle, Key } from 'lucide-react'
import CodeEditor from '@/components/CodeEditor'
import { useWebSocket } from '@/hooks/useWebSocket'
import { PresenceIndicator } from '@/components/PresenceIndicator'
import { useEncryption } from '@/hooks/useEncryption'
import { EncryptedData } from '@/services/encryption'
import DependencyInfo from '@/components/DependencyInfo'
import ChatPanel from '@/components/ChatPanel'
import ExpirationWarning from '@/components/ExpirationWarning'
import MultiFormatViewer from '@/components/MultiFormatViewer'
import AuditTrail from '@/components/AuditTrail'
import VersionHistory from '@/components/VersionHistory'

export default function ViewPaste() {
  const { id, customUrl } = useParams<{ id?: string; customUrl?: string }>()
  const [paste, setPaste] = useState<Paste | null>(null)
  const [decryptedContent, setDecryptedContent] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isDecrypting, setIsDecrypting] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [decryptionError, setDecryptionError] = useState<string | null>(null)
  const location = useLocation()
  const navigate = useNavigate()
  const { toast } = useToast()
  const { user } = useAuth()
  const { decryptContent, importKeyFromUrl, isEncryptionSupported } = useEncryption()

  // Determine if we're viewing by custom URL or regular ID
  const isCustomURL = location.pathname.startsWith('/p/')
  const identifier = isCustomURL ? customUrl : id

  // WebSocket connection for real-time features
  const {
    isConnected,
    connectionState,
    activeUsers,
    userCount,
    reconnectAttempts
  } = useWebSocket({
    pasteId: paste?.id,
    autoConnect: true
  })

  useEffect(() => {
    const fetchPaste = async () => {
      if (!identifier) return

      try {
        let response
        if (isCustomURL) {
          response = await pasteAPI.getByCustomURL(identifier)
        } else {
          response = await pasteAPI.get(identifier)
        }
        setPaste(response.data)

        // If paste is encrypted, try to decrypt it
        if (response.data.is_encrypted) {
          await handleDecryption(response.data)
        }
      } catch (error: any) {
        toast({
          title: "Error",
          description: error.response?.data?.error || "Failed to load paste",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchPaste()
  }, [identifier, isCustomURL, toast])

  const handleDecryption = async (pasteData: Paste) => {
    if (!pasteData.is_encrypted || !isEncryptionSupported) {
      return
    }

    setIsDecrypting(true)
    setDecryptionError(null)

    try {
      // Try to import key from URL
      const keyImported = await importKeyFromUrl()
      
      if (!keyImported) {
        setDecryptionError('No encryption key found in URL')
        return
      }

      // Parse encrypted data from content
      const encryptedData: EncryptedData = JSON.parse(pasteData.content)
      
      // Decrypt the content
      const decrypted = await decryptContent(encryptedData)
      
      if (decrypted) {
        setDecryptedContent(decrypted)
      } else {
        setDecryptionError('Failed to decrypt content. Invalid key or corrupted data.')
      }
    } catch (error) {
      console.error('Decryption error:', error)
      setDecryptionError('Failed to decrypt content. Invalid key or corrupted data.')
    } finally {
      setIsDecrypting(false)
    }
  }

  const copyToClipboard = async () => {
    if (!paste) return

    try {
      const contentToCopy = paste.is_encrypted && decryptedContent 
        ? decryptedContent 
        : paste.content
      
      await navigator.clipboard.writeText(contentToCopy)
      toast({
        title: "Copied",
        description: "Content copied to clipboard",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      })
    }
  }

  const copyLinkToClipboard = async () => {
    if (!paste) return

    try {
      let url = paste.custom_url 
        ? `${window.location.origin}/p/${paste.custom_url}`
        : `${window.location.origin}/paste/${paste.id}`
      
      // Include encryption key in URL if paste is encrypted
      if (paste.is_encrypted && window.location.hash) {
        url += window.location.hash
      }
      
      await navigator.clipboard.writeText(url)
      toast({
        title: "Copied",
        description: paste.is_encrypted 
          ? "Encrypted link copied to clipboard" 
          : "Link copied to clipboard",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy link to clipboard",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async () => {
    if (!paste) return

    if (!confirm('Are you sure you want to delete this paste? This action cannot be undone.')) {
      return
    }

    setIsDeleting(true)

    try {
      await pasteAPI.delete(paste.id)
      toast({
        title: "Success",
        description: "Paste deleted successfully",
      })
      navigate('/')
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Failed to delete paste",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const canEdit = user && paste && paste.user_id === user.id

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <SkeletonCard />
        <SkeletonCodeEditor />
        <SkeletonCard />
      </div>
    )
  }

  if (!paste) {
    return (
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardContent className="p-8">
            <div className="text-center">Paste not found</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Real-time Presence Indicator */}
      <PresenceIndicator
        isConnected={isConnected}
        connectionState={connectionState}
        activeUsers={activeUsers}
        userCount={userCount}
        reconnectAttempts={reconnectAttempts}
      />

      {/* Paste Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle>{paste.title}</CardTitle>
              {paste.is_encrypted && (
                <div className="flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 rounded-md text-xs">
                  <Shield className="h-3 w-3" />
                  Encrypted
                </div>
              )}
            </div>
            <div className="flex space-x-2">
              <Button 
                onClick={copyToClipboard} 
                variant="outline" 
                size="sm"
                disabled={paste.is_encrypted && !decryptedContent}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Content
              </Button>
              <Button onClick={copyLinkToClipboard} variant="outline" size="sm">
                <LinkIcon className="h-4 w-4 mr-2" />
                {paste.is_encrypted ? 'Copy Encrypted Link' : 'Copy Link'}
              </Button>
              {canEdit && (
                <>
                  <Button asChild variant="outline" size="sm">
                    <Link to={`/paste/${paste.id}/edit`}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Link>
                  </Button>
                  <Button 
                    onClick={handleDelete} 
                    variant="destructive" 
                    size="sm"
                    disabled={isDeleting}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {isDeleting ? 'Deleting...' : 'Delete'}
                  </Button>
                </>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              {new Date(paste.created_at).toLocaleDateString()}
            </div>
            <div className="flex items-center">
              <Eye className="h-4 w-4 mr-1" />
              {paste.view_count} views
            </div>
            <div className="flex items-center">
              <User className="h-4 w-4 mr-1" />
              {paste.language}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Expiration Warning */}
      <ExpirationWarning paste={paste} />

      {/* Encryption Status */}
      {paste.is_encrypted && (
        <Card className={`border-l-4 ${
          decryptedContent 
            ? 'border-l-green-500 bg-green-50' 
            : decryptionError 
            ? 'border-l-red-500 bg-red-50' 
            : 'border-l-yellow-500 bg-yellow-50'
        }`}>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              {isDecrypting ? (
                <>
                  <Key className="h-5 w-5 text-yellow-600 animate-pulse" />
                  <span className="font-medium text-yellow-800">Decrypting content...</span>
                </>
              ) : decryptedContent ? (
                <>
                  <Shield className="h-5 w-5 text-green-600" />
                  <span className="font-medium text-green-800">Content successfully decrypted</span>
                </>
              ) : decryptionError ? (
                <>
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  <div>
                    <span className="font-medium text-red-800">Decryption failed</span>
                    <p className="text-sm text-red-700 mt-1">{decryptionError}</p>
                  </div>
                </>
              ) : (
                <>
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                  <span className="font-medium text-yellow-800">This paste is encrypted but no decryption key was found</span>
                </>
              )}
            </div>
            
            {paste.is_encrypted && !isEncryptionSupported && (
              <div className="mt-2 p-2 bg-red-100 border border-red-200 rounded text-sm text-red-700">
                Your browser doesn't support the Web Crypto API required for decryption.
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Paste Content */}
      {paste.is_encrypted && !decryptedContent ? (
        <Card>
          <CardContent className="p-8">
            <div className="text-center space-y-4">
              <Shield className="h-16 w-16 text-gray-400 mx-auto" />
              <div>
                <h3 className="text-lg font-medium text-gray-900">Encrypted Content</h3>
                <p className="text-gray-600 mt-2">
                  {decryptionError 
                    ? 'Unable to decrypt this paste. Please check that you have the correct decryption key.'
                    : 'This paste is encrypted and cannot be displayed without the decryption key.'
                  }
                </p>
                {!isEncryptionSupported && (
                  <p className="text-red-600 mt-2 text-sm">
                    Your browser doesn't support the encryption features required to view this paste.
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <CodeEditor
          value={paste.is_encrypted ? (decryptedContent || '') : paste.content}
          language={paste.language}
          readOnly={true}
          height="500px"
          showToolbar={true}
          showLanguageSelector={false}
          title={paste.title}
        />
      )}

      {/* Dependency Information */}
      {paste.language && (!paste.is_encrypted || decryptedContent) && (
        <DependencyInfo
          pasteId={paste.id}
          language={paste.language}
          content={paste.is_encrypted ? decryptedContent || undefined : undefined}
        />
      )}

      {/* Multi-Format Viewer */}
      {paste.language && (!paste.is_encrypted || decryptedContent) && (
        <MultiFormatViewer
          content={paste.is_encrypted ? (decryptedContent || '') : paste.content}
          language={paste.language}
          title={`${paste.language.toUpperCase()} Preview`}
        />
      )}

      {/* Paste Metadata */}
      <Card>
        <CardHeader>
          <CardTitle>Paste Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
            <div>
              <div className="font-medium">Language</div>
              <div className="text-muted-foreground">{paste.language}</div>
            </div>
            <div>
              <div className="font-medium">Created</div>
              <div className="text-muted-foreground">
                {new Date(paste.created_at).toLocaleString()}
              </div>
            </div>
            <div>
              <div className="font-medium">Updated</div>
              <div className="text-muted-foreground">
                {new Date(paste.updated_at).toLocaleString()}
              </div>
            </div>
            <div>
              <div className="font-medium">Views</div>
              <div className="text-muted-foreground">{paste.view_count}</div>
            </div>
            {paste.custom_url && (
              <div>
                <div className="font-medium">Custom URL</div>
                <div className="text-muted-foreground font-mono">/p/{paste.custom_url}</div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Version History */}
      {paste && user && (
        <VersionHistory
          pasteId={paste.id}
          onVersionSelect={(version) => {
            // Handle version selection - could show version content in a modal
            console.log('Selected version:', version);
          }}
        />
      )}

      {/* Audit Trail */}
      {paste && user && (
        <AuditTrail pasteId={paste.id} showWatermarks={true} />
      )}

      {/* Chat Panel */}
      {paste && (
        <ChatPanel
          pasteId={paste.id}
          onLineReference={(lineNumber) => {
            // Scroll to line in code editor
            // This would need to be implemented in the CodeEditor component
            console.log('Navigate to line:', lineNumber);
          }}
        />
      )}
    </div>
  )
}