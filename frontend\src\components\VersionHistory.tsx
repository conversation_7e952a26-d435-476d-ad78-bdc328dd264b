import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { History, Clock, User, GitBranch, Eye, ArrowRight } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { versionAPI, PasteVersion, VersionComparison } from '@/lib/api';

interface VersionHistoryProps {
  pasteId: string;
  onVersionSelect?: (version: PasteVersion) => void;
}

export default function VersionHistory({ pasteId, onVersionSelect }: VersionHistoryProps) {
  const [versions, setVersions] = useState<PasteVersion[]>([]);
  const [selectedVersions, setSelectedVersions] = useState<string[]>([]);
  const [comparison, setComparison] = useState<VersionComparison | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showComparison, setShowComparison] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadVersions();
  }, [pasteId]);

  const loadVersions = async () => {
    try {
      setIsLoading(true);
      const response = await versionAPI.getVersions(pasteId);
      setVersions(response.data.versions || []);
    } catch (error) {
      console.error('Error loading versions:', error);
      toast({
        title: "Error",
        description: "Failed to load version history",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleVersionSelect = (versionId: string) => {
    if (showComparison) {
      // For comparison mode, allow selecting two versions
      if (selectedVersions.includes(versionId)) {
        setSelectedVersions(prev => prev.filter(id => id !== versionId));
      } else if (selectedVersions.length < 2) {
        setSelectedVersions(prev => [...prev, versionId]);
      } else {
        // Replace the first selected version
        setSelectedVersions([selectedVersions[1], versionId]);
      }
    } else {
      // For single selection mode
      const version = versions.find(v => v.id === versionId);
      if (version && onVersionSelect) {
        onVersionSelect(version);
      }
    }
  };

  const compareVersions = async () => {
    if (selectedVersions.length !== 2) {
      toast({
        title: "Error",
        description: "Please select exactly two versions to compare",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      const response = await versionAPI.compareVersions(selectedVersions[0], selectedVersions[1]);
      setComparison(response.data);
    } catch (error) {
      console.error('Error comparing versions:', error);
      toast({
        title: "Error",
        description: "Failed to compare versions",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const renderVersionItem = (version: PasteVersion) => {
    const isSelected = selectedVersions.includes(version.id);
    const isLatest = version.version_number === Math.max(...versions.map(v => v.version_number));

    return (
      <div
        key={version.id}
        className={`border rounded-lg p-3 cursor-pointer transition-colors ${
          isSelected ? 'bg-primary/10 border-primary' : 'hover:bg-muted/50'
        }`}
        onClick={() => handleVersionSelect(version.id)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <GitBranch className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="flex items-center space-x-2">
                <span className="font-medium">Version {version.version_number}</span>
                {isLatest && <Badge variant="default">Latest</Badge>}
              </div>
              <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span>{formatTimestamp(version.created_at)}</span>
                </div>
                {version.created_by_username && (
                  <div className="flex items-center space-x-1">
                    <User className="h-3 w-3" />
                    <span>{version.created_by_username}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {showComparison && isSelected && (
              <Badge variant="outline">Selected</Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                if (onVersionSelect) {
                  onVersionSelect(version);
                }
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  const renderComparison = () => {
    if (!comparison) return null;

    return (
      <Card className="mt-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Version Comparison
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm">
              <Badge variant="outline">Version {comparison.version1.version_number}</Badge>
              <span className="mx-2">vs</span>
              <Badge variant="outline">Version {comparison.version2.version_number}</Badge>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setComparison(null);
                setSelectedVersions([]);
              }}
            >
              Close
            </Button>
          </div>
          
          <ScrollArea className="h-96 border rounded-md p-4">
            <div className="font-mono text-sm space-y-1">
              {comparison.differences.map((diff, index) => (
                <div
                  key={index}
                  className={`px-2 py-1 rounded ${
                    diff.type === 'added'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : diff.type === 'removed'
                      ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      : 'bg-gray-50 dark:bg-gray-800'
                  }`}
                >
                  <span className="text-xs text-muted-foreground mr-2">
                    {diff.line_num}
                  </span>
                  <span className="mr-2">
                    {diff.type === 'added' ? '+' : diff.type === 'removed' ? '-' : ' '}
                  </span>
                  <span>{diff.content}</span>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    );
  };

  if (isLoading && versions.length === 0) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="text-center">Loading version history...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <History className="h-5 w-5" />
              Version History ({versions.length})
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant={showComparison ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setShowComparison(!showComparison);
                  setSelectedVersions([]);
                  setComparison(null);
                }}
              >
                Compare Versions
              </Button>
              {showComparison && selectedVersions.length === 2 && (
                <Button size="sm" onClick={compareVersions} disabled={isLoading}>
                  <ArrowRight className="h-4 w-4 mr-1" />
                  Compare
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {versions.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              No version history available
            </div>
          ) : (
            <ScrollArea className="h-96">
              <div className="space-y-2">
                {versions.map(renderVersionItem)}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {comparison && renderComparison()}
    </div>
  );
}
