import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base-page';

export class LoginPage extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  // Page-specific locators
  get pageTitle(): Locator {
    return this.page.locator('h1').first();
  }

  get loginForm(): Locator {
    return this.page.locator('form, [data-testid="login-form"]').first();
  }

  get usernameInput(): Locator {
    return this.page.locator('input[name="username"], input[type="text"]').first();
  }

  get passwordInput(): Locator {
    return this.page.locator('input[name="password"], input[type="password"]').first();
  }

  get submitButton(): Locator {
    return this.page.locator('button[type="submit"], button:has-text("Login")').first();
  }

  get registerLink(): Locator {
    return this.page.locator('a[href="/register"], a:has-text("Register")').first();
  }

  get forgotPasswordLink(): Locator {
    return this.page.locator('a:has-text("Forgot"), a:has-text("Reset")').first();
  }

  get rememberMeCheckbox(): Locator {
    return this.page.locator('input[type="checkbox"][name="remember"], input[type="checkbox"]:has-text("Remember")').first();
  }

  get validationErrors(): Locator {
    return this.page.locator('.field-error, .validation-error, [data-testid="field-error"]');
  }

  get usernameError(): Locator {
    return this.page.locator('.field-error:near(input[name="username"]), [data-testid="username-error"]').first();
  }

  get passwordError(): Locator {
    return this.page.locator('.field-error:near(input[name="password"]), [data-testid="password-error"]').first();
  }

  // Page actions
  async visit(): Promise<void> {
    await this.page.goto('/login');
    await this.waitForPageLoad();
  }

  async fillUsername(username: string): Promise<void> {
    await this.usernameInput.fill(username);
  }

  async fillPassword(password: string): Promise<void> {
    await this.passwordInput.fill(password);
  }

  async checkRememberMe(): Promise<void> {
    if (await this.rememberMeCheckbox.isVisible()) {
      await this.rememberMeCheckbox.check();
    }
  }

  async uncheckRememberMe(): Promise<void> {
    if (await this.rememberMeCheckbox.isVisible()) {
      await this.rememberMeCheckbox.uncheck();
    }
  }

  async submitForm(): Promise<void> {
    await this.submitButton.click();
  }

  async login(username: string, password: string, rememberMe: boolean = false): Promise<void> {
    await this.fillUsername(username);
    await this.fillPassword(password);
    
    if (rememberMe) {
      await this.checkRememberMe();
    }
    
    await this.submitForm();
  }

  async loginAndWaitForRedirect(username: string, password: string, expectedUrl: string | RegExp = '/'): Promise<void> {
    await this.login(username, password);
    await this.page.waitForURL(expectedUrl);
  }

  async clickRegisterLink(): Promise<void> {
    await this.registerLink.click();
    await this.page.waitForURL('/register');
  }

  async clickForgotPasswordLink(): Promise<void> {
    if (await this.forgotPasswordLink.isVisible()) {
      await this.forgotPasswordLink.click();
      await this.page.waitForURL(/\/forgot|\/reset/);
    }
  }

  // Page assertions
  async expectToBeOnLoginPage(): Promise<void> {
    await expect(this.page).toHaveURL('/login');
    await expect(this.pageTitle).toContainText(/login/i);
  }

  async expectLoginForm(): Promise<void> {
    await expect(this.loginForm).toBeVisible();
    await expect(this.usernameInput).toBeVisible();
    await expect(this.passwordInput).toBeVisible();
    await expect(this.submitButton).toBeVisible();
  }

  async expectUsernameError(message: string): Promise<void> {
    await expect(this.usernameError).toBeVisible();
    await expect(this.usernameError).toContainText(message);
  }

  async expectPasswordError(message: string): Promise<void> {
    await expect(this.passwordError).toBeVisible();
    await expect(this.passwordError).toContainText(message);
  }

  async expectValidationErrors(): Promise<void> {
    await expect(this.validationErrors.first()).toBeVisible();
  }

  async expectNoValidationErrors(): Promise<void> {
    await expect(this.validationErrors).toHaveCount(0);
  }

  async expectLoginSuccess(): Promise<void> {
    // Should be redirected away from login page
    await expect(this.page).not.toHaveURL('/login');
    
    // Should show user menu or profile indicator
    await this.expectToBeLoggedIn();
  }

  async expectLoginFailure(errorMessage?: string): Promise<void> {
    // Should still be on login page
    await expect(this.page).toHaveURL('/login');
    
    if (errorMessage) {
      await this.expectErrorMessage(errorMessage);
    } else {
      // Should show some error indication
      await expect(this.errorMessage.or(this.validationErrors.first())).toBeVisible();
    }
  }

  async expectRegisterLinkVisible(): Promise<void> {
    await expect(this.registerLink).toBeVisible();
  }

  async expectForgotPasswordLinkVisible(): Promise<void> {
    await expect(this.forgotPasswordLink).toBeVisible();
  }

  // Form validation helpers
  async expectRequiredFieldValidation(): Promise<void> {
    // Try to submit empty form
    await this.submitForm();
    
    // Should show validation errors
    await this.expectValidationErrors();
  }

  async expectInvalidCredentialsError(): Promise<void> {
    await this.expectErrorMessage(/invalid|incorrect|wrong/i);
  }

  async expectAccountLockedError(): Promise<void> {
    await this.expectErrorMessage(/locked|suspended|disabled/i);
  }

  // Utility methods
  async clearForm(): Promise<void> {
    await this.usernameInput.clear();
    await this.passwordInput.clear();
  }

  async isFormValid(): Promise<boolean> {
    const usernameValue = await this.usernameInput.inputValue();
    const passwordValue = await this.passwordInput.inputValue();
    
    return usernameValue.length > 0 && passwordValue.length > 0;
  }

  async getFormData(): Promise<{ username: string; password: string; rememberMe: boolean }> {
    const username = await this.usernameInput.inputValue();
    const password = await this.passwordInput.inputValue();
    const rememberMe = await this.rememberMeCheckbox.isChecked();
    
    return { username, password, rememberMe };
  }

  async waitForFormSubmission(): Promise<void> {
    // Wait for form submission to complete (either success or error)
    await Promise.race([
      this.page.waitForURL(url => !url.includes('/login')),
      this.errorMessage.waitFor(),
      this.validationErrors.first().waitFor()
    ]);
  }
}
