interface LanguagePattern {
  language: string;
  patterns: RegExp[];
  keywords: string[];
  extensions: string[];
  weight: number;
}

const languagePatterns: LanguagePattern[] = [
  {
    language: 'javascript',
    patterns: [
      /function\s+\w+\s*\(/,
      /const\s+\w+\s*=/,
      /let\s+\w+\s*=/,
      /var\s+\w+\s*=/,
      /=>\s*{/,
      /console\.log\(/,
      /require\s*\(/,
      /import\s+.*from/,
      /export\s+(default\s+)?/,
    ],
    keywords: ['function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return', 'class', 'extends'],
    extensions: ['.js', '.mjs'],
    weight: 1
  },
  {
    language: 'typescript',
    patterns: [
      /interface\s+\w+/,
      /type\s+\w+\s*=/,
      /:\s*(string|number|boolean|any|void)/,
      /function\s+\w+\s*\([^)]*:\s*\w+/,
      /import\s+.*from\s+['"][^'"]+['"];?$/m,
    ],
    keywords: ['interface', 'type', 'enum', 'namespace', 'declare', 'readonly', 'private', 'public', 'protected'],
    extensions: ['.ts', '.tsx'],
    weight: 1.2
  },
  {
    language: 'python',
    patterns: [
      /def\s+\w+\s*\(/,
      /class\s+\w+\s*(\(.*\))?:/,
      /import\s+\w+/,
      /from\s+\w+\s+import/,
      /if\s+__name__\s*==\s*['"]__main__['"]:/,
      /print\s*\(/,
      /@\w+/,
    ],
    keywords: ['def', 'class', 'import', 'from', 'if', 'elif', 'else', 'for', 'while', 'try', 'except', 'with', 'as'],
    extensions: ['.py', '.pyw'],
    weight: 1
  },
  {
    language: 'java',
    patterns: [
      /public\s+class\s+\w+/,
      /public\s+static\s+void\s+main/,
      /import\s+java\./,
      /System\.out\.println/,
      /@Override/,
      /extends\s+\w+/,
      /implements\s+\w+/,
    ],
    keywords: ['public', 'private', 'protected', 'class', 'interface', 'extends', 'implements', 'static', 'final'],
    extensions: ['.java'],
    weight: 1
  },
  {
    language: 'go',
    patterns: [
      /package\s+\w+/,
      /func\s+\w+\s*\(/,
      /import\s+\(/,
      /fmt\.Print/,
      /var\s+\w+\s+\w+/,
      /type\s+\w+\s+struct/,
      /:=\s*/,
    ],
    keywords: ['package', 'import', 'func', 'var', 'const', 'type', 'struct', 'interface', 'if', 'else', 'for', 'range'],
    extensions: ['.go'],
    weight: 1
  },
  {
    language: 'rust',
    patterns: [
      /fn\s+\w+\s*\(/,
      /let\s+mut\s+\w+/,
      /let\s+\w+\s*=/,
      /use\s+\w+::/,
      /struct\s+\w+/,
      /impl\s+\w+/,
      /println!\s*\(/,
    ],
    keywords: ['fn', 'let', 'mut', 'use', 'struct', 'enum', 'impl', 'trait', 'if', 'else', 'match', 'for', 'while'],
    extensions: ['.rs'],
    weight: 1
  },
  {
    language: 'cpp',
    patterns: [
      /#include\s*<.*>/,
      /using\s+namespace\s+std/,
      /int\s+main\s*\(/,
      /std::/,
      /cout\s*<<|cin\s*>>/,
      /class\s+\w+/,
      /template\s*</,
    ],
    keywords: ['include', 'using', 'namespace', 'class', 'struct', 'template', 'public', 'private', 'protected'],
    extensions: ['.cpp', '.cc', '.cxx', '.c++'],
    weight: 1
  },
  {
    language: 'c',
    patterns: [
      /#include\s*<.*\.h>/,
      /int\s+main\s*\(/,
      /printf\s*\(/,
      /scanf\s*\(/,
      /malloc\s*\(/,
      /struct\s+\w+/,
    ],
    keywords: ['include', 'int', 'char', 'float', 'double', 'void', 'struct', 'union', 'typedef', 'static'],
    extensions: ['.c', '.h'],
    weight: 0.8
  },
  {
    language: 'csharp',
    patterns: [
      /using\s+System/,
      /namespace\s+\w+/,
      /public\s+class\s+\w+/,
      /Console\.WriteLine/,
      /\[.*\]/,
      /public\s+static\s+void\s+Main/,
    ],
    keywords: ['using', 'namespace', 'class', 'interface', 'public', 'private', 'protected', 'static', 'void'],
    extensions: ['.cs'],
    weight: 1
  },
  {
    language: 'php',
    patterns: [
      /<\?php/,
      /\$\w+\s*=/,
      /function\s+\w+\s*\(/,
      /class\s+\w+/,
      /echo\s+/,
      /require_once|include_once/,
    ],
    keywords: ['function', 'class', 'echo', 'print', 'if', 'else', 'foreach', 'while', 'for', 'require', 'include'],
    extensions: ['.php'],
    weight: 1
  },
  {
    language: 'ruby',
    patterns: [
      /def\s+\w+/,
      /class\s+\w+/,
      /require\s+['"][^'"]+['"]$/m,
      /puts\s+/,
      /end$/m,
      /@\w+/,
    ],
    keywords: ['def', 'class', 'module', 'require', 'if', 'else', 'elsif', 'end', 'puts', 'print'],
    extensions: ['.rb'],
    weight: 1
  },
  {
    language: 'json',
    patterns: [
      /^\s*{[\s\S]*}$/,
      /^\s*\[[\s\S]*\]$/,
      /"[^"]*"\s*:\s*"[^"]*"/,
      /"[^"]*"\s*:\s*\d+/,
      /"[^"]*"\s*:\s*(true|false|null)/,
    ],
    keywords: [],
    extensions: ['.json'],
    weight: 1.5
  },
  {
    language: 'xml',
    patterns: [
      /<\?xml\s+version/,
      /<[^>]+>[^<]*<\/[^>]+>/,
      /<[^/>]+\/>/,
    ],
    keywords: [],
    extensions: ['.xml'],
    weight: 1.2
  },
  {
    language: 'html',
    patterns: [
      /<!DOCTYPE\s+html>/i,
      /<html[^>]*>/i,
      /<head[^>]*>/i,
      /<body[^>]*>/i,
      /<div[^>]*>/i,
      /<script[^>]*>/i,
    ],
    keywords: [],
    extensions: ['.html', '.htm'],
    weight: 1
  },
  {
    language: 'css',
    patterns: [
      /[.#]?\w+\s*{[^}]*}/,
      /@media\s+/,
      /@import\s+/,
      /:\s*[^;]+;/,
    ],
    keywords: [],
    extensions: ['.css'],
    weight: 1
  },
  {
    language: 'sql',
    patterns: [
      /SELECT\s+.*\s+FROM/i,
      /INSERT\s+INTO/i,
      /UPDATE\s+.*\s+SET/i,
      /DELETE\s+FROM/i,
      /CREATE\s+TABLE/i,
      /ALTER\s+TABLE/i,
    ],
    keywords: ['SELECT', 'FROM', 'WHERE', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP', 'TABLE'],
    extensions: ['.sql'],
    weight: 1
  },
  {
    language: 'markdown',
    patterns: [
      /^#{1,6}\s+/m,
      /\*\*[^*]+\*\*/,
      /\*[^*]+\*/,
      /\[[^\]]+\]\([^)]+\)/,
      /```[\s\S]*?```/,
      /`[^`]+`/,
      /^\* /m,
      /^\d+\. /m,
    ],
    keywords: [],
    extensions: ['.md', '.markdown'],
    weight: 1
  },
  {
    language: 'yaml',
    patterns: [
      /^---\s*$/m,
      /^\s*\w+:\s*\w+/m,
      /^\s*-\s+\w+/m,
      /^\s*\w+:\s*$/m,
    ],
    keywords: [],
    extensions: ['.yml', '.yaml'],
    weight: 1
  },
];

export function detectLanguage(content: string, filename?: string): string {
  if (!content.trim()) {
    return 'text';
  }

  const scores: { [key: string]: number } = {};

  // Check filename extension first
  if (filename) {
    for (const pattern of languagePatterns) {
      for (const ext of pattern.extensions) {
        if (filename.toLowerCase().endsWith(ext)) {
          scores[pattern.language] = (scores[pattern.language] || 0) + 2;
        }
      }
    }
  }

  // Check patterns and keywords
  for (const pattern of languagePatterns) {
    let score = 0;

    // Check regex patterns
    for (const regex of pattern.patterns) {
      if (regex.test(content)) {
        score += pattern.weight;
      }
    }

    // Check keywords
    const words = content.toLowerCase().split(/\W+/);
    for (const keyword of pattern.keywords) {
      if (words.includes(keyword.toLowerCase())) {
        score += 0.5;
      }
    }

    if (score > 0) {
      scores[pattern.language] = (scores[pattern.language] || 0) + score;
    }
  }

  // Find the language with the highest score
  const sortedLanguages = Object.entries(scores)
    .sort(([, a], [, b]) => b - a);

  if (sortedLanguages.length > 0 && sortedLanguages[0][1] > 0) {
    return sortedLanguages[0][0];
  }

  return 'text';
}

export function getLanguageDisplayName(language: string): string {
  const displayNames: { [key: string]: string } = {
    'javascript': 'JavaScript',
    'typescript': 'TypeScript',
    'python': 'Python',
    'java': 'Java',
    'go': 'Go',
    'rust': 'Rust',
    'cpp': 'C++',
    'c': 'C',
    'csharp': 'C#',
    'php': 'PHP',
    'ruby': 'Ruby',
    'json': 'JSON',
    'xml': 'XML',
    'html': 'HTML',
    'css': 'CSS',
    'sql': 'SQL',
    'markdown': 'Markdown',
    'yaml': 'YAML',
    'text': 'Plain Text',
  };

  return displayNames[language.toLowerCase()] || language;
}
