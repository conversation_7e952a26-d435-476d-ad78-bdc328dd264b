package services

import (
	"errors"
	"time"

	"enhanced-pastebin/internal/models"
	"enhanced-pastebin/internal/repositories"

	"github.com/google/uuid"
)

type ChatService interface {
	CreateMessage(req *models.CreateChatMessageRequest, userID string) (*models.ChatMessage, error)
	GetMessagesByPasteID(pasteID string, limit, offset int) ([]*models.ChatMessage, error)
	GetMessageByID(id string) (*models.ChatMessage, error)
	DeleteMessage(id, userID string) error
	GetMessageCount(pasteID string) (int, error)
}

type chatService struct {
	chatRepo repositories.ChatRepository
}

func NewChatService(chatRepo repositories.ChatRepository) ChatService {
	return &chatService{
		chatRepo: chatRepo,
	}
}

func (s *chatService) CreateMessage(req *models.CreateChatMessageRequest, userID string) (*models.ChatMessage, error) {
	message := &models.ChatMessage{
		ID:             uuid.New().String(),
		PasteID:        req.PasteID,
		UserID:         userID,
		Content:        req.Content,
		LineReferences: req.LineReferences,
		CreatedAt:      time.Now(),
	}

	if err := s.chatRepo.Create(message); err != nil {
		return nil, err
	}

	// Get the message with username for response
	return s.chatRepo.GetByID(message.ID)
}

func (s *chatService) GetMessagesByPasteID(pasteID string, limit, offset int) ([]*models.ChatMessage, error) {
	return s.chatRepo.GetByPasteID(pasteID, limit, offset)
}

func (s *chatService) GetMessageByID(id string) (*models.ChatMessage, error) {
	return s.chatRepo.GetByID(id)
}

func (s *chatService) DeleteMessage(id, userID string) error {
	// Get message to verify ownership
	message, err := s.chatRepo.GetByID(id)
	if err != nil {
		return err
	}
	if message == nil {
		return errors.New("message not found")
	}
	if message.UserID != userID {
		return errors.New("unauthorized to delete this message")
	}

	return s.chatRepo.Delete(id)
}

func (s *chatService) GetMessageCount(pasteID string) (int, error) {
	return s.chatRepo.GetMessageCount(pasteID)
}
