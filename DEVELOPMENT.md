# Development Guide

This document provides comprehensive information for developers working on the Enhanced Pastebin project.

## 🏗 Architecture Overview

### Backend Architecture

The backend follows a clean architecture pattern with clear separation of concerns:

```
cmd/server/          # Application entry point
internal/
├── config/          # Configuration management
├── database/        # Database connection and setup
├── handlers/        # HTTP request handlers (controllers)
├── middleware/      # HTTP middleware (auth, logging, etc.)
├── models/          # Data models and DTOs
├── repositories/    # Data access layer
├── services/        # Business logic layer
└── utils/           # Utility functions
```

### Frontend Architecture

The frontend uses a component-based architecture with React:

```
src/
├── components/      # Reusable UI components
│   ├── ui/         # Base UI components (buttons, inputs, etc.)
│   └── __tests__/  # Component tests
├── contexts/        # React contexts for state management
├── hooks/          # Custom React hooks
├── lib/            # Utilities and API client
├── pages/          # Page components (routes)
└── test-utils.tsx  # Testing utilities
```

## 🔧 Development Setup

### Prerequisites

- **Go 1.21+**: Backend development
- **Node.js 18+**: Frontend development
- **PostgreSQL 14+**: Database
- **Docker**: For containerized development
- **Make**: For convenience commands (optional)

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd enhanced-pastebin
   ```

2. **Set up environment files**
   ```bash
   cp .env.example .env
   cp frontend/.env.example frontend/.env
   ```

3. **Configure environment variables**
   
   Backend (`.env`):
   ```env
   DATABASE_URL=postgres://username:password@localhost/enhanced_pastebin?sslmode=disable
   JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
   PORT=8080
   GIN_MODE=debug
   CORS_ORIGINS=http://localhost:3000
   ENABLE_RATE_LIMITING=true
   ENABLE_SECURITY_SCANNING=true
   ENABLE_WATERMARKING=true
   CLEANUP_INTERVAL=1h
   ```

   Frontend (`frontend/.env`):
   ```env
   VITE_API_URL=http://localhost:8080/api/v1
   VITE_WS_URL=ws://localhost:8080/api/v1/ws
   ```

4. **Start development services**
   ```bash
   docker-compose up -d  # PostgreSQL and Redis
   ```

5. **Install dependencies**
   ```bash
   # Backend
   go mod download
   
   # Frontend
   cd frontend && npm install
   ```

6. **Run database migrations**
   ```bash
   migrate -path migrations -database $DATABASE_URL up
   ```

## 🚀 Development Workflow

### Running the Application

**Option 1: Using Make (recommended)**
```bash
make dev  # Starts both backend and frontend
```

**Option 2: Manual startup**
```bash
# Terminal 1: Backend
go run cmd/server/main.go

# Terminal 2: Frontend
cd frontend && npm run dev
```

### Available Make Commands

- `make setup` - Complete development environment setup
- `make dev` - Start both backend and frontend servers
- `make dev-backend` - Start only backend server
- `make dev-frontend` - Start only frontend server
- `make test` - Run all tests
- `make test-backend` - Run backend tests
- `make test-frontend` - Run frontend tests
- `make build` - Build production artifacts
- `make migrate-up` - Run database migrations
- `make migrate-down` - Rollback database migrations
- `make clean` - Clean build artifacts
- `make db-reset` - Reset database (⚠️ deletes all data)

### Code Style and Standards

#### Backend (Go)

- Follow standard Go conventions
- Use `gofmt` for formatting
- Use `golint` for linting
- Write comprehensive tests for all services
- Use dependency injection for testability

Example service structure:
```go
type UserService interface {
    CreateUser(req *CreateUserRequest) (*User, error)
    GetUser(id string) (*User, error)
}

type userService struct {
    userRepo UserRepository
}

func NewUserService(userRepo UserRepository) UserService {
    return &userService{userRepo: userRepo}
}
```

#### Frontend (React/TypeScript)

- Use TypeScript for all new code
- Follow React best practices and hooks patterns
- Use Tailwind CSS for styling
- Implement proper error boundaries
- Write tests for all components

Example component structure:
```tsx
interface ComponentProps {
  title: string;
  onAction?: () => void;
}

export default function Component({ title, onAction }: ComponentProps) {
  const [state, setState] = useState<string>('');
  
  return (
    <div className="p-4">
      <h1>{title}</h1>
      {/* Component content */}
    </div>
  );
}
```

## 🧪 Testing

### Backend Testing

**Unit Tests**
```bash
go test ./internal/services/...
go test ./internal/handlers/...
```

**Integration Tests**
```bash
go test ./tests/integration/...
```

**Test Coverage**
```bash
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

### Frontend Testing

**Unit Tests**
```bash
npm test
```

**Component Tests**
```bash
npm run test:components
```

**E2E Tests**
```bash
npm run test:e2e
```

**Test Coverage**
```bash
npm run test:coverage
```

### Writing Tests

#### Backend Test Example
```go
func TestUserService_CreateUser(t *testing.T) {
    mockRepo := &MockUserRepository{}
    service := NewUserService(mockRepo)
    
    req := &CreateUserRequest{
        Username: "testuser",
        Email:    "<EMAIL>",
    }
    
    mockRepo.On("Create", mock.AnythingOfType("*User")).Return(nil)
    
    user, err := service.CreateUser(req)
    
    assert.NoError(t, err)
    assert.Equal(t, "testuser", user.Username)
    mockRepo.AssertExpectations(t)
}
```

#### Frontend Test Example
```tsx
import { render, screen, fireEvent } from '@/test-utils';
import Component from './Component';

describe('Component', () => {
  it('renders correctly', () => {
    render(<Component title="Test Title" />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });
  
  it('handles user interaction', () => {
    const onAction = jest.fn();
    render(<Component title="Test" onAction={onAction} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(onAction).toHaveBeenCalled();
  });
});
```

## 🔍 Debugging

### Backend Debugging

1. **Enable debug mode**
   ```env
   GIN_MODE=debug
   ```

2. **Use VS Code debugger**
   Create `.vscode/launch.json`:
   ```json
   {
     "version": "0.2.0",
     "configurations": [
       {
         "name": "Launch Server",
         "type": "go",
         "request": "launch",
         "mode": "auto",
         "program": "${workspaceFolder}/cmd/server/main.go"
       }
     ]
   }
   ```

3. **Database debugging**
   ```bash
   # Connect to database
   psql $DATABASE_URL
   
   # View logs
   docker logs enhanced-pastebin-db
   ```

### Frontend Debugging

1. **Browser DevTools**
   - Use React Developer Tools extension
   - Check Network tab for API calls
   - Use Console for JavaScript errors

2. **VS Code debugging**
   Install "Debugger for Chrome" extension and configure

3. **Performance debugging**
   - Use the built-in Performance Monitor (dev mode)
   - Check React Profiler
   - Monitor bundle size with `npm run analyze`

## 📊 Performance Optimization

### Backend Performance

1. **Database optimization**
   - Use proper indexes
   - Implement connection pooling
   - Use prepared statements

2. **Caching strategies**
   - Implement Redis caching for frequently accessed data
   - Use in-memory caching for static data

3. **API optimization**
   - Implement pagination
   - Use compression middleware
   - Optimize JSON serialization

### Frontend Performance

1. **Bundle optimization**
   - Code splitting with React.lazy()
   - Tree shaking unused code
   - Optimize images and assets

2. **Runtime optimization**
   - Use React.memo for expensive components
   - Implement virtual scrolling for large lists
   - Debounce user inputs

3. **Loading strategies**
   - Implement skeleton loading
   - Use progressive loading
   - Preload critical resources

## 🔒 Security Considerations

### Backend Security

1. **Input validation**
   - Validate all inputs
   - Use parameterized queries
   - Implement rate limiting

2. **Authentication & Authorization**
   - Use secure JWT implementation
   - Implement proper session management
   - Use HTTPS in production

3. **Data protection**
   - Encrypt sensitive data
   - Implement audit logging
   - Use secure headers

### Frontend Security

1. **XSS Prevention**
   - Sanitize user inputs
   - Use Content Security Policy
   - Avoid dangerouslySetInnerHTML

2. **Data handling**
   - Don't store sensitive data in localStorage
   - Implement proper error handling
   - Use HTTPS for all API calls

## 🚀 Deployment

### Production Build

1. **Backend**
   ```bash
   CGO_ENABLED=0 GOOS=linux go build -o bin/server cmd/server/main.go
   ```

2. **Frontend**
   ```bash
   npm run build
   ```

### Docker Deployment

```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Environment Configuration

- Use environment-specific configuration files
- Implement proper secret management
- Set up monitoring and logging
- Configure backup strategies

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Make your changes**
4. **Write tests**
5. **Run the test suite**
   ```bash
   make test
   ```
6. **Commit your changes**
   ```bash
   git commit -m 'Add amazing feature'
   ```
7. **Push to the branch**
   ```bash
   git push origin feature/amazing-feature
   ```
8. **Open a Pull Request**

### Pull Request Guidelines

- Include a clear description of changes
- Add tests for new functionality
- Update documentation if needed
- Ensure all tests pass
- Follow the existing code style

## 📚 Additional Resources

- [Go Documentation](https://golang.org/doc/)
- [React Documentation](https://reactjs.org/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Radix UI Documentation](https://www.radix-ui.com/docs)
