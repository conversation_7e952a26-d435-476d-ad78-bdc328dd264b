import { test, expect } from '@playwright/test';
import { CreatePastePage, ViewPastePage, HomePage } from '../../pages';
import { TestHelpers } from '../../utils/test-helpers';

test.describe('Create Paste', () => {
  let createPastePage: CreatePastePage;
  let viewPastePage: ViewPastePage;
  let homePage: HomePage;
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    createPastePage = new CreatePastePage(page);
    viewPastePage = new ViewPastePage(page);
    homePage = new HomePage(page);
    helpers = new TestHelpers();
    
    await createPastePage.visit();
  });

  test.afterEach(async () => {
    await helpers.cleanupTestData();
  });

  test('should display create paste form', async () => {
    await createPastePage.expectToBeOnCreatePage();
    await createPastePage.expectCreateForm();
  });

  test('should create a basic paste successfully', async () => {
    const pasteData = {
      title: 'Test Paste',
      content: 'console.log("Hello, World!");',
      language: 'javascript'
    };

    const pasteId = await createPastePage.createPasteAndWaitForRedirect(pasteData);
    
    // Should be redirected to view paste page
    await viewPastePage.expectToBeOnPastePage(pasteId);
    await viewPastePage.expectPasteTitle(pasteData.title);
    await viewPastePage.expectPasteContent(pasteData.content);
    await viewPastePage.expectPasteLanguage(pasteData.language);
  });

  test('should create paste with custom URL', async () => {
    const timestamp = Date.now();
    const pasteData = {
      title: 'Custom URL Paste',
      content: 'print("Custom URL test")',
      language: 'python',
      customUrl: `custom-paste-${timestamp}`
    };

    await createPastePage.createPaste(pasteData);
    await createPastePage.expectCreateSuccess();

    // Should be able to access via custom URL
    await viewPastePage.visitByCustomUrl(pasteData.customUrl!);
    await viewPastePage.expectPasteTitle(pasteData.title);
    await viewPastePage.expectPasteContent(pasteData.content);
  });

  test('should create private paste', async ({ page }) => {
    // Login first for private paste
    const user = await helpers.loginAsTestUser(page);

    const pasteData = {
      title: 'Private Paste',
      content: 'secret_key = "very_secret"',
      language: 'python',
      isPublic: false
    };

    const pasteId = await createPastePage.createPasteAndWaitForRedirect(pasteData);
    
    await viewPastePage.expectToBeOnPastePage(pasteId);
    await viewPastePage.expectPasteTitle(pasteData.title);
    await viewPastePage.expectPastePrivate();
  });

  test('should create paste with expiration', async () => {
    const pasteData = {
      title: 'Expiring Paste',
      content: 'This paste will expire',
      expiration: '1h'
    };

    const pasteId = await createPastePage.createPasteAndWaitForRedirect(pasteData);
    
    await viewPastePage.expectToBeOnPastePage(pasteId);
    await viewPastePage.expectExpirationWarning();
  });

  test('should show validation errors for empty form', async () => {
    await createPastePage.submitForm();
    await createPastePage.expectValidationErrors();
    await createPastePage.expectTitleRequired();
    await createPastePage.expectContentRequired();
  });

  test('should show validation error for empty title', async () => {
    await createPastePage.fillContent('Some content');
    await createPastePage.submitForm();
    await createPastePage.expectTitleError(/required|empty/i);
  });

  test('should show validation error for empty content', async () => {
    await createPastePage.fillTitle('Some title');
    await createPastePage.submitForm();
    await createPastePage.expectContentError(/required|empty/i);
  });

  test('should validate custom URL availability', async () => {
    const timestamp = Date.now();
    const customUrl = `test-url-${timestamp}`;

    // Create a paste with custom URL first
    await helpers.api.createTestPaste({ custom_url: customUrl });

    // Try to use the same custom URL
    await createPastePage.fillTitle('Test Paste');
    await createPastePage.fillContent('Test content');
    await createPastePage.fillCustomUrl(customUrl);

    // Should show URL unavailable message
    await createPastePage.expectCustomUrlUnavailable();
  });

  test('should show custom URL available for unique URLs', async () => {
    const timestamp = Date.now();
    const customUrl = `unique-url-${timestamp}`;

    await createPastePage.fillCustomUrl(customUrl);
    await createPastePage.expectCustomUrlAvailable();
  });

  test('should auto-detect language from content', async () => {
    const jsContent = `
      function hello() {
        console.log("Hello, World!");
      }
    `;

    await createPastePage.fillContent(jsContent);
    
    // Language should be auto-detected as JavaScript
    await createPastePage.expectLanguageSelected('javascript');
  });

  test('should format code when format button is clicked', async () => {
    const unformattedJs = 'function test(){console.log("test");}';
    
    await createPastePage.fillContent(unformattedJs);
    await createPastePage.formatCode();

    // Content should be formatted
    const formattedContent = await createPastePage.getContentValue();
    expect(formattedContent).toContain('function test() {');
    expect(formattedContent).toContain('  console.log("test");');
  });

  test('should show preview when preview button is clicked', async () => {
    const pasteData = {
      title: 'Preview Test',
      content: '# Markdown Title\n\nThis is **bold** text.',
      language: 'markdown'
    };

    await createPastePage.fillTitle(pasteData.title);
    await createPastePage.fillContent(pasteData.content);
    await createPastePage.selectLanguage(pasteData.language);
    
    await createPastePage.previewPaste();
    await createPastePage.expectPreviewVisible();
  });

  test('should handle large paste content', async () => {
    const largeContent = 'console.log("test");\n'.repeat(1000);
    
    const pasteData = {
      title: 'Large Paste',
      content: largeContent,
      language: 'javascript'
    };

    const pasteId = await createPastePage.createPasteAndWaitForRedirect(pasteData);
    
    await viewPastePage.expectToBeOnPastePage(pasteId);
    await viewPastePage.expectPasteTitle(pasteData.title);
  });

  test('should handle special characters in content', async () => {
    const specialContent = `
      const emoji = "🚀 Hello World! 🌟";
      const unicode = "Ñoño café résumé";
      const symbols = "!@#$%^&*()_+-=[]{}|;':\",./<>?";
    `;

    const pasteData = {
      title: 'Special Characters Test',
      content: specialContent,
      language: 'javascript'
    };

    const pasteId = await createPastePage.createPasteAndWaitForRedirect(pasteData);
    
    await viewPastePage.expectToBeOnPastePage(pasteId);
    await viewPastePage.expectPasteContent('🚀 Hello World! 🌟');
    await viewPastePage.expectPasteContent('Ñoño café résumé');
  });

  test('should handle network errors gracefully', async ({ page }) => {
    // Simulate network failure
    await page.route('**/api/v1/pastes', route => route.abort());

    const pasteData = {
      title: 'Network Error Test',
      content: 'This should fail'
    };

    await createPastePage.createPaste(pasteData);
    await createPastePage.expectErrorMessage(/network|connection|failed/i);
  });

  test('should handle server errors gracefully', async ({ page }) => {
    // Simulate server error
    await page.route('**/api/v1/pastes', route => 
      route.fulfill({ status: 500, body: JSON.stringify({ error: 'Internal server error' }) })
    );

    const pasteData = {
      title: 'Server Error Test',
      content: 'This should fail'
    };

    await createPastePage.createPaste(pasteData);
    await createPastePage.expectErrorMessage(/server error|internal error/i);
  });

  test('should clear form when requested', async () => {
    await createPastePage.fillTitle('Test Title');
    await createPastePage.fillContent('Test Content');
    await createPastePage.fillCustomUrl('test-url');

    await createPastePage.clearForm();

    const formData = await createPastePage.getFormData();
    expect(formData.title).toBe('');
    expect(formData.content).toBe('');
    expect(formData.customUrl).toBe('');
  });

  test('should validate form completion', async () => {
    // Empty form should be invalid
    expect(await createPastePage.isFormValid()).toBe(false);

    // Title only should be invalid
    await createPastePage.fillTitle('Test Title');
    expect(await createPastePage.isFormValid()).toBe(false);

    // Complete form should be valid
    await createPastePage.fillContent('Test Content');
    expect(await createPastePage.isFormValid()).toBe(true);
  });

  test('should handle encryption settings', async () => {
    const pasteData = {
      title: 'Encrypted Paste',
      content: 'This is secret content',
      encrypted: true,
      password: 'secret123'
    };

    await createPastePage.fillTitle(pasteData.title);
    await createPastePage.fillContent(pasteData.content);
    await createPastePage.setEncryption(pasteData.encrypted, pasteData.password);

    await createPastePage.expectEncryptionEnabled();
    
    const pasteId = await createPastePage.createPasteAndWaitForRedirect(pasteData);
    await viewPastePage.expectToBeOnPastePage(pasteId);
  });

  test('should navigate to home after successful creation', async () => {
    const pasteData = {
      title: 'Navigation Test',
      content: 'Test content'
    };

    await createPastePage.createPasteAndWaitForRedirect(pasteData);
    
    // Navigate back to home
    await homePage.visit();
    await homePage.expectToBeOnHomePage();
    
    // The new paste should appear in recent pastes
    await homePage.expectPasteInList(pasteData.title);
  });
});
