import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tree, List, Search, Copy, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface JsonViewerProps {
  content: string;
  title?: string;
}

interface JsonNode {
  key: string;
  value: any;
  type: string;
  path: string;
  expanded?: boolean;
}

export default function JsonViewer({ content, title = "JSON Viewer" }: JsonViewerProps) {
  const [viewMode, setViewMode] = useState<'tree' | 'formatted'>('tree');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  const { parsedJson, error, formattedJson } = useMemo(() => {
    try {
      const parsed = JSON.parse(content);
      const formatted = JSON.stringify(parsed, null, 2);
      return { parsedJson: parsed, error: null, formattedJson: formatted };
    } catch (err) {
      return { 
        parsedJson: null, 
        error: err instanceof Error ? err.message : 'Invalid JSON',
        formattedJson: content 
      };
    }
  }, [content]);

  const jsonTree = useMemo(() => {
    if (!parsedJson) return [];

    const buildTree = (obj: any, path = '', key = 'root'): JsonNode[] => {
      const nodes: JsonNode[] = [];
      
      if (obj === null) {
        nodes.push({ key, value: null, type: 'null', path });
      } else if (Array.isArray(obj)) {
        nodes.push({ 
          key, 
          value: obj, 
          type: 'array', 
          path,
          expanded: expandedNodes.has(path)
        });
        
        if (expandedNodes.has(path)) {
          obj.forEach((item, index) => {
            const itemPath = `${path}[${index}]`;
            nodes.push(...buildTree(item, itemPath, `[${index}]`));
          });
        }
      } else if (typeof obj === 'object') {
        nodes.push({ 
          key, 
          value: obj, 
          type: 'object', 
          path,
          expanded: expandedNodes.has(path)
        });
        
        if (expandedNodes.has(path)) {
          Object.entries(obj).forEach(([objKey, objValue]) => {
            const itemPath = path ? `${path}.${objKey}` : objKey;
            nodes.push(...buildTree(objValue, itemPath, objKey));
          });
        }
      } else {
        nodes.push({ 
          key, 
          value: obj, 
          type: typeof obj, 
          path 
        });
      }
      
      return nodes;
    };

    return buildTree(parsedJson);
  }, [parsedJson, expandedNodes]);

  const filteredTree = useMemo(() => {
    if (!searchTerm) return jsonTree;
    
    return jsonTree.filter(node => 
      node.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (typeof node.value === 'string' && node.value.toLowerCase().includes(searchTerm.toLowerCase())) ||
      node.path.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [jsonTree, searchTerm]);

  const toggleNode = (path: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedNodes(newExpanded);
  };

  const expandAll = () => {
    const allPaths = new Set<string>();
    const collectPaths = (obj: any, path = '') => {
      if (Array.isArray(obj)) {
        allPaths.add(path);
        obj.forEach((item, index) => {
          collectPaths(item, `${path}[${index}]`);
        });
      } else if (obj && typeof obj === 'object') {
        allPaths.add(path);
        Object.entries(obj).forEach(([key, value]) => {
          const itemPath = path ? `${path}.${key}` : key;
          collectPaths(value, itemPath);
        });
      }
    };
    
    if (parsedJson) {
      collectPaths(parsedJson);
      setExpandedNodes(allPaths);
    }
  };

  const collapseAll = () => {
    setExpandedNodes(new Set());
  };

  const copyFormatted = async () => {
    try {
      await navigator.clipboard.writeText(formattedJson);
      toast({
        title: "Copied",
        description: "Formatted JSON copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const renderValue = (value: any, type: string) => {
    switch (type) {
      case 'string':
        return <span className="text-green-600">"{value}"</span>;
      case 'number':
        return <span className="text-blue-600">{value}</span>;
      case 'boolean':
        return <span className="text-purple-600">{value.toString()}</span>;
      case 'null':
        return <span className="text-gray-500">null</span>;
      case 'array':
        return <span className="text-gray-600">[{value.length} items]</span>;
      case 'object':
        return <span className="text-gray-600">{{{Object.keys(value).length} keys}}</span>;
      default:
        return <span>{String(value)}</span>;
    }
  };

  const renderTreeNode = (node: JsonNode, depth = 0) => {
    const isExpandable = node.type === 'object' || node.type === 'array';
    const isExpanded = expandedNodes.has(node.path);
    
    return (
      <div key={node.path} className="font-mono text-sm">
        <div 
          className="flex items-center py-1 hover:bg-muted/50 rounded px-2"
          style={{ paddingLeft: `${depth * 20 + 8}px` }}
        >
          {isExpandable && (
            <button
              onClick={() => toggleNode(node.path)}
              className="mr-2 w-4 h-4 flex items-center justify-center text-gray-500 hover:text-gray-700"
            >
              {isExpanded ? '▼' : '▶'}
            </button>
          )}
          {!isExpandable && <div className="w-6" />}
          
          <span className="font-medium text-blue-800 mr-2">{node.key}:</span>
          {renderValue(node.value, node.type)}
        </div>
      </div>
    );
  };

  if (!content.trim()) {
    return null;
  }

  return (
    <Card className="mt-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            {title}
            {error && <AlertCircle className="h-4 w-4 text-red-500" />}
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'tree' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('tree')}
              disabled={!!error}
            >
              <Tree className="h-4 w-4 mr-1" />
              Tree
            </Button>
            <Button
              variant={viewMode === 'formatted' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('formatted')}
            >
              <List className="h-4 w-4 mr-1" />
              Formatted
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={copyFormatted}
            >
              <Copy className="h-4 w-4 mr-1" />
              Copy
            </Button>
          </div>
        </div>
        
        {!error && viewMode === 'tree' && (
          <div className="flex items-center space-x-2 mt-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search JSON..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Button variant="outline" size="sm" onClick={expandAll}>
              Expand All
            </Button>
            <Button variant="outline" size="sm" onClick={collapseAll}>
              Collapse All
            </Button>
          </div>
        )}
      </CardHeader>
      
      <CardContent>
        {error ? (
          <div className="text-red-600 bg-red-50 p-4 rounded-md">
            <div className="font-medium">Invalid JSON</div>
            <div className="text-sm mt-1">{error}</div>
            <pre className="mt-2 text-xs bg-white p-2 rounded border overflow-auto max-h-32">
              {content}
            </pre>
          </div>
        ) : viewMode === 'tree' ? (
          <div className="border rounded-md max-h-96 overflow-auto">
            {filteredTree.map(node => renderTreeNode(node))}
          </div>
        ) : (
          <pre className="bg-muted p-4 rounded-md overflow-auto max-h-96 text-sm font-mono">
            {formattedJson}
          </pre>
        )}
      </CardContent>
    </Card>
  );
}
