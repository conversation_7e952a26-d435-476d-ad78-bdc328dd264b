import { test, expect } from '@playwright/test';
import { RegisterPage, LoginPage, HomePage } from '../../pages';
import { TestHelpers } from '../../utils/test-helpers';

test.describe('User Registration', () => {
  let registerPage: RegisterPage;
  let loginPage: LoginPage;
  let homePage: HomePage;
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    registerPage = new RegisterPage(page);
    loginPage = new LoginPage(page);
    homePage = new HomePage(page);
    helpers = new TestHelpers();
    
    await registerPage.visit();
  });

  test.afterEach(async () => {
    await helpers.cleanupTestData();
  });

  test('should display registration form', async () => {
    await registerPage.expectToBeOnRegisterPage();
    await registerPage.expectRegisterForm();
    await registerPage.expectLoginLinkVisible();
  });

  test('should register a new user successfully', async () => {
    const timestamp = Date.now();
    const userData = {
      username: `testuser_${timestamp}`,
      email: `test_${timestamp}@example.com`,
      password: 'TestPassword123!'
    };

    await registerPage.registerAndWaitForRedirect(userData);
    
    // Should be redirected to login page with success message
    await loginPage.expectToBeOnLoginPage();
    await registerPage.expectSuccessMessage(/registered|created|welcome/i);
  });

  test('should show validation errors for empty form', async () => {
    await registerPage.submitForm();
    await registerPage.expectValidationErrors();
  });

  test('should show error for invalid email format', async () => {
    const userData = {
      username: 'testuser',
      email: 'invalid-email',
      password: 'TestPassword123!'
    };

    await registerPage.register(userData);
    await registerPage.expectInvalidEmailError();
  });

  test('should show error for weak password', async () => {
    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: '123'
    };

    await registerPage.register(userData);
    await registerPage.expectWeakPasswordError();
  });

  test('should show error for password mismatch', async () => {
    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      confirmPassword: 'DifferentPassword123!'
    };

    await registerPage.register(userData);
    await registerPage.expectPasswordMismatchError();
  });

  test('should show error for duplicate username', async () => {
    const timestamp = Date.now();
    const userData = {
      username: `testuser_${timestamp}`,
      email: `test_${timestamp}@example.com`,
      password: 'TestPassword123!'
    };

    // Create user via API first
    await helpers.api.createTestUser(userData);

    // Try to register with same username
    await registerPage.register({
      ...userData,
      email: `different_${timestamp}@example.com`
    });

    await registerPage.expectUsernameAlreadyExistsError();
  });

  test('should show error for duplicate email', async () => {
    const timestamp = Date.now();
    const userData = {
      username: `testuser_${timestamp}`,
      email: `test_${timestamp}@example.com`,
      password: 'TestPassword123!'
    };

    // Create user via API first
    await helpers.api.createTestUser(userData);

    // Try to register with same email
    await registerPage.register({
      ...userData,
      username: `different_${timestamp}`
    });

    await registerPage.expectEmailAlreadyExistsError();
  });

  test('should navigate to login page when clicking login link', async () => {
    await registerPage.clickLoginLink();
    await loginPage.expectToBeOnLoginPage();
  });

  test('should validate password strength indicator', async () => {
    await registerPage.fillPassword('weak');
    await registerPage.expectPasswordStrength('weak');

    await registerPage.fillPassword('MediumPass123');
    await registerPage.expectPasswordStrength('medium');

    await registerPage.fillPassword('VeryStrongPassword123!@#');
    await registerPage.expectPasswordStrength('strong');
  });

  test('should handle terms and conditions checkbox', async () => {
    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      acceptTerms: false
    };

    await registerPage.register(userData);
    
    // Should show error about accepting terms
    await registerPage.expectValidationErrors();
  });

  test('should clear form when requested', async () => {
    await registerPage.fillUsername('testuser');
    await registerPage.fillEmail('<EMAIL>');
    await registerPage.fillPassword('password');

    await registerPage.clearForm();

    const formData = await registerPage.getFormData();
    expect(formData.username).toBe('');
    expect(formData.email).toBe('');
    expect(formData.password).toBe('');
  });

  test('should validate form completion', async () => {
    // Empty form should be invalid
    expect(await registerPage.isFormValid()).toBe(false);

    // Partially filled form should be invalid
    await registerPage.fillUsername('testuser');
    expect(await registerPage.isFormValid()).toBe(false);

    // Complete form should be valid
    await registerPage.fillEmail('<EMAIL>');
    await registerPage.fillPassword('TestPassword123!');
    await registerPage.fillConfirmPassword('TestPassword123!');
    expect(await registerPage.isFormValid()).toBe(true);
  });

  test('should handle network errors gracefully', async ({ page }) => {
    // Simulate network failure
    await page.route('**/api/v1/users/register', route => route.abort());

    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'TestPassword123!'
    };

    await registerPage.register(userData);
    await registerPage.expectErrorMessage(/network|connection|failed/i);
  });

  test('should handle server errors gracefully', async ({ page }) => {
    // Simulate server error
    await page.route('**/api/v1/users/register', route => 
      route.fulfill({ status: 500, body: JSON.stringify({ error: 'Internal server error' }) })
    );

    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'TestPassword123!'
    };

    await registerPage.register(userData);
    await registerPage.expectErrorMessage(/server error|internal error/i);
  });
});
