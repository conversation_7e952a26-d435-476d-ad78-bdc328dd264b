import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { WebSocketClient } from '../websocketClient'

// Mock WebSocket
class MockWebSocket {
  static CONNECTING = 0
  static OPEN = 1
  static CLOSING = 2
  static CLOSED = 3

  readyState = MockWebSocket.CONNECTING
  onopen: ((event: Event) => void) | null = null
  onclose: ((event: CloseEvent) => void) | null = null
  onmessage: ((event: MessageEvent) => void) | null = null
  onerror: ((event: Event) => void) | null = null

  constructor(public url: string) {
    // Simulate connection opening after a short delay
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN
      if (this.onopen) {
        this.onopen(new Event('open'))
      }
    }, 10)
  }

  send(_data: string) {
    // Mock send functionality
  }

  close() {
    this.readyState = MockWebSocket.CLOSED
    if (this.onclose) {
      this.onclose(new CloseEvent('close'))
    }
  }
}

// Mock global WebSocket
// @ts-ignore
globalThis.WebSocket = MockWebSocket as any

describe('WebSocketClient', () => {
  let client: WebSocketClient
  
  beforeEach(() => {
    client = new WebSocketClient({
      url: 'ws://localhost:8080/api/v1/ws/connect',
      reconnectInterval: 100,
      maxReconnectAttempts: 2
    })
  })

  afterEach(() => {
    client.disconnect()
  })

  describe('connection management', () => {
    it('should connect to WebSocket server', async () => {
      const connectPromise = client.connect()
      
      // Wait for connection to establish
      await connectPromise
      
      expect(client.isConnected()).toBe(true)
      expect(client.getConnectionState()).toBe('OPEN')
    })

    it('should disconnect from WebSocket server', async () => {
      await client.connect()
      expect(client.isConnected()).toBe(true)
      
      client.disconnect()
      expect(client.isConnected()).toBe(false)
    })

    it('should handle connection state changes', async () => {
      expect(client.getConnectionState()).toBe('CLOSED')
      
      const connectPromise = client.connect()
      expect(client.getConnectionState()).toBe('CONNECTING')
      
      await connectPromise
      expect(client.getConnectionState()).toBe('OPEN')
    })
  })

  describe('room management', () => {
    beforeEach(async () => {
      await client.connect()
    })

    it('should join a room', () => {
      const sendSpy = vi.spyOn(client as any, 'send')
      
      client.joinRoom('test-paste-123')
      
      expect(sendSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'join',
          paste_id: 'test-paste-123'
        })
      )
    })

    it('should leave a room', () => {
      const sendSpy = vi.spyOn(client as any, 'send')
      
      client.leaveRoom('test-paste-123')
      
      expect(sendSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'leave',
          paste_id: 'test-paste-123'
        })
      )
    })

    it('should broadcast messages', () => {
      const sendSpy = vi.spyOn(client as any, 'send')
      const testData = { message: 'Hello, world!' }
      
      client.broadcast(testData)
      
      expect(sendSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'broadcast',
          data: testData
        })
      )
    })
  })

  describe('event handlers', () => {
    it('should call onConnect handler when connected', async () => {
      const onConnect = vi.fn()
      client.setHandlers({ onConnect })
      
      await client.connect()
      
      expect(onConnect).toHaveBeenCalled()
    })

    it('should call onDisconnect handler when disconnected', async () => {
      const onDisconnect = vi.fn()
      client.setHandlers({ onDisconnect })
      
      await client.connect()
      client.disconnect()
      
      expect(onDisconnect).toHaveBeenCalled()
    })
  })

  describe('token management', () => {
    it('should include token in WebSocket URL', () => {
      const clientWithToken = new WebSocketClient({
        url: 'ws://localhost:8080/api/v1/ws/connect',
        token: 'test-token-123'
      })
      
      // The token should be included in the URL when connecting
      // This is tested indirectly through the connection process
      expect(clientWithToken).toBeDefined()
    })

    it('should update token and reconnect', async () => {
      await client.connect()
      const disconnectSpy = vi.spyOn(client, 'disconnect')
      const connectSpy = vi.spyOn(client, 'connect')
      
      client.updateToken('new-token')
      
      expect(disconnectSpy).toHaveBeenCalled()
      // Connect is called asynchronously, so we need to wait
      await new Promise(resolve => setTimeout(resolve, 150))
      expect(connectSpy).toHaveBeenCalled()
    })
  })
})