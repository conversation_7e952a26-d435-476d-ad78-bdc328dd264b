<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Disconnected</div>
    <div id="messages"></div>
    <button onclick="connect()">Connect</button>
    <button onclick="disconnect()">Disconnect</button>
    <button onclick="joinRoom()">Join Room (test-paste)</button>
    <button onclick="leaveRoom()">Leave Room (test-paste)</button>

    <script>
        let ws = null;
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');

        function addMessage(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            messagesDiv.appendChild(div);
        }

        function connect() {
            if (ws) {
                ws.close();
            }

            ws = new WebSocket('ws://localhost:8080/api/v1/ws/connect');
            
            ws.onopen = function() {
                statusDiv.textContent = 'Connected';
                statusDiv.style.color = 'green';
                addMessage('Connected to WebSocket server');
            };

            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                addMessage('Received: ' + JSON.stringify(message, null, 2));
            };

            ws.onclose = function() {
                statusDiv.textContent = 'Disconnected';
                statusDiv.style.color = 'red';
                addMessage('Disconnected from WebSocket server');
            };

            ws.onerror = function(error) {
                addMessage('Error: ' + error);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function joinRoom() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'join',
                    paste_id: 'test-paste',
                    timestamp: new Date().toISOString()
                };
                ws.send(JSON.stringify(message));
                addMessage('Sent join message for test-paste');
            } else {
                addMessage('WebSocket not connected');
            }
        }

        function leaveRoom() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'leave',
                    paste_id: 'test-paste',
                    timestamp: new Date().toISOString()
                };
                ws.send(JSON.stringify(message));
                addMessage('Sent leave message for test-paste');
            } else {
                addMessage('WebSocket not connected');
            }
        }
    </script>
</body>
</html>