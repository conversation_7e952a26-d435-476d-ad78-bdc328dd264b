package services

import (
	"errors"
	"testing"

	"enhanced-pastebin/internal/models"
	"enhanced-pastebin/internal/utils"

	"golang.org/x/crypto/bcrypt"
)

// Mock repository for testing
type mockUserRepository struct {
	users map[string]*models.User
}

func newMockUserRepository() *mockUserRepository {
	return &mockUserRepository{
		users: make(map[string]*models.User),
	}
}

func (m *mockUserRepository) Create(user *models.User) error {
	// Check for existing email
	for _, u := range m.users {
		if u.Email == user.Email {
			return errors.New("user with this email already exists")
		}
		if u.Username == user.Username {
			return errors.New("user with this username already exists")
		}
	}
	m.users[user.ID] = user
	return nil
}

func (m *mockUserRepository) GetByID(id string) (*models.User, error) {
	if user, exists := m.users[id]; exists {
		return user, nil
	}
	return nil, nil
}

func (m *mockUserRepository) GetByEmail(email string) (*models.User, error) {
	for _, user := range m.users {
		if user.Email == email {
			return user, nil
		}
	}
	return nil, nil
}

func (m *mockUserRepository) GetByUsername(username string) (*models.User, error) {
	for _, user := range m.users {
		if user.Username == username {
			return user, nil
		}
	}
	return nil, nil
}

func (m *mockUserRepository) Update(user *models.User) error {
	if _, exists := m.users[user.ID]; exists {
		m.users[user.ID] = user
		return nil
	}
	return errors.New("user not found")
}

func (m *mockUserRepository) Delete(id string) error {
	if _, exists := m.users[id]; exists {
		delete(m.users, id)
		return nil
	}
	return errors.New("user not found")
}

func TestUserService_Register(t *testing.T) {
	repo := newMockUserRepository()
	service := NewUserService(repo)

	req := &models.CreateUserRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "password123",
	}

	user, err := service.Register(req)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if user.Username != req.Username {
		t.Errorf("Expected username %s, got %s", req.Username, user.Username)
	}

	if user.Email != req.Email {
		t.Errorf("Expected email %s, got %s", req.Email, user.Email)
	}

	// Verify password was hashed
	if user.PasswordHash == req.Password {
		t.Error("Password should be hashed, not stored in plain text")
	}

	// Verify password hash is correct
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password))
	if err != nil {
		t.Error("Password hash verification failed")
	}
}

func TestUserService_Register_DuplicateEmail(t *testing.T) {
	repo := newMockUserRepository()
	service := NewUserService(repo)

	req1 := &models.CreateUserRequest{
		Username: "testuser1",
		Email:    "<EMAIL>",
		Password: "password123",
	}

	req2 := &models.CreateUserRequest{
		Username: "testuser2",
		Email:    "<EMAIL>", // Same email
		Password: "password456",
	}

	// First registration should succeed
	_, err := service.Register(req1)
	if err != nil {
		t.Fatalf("First registration should succeed, got %v", err)
	}

	// Second registration should fail
	_, err = service.Register(req2)
	if err == nil {
		t.Error("Expected error for duplicate email, got nil")
	}
}

func TestUserService_Register_DuplicateUsername(t *testing.T) {
	repo := newMockUserRepository()
	service := NewUserService(repo)

	req1 := &models.CreateUserRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "password123",
	}

	req2 := &models.CreateUserRequest{
		Username: "testuser", // Same username
		Email:    "<EMAIL>",
		Password: "password456",
	}

	// First registration should succeed
	_, err := service.Register(req1)
	if err != nil {
		t.Fatalf("First registration should succeed, got %v", err)
	}

	// Second registration should fail
	_, err = service.Register(req2)
	if err == nil {
		t.Error("Expected error for duplicate username, got nil")
	}
}

func TestUserService_Login(t *testing.T) {
	repo := newMockUserRepository()
	service := NewUserService(repo)

	// First register a user
	registerReq := &models.CreateUserRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "password123",
	}

	_, err := service.Register(registerReq)
	if err != nil {
		t.Fatalf("Registration failed: %v", err)
	}

	// Now test login
	loginReq := &models.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}

	response, err := service.Login(loginReq)
	if err != nil {
		t.Fatalf("Login failed: %v", err)
	}

	if response.User.Email != loginReq.Email {
		t.Errorf("Expected email %s, got %s", loginReq.Email, response.User.Email)
	}

	if response.Token == "" {
		t.Error("Expected JWT token, got empty string")
	}

	// Verify JWT token
	claims, err := utils.ValidateJWT(response.Token)
	if err != nil {
		t.Errorf("JWT validation failed: %v", err)
	}

	if claims.UserID != response.User.ID {
		t.Errorf("Expected user ID %s in token, got %s", response.User.ID, claims.UserID)
	}
}

func TestUserService_Login_InvalidCredentials(t *testing.T) {
	repo := newMockUserRepository()
	service := NewUserService(repo)

	// First register a user
	registerReq := &models.CreateUserRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "password123",
	}

	_, err := service.Register(registerReq)
	if err != nil {
		t.Fatalf("Registration failed: %v", err)
	}

	// Test login with wrong password
	loginReq := &models.LoginRequest{
		Email:    "<EMAIL>",
		Password: "wrongpassword",
	}

	_, err = service.Login(loginReq)
	if err == nil {
		t.Error("Expected error for invalid password, got nil")
	}

	// Test login with non-existent email
	loginReq = &models.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}

	_, err = service.Login(loginReq)
	if err == nil {
		t.Error("Expected error for non-existent email, got nil")
	}
}