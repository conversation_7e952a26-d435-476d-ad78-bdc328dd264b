package models

import "time"

// AccessLog represents an access log entry
type AccessLog struct {
	ID        string    `json:"id" db:"id"`
	Paste<PERSON>   string    `json:"paste_id" db:"paste_id"`
	UserID    *string   `json:"user_id,omitempty" db:"user_id"`
	Username  *string   `json:"username,omitempty" db:"username"`
	IPAddress string    `json:"ip_address" db:"ip_address"`
	UserAgent string    `json:"user_agent" db:"user_agent"`
	Action    string    `json:"action" db:"action"` // view, copy, download, edit, delete, security_event
	Details   string    `json:"details,omitempty" db:"details"`
	Timestamp time.Time `json:"timestamp" db:"timestamp"`
}

// AccessLogRequest represents a request to log an access event
type AccessLogRequest struct {
	PasteID   string  `json:"paste_id"`
	UserID    *string `json:"user_id,omitempty"`
	IPAddress string  `json:"ip_address"`
	UserAgent string  `json:"user_agent"`
	Action    string  `json:"action"`
	Details   string  `json:"details,omitempty"`
}

// SecurityEventRequest represents a request to log a security event
type SecurityEventRequest struct {
	PasteID   *string `json:"paste_id,omitempty"`
	UserID    *string `json:"user_id,omitempty"`
	IPAddress string  `json:"ip_address"`
	UserAgent string  `json:"user_agent"`
	Details   string  `json:"details"`
}

// AccessLogResponse represents the response for access log queries
type AccessLogResponse struct {
	Logs       []*AccessLog `json:"logs"`
	TotalCount int          `json:"total_count"`
	Limit      int          `json:"limit"`
	Offset     int          `json:"offset"`
}

// WatermarkInfo represents watermark information for a paste
type WatermarkInfo struct {
	ID          string    `json:"id" db:"id"`
	PasteID     string    `json:"paste_id" db:"paste_id"`
	UserID      string    `json:"user_id" db:"user_id"`
	Username    string    `json:"username" db:"username"`
	WatermarkID string    `json:"watermark_id" db:"watermark_id"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
}

// WatermarkRequest represents a request to create a watermark
type WatermarkRequest struct {
	PasteID string `json:"paste_id"`
	UserID  string `json:"user_id"`
}

// AuditTrailEntry represents an entry in the audit trail
type AuditTrailEntry struct {
	ID          string                 `json:"id"`
	EntityType  string                 `json:"entity_type"` // paste, user, chat_message
	EntityID    string                 `json:"entity_id"`
	Action      string                 `json:"action"` // create, update, delete, view, copy
	UserID      *string                `json:"user_id,omitempty"`
	Username    *string                `json:"username,omitempty"`
	IPAddress   string                 `json:"ip_address"`
	UserAgent   string                 `json:"user_agent"`
	Changes     map[string]interface{} `json:"changes,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
	Severity    string                 `json:"severity"` // info, warning, error, critical
}
