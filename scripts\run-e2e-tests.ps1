#!/usr/bin/env pwsh

# Enhanced Pastebin E2E Test Runner (PowerShell)
# This script sets up the environment and runs Playwright tests

param(
    [switch]$Headed,
    [switch]$UI,
    [switch]$Debug,
    [string]$Test = "",
    [switch]$SkipSetup,
    [switch]$Help
)

# Configuration
$BackendPort = 8080
$FrontendPort = 3000
$DatabaseUrl = "postgres://postgres:password@localhost:5432/enhanced_pastebin?sslmode=disable"
$JwtSecret = "test-jwt-secret-key-for-e2e-tests"

# Global variables for cleanup
$BackendProcess = $null
$FrontendProcess = $null
$StartedDocker = $false

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Function to wait for service to be ready
function Wait-ForService {
    param(
        [string]$Url,
        [string]$ServiceName,
        [int]$MaxAttempts = 30
    )
    
    Write-Status "Waiting for $ServiceName to be ready..."
    
    for ($attempt = 1; $attempt -le $MaxAttempts; $attempt++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -Method GET -TimeoutSec 5 -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                Write-Success "$ServiceName is ready!"
                return $true
            }
        }
        catch {
            # Service not ready yet
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 2
    }
    
    Write-Host ""
    Write-Error "$ServiceName failed to start within timeout"
    return $false
}

# Function to cleanup processes
function Cleanup {
    Write-Status "Cleaning up..."
    
    if ($BackendProcess -and !$BackendProcess.HasExited) {
        Write-Status "Stopping backend server (PID: $($BackendProcess.Id))"
        try {
            $BackendProcess.Kill()
            $BackendProcess.WaitForExit(5000)
        }
        catch {
            Write-Warning "Failed to stop backend process gracefully"
        }
    }
    
    if ($FrontendProcess -and !$FrontendProcess.HasExited) {
        Write-Status "Stopping frontend server (PID: $($FrontendProcess.Id))"
        try {
            $FrontendProcess.Kill()
            $FrontendProcess.WaitForExit(5000)
        }
        catch {
            Write-Warning "Failed to stop frontend process gracefully"
        }
    }
    
    # Stop Docker services if we started them
    if ($StartedDocker) {
        Write-Status "Stopping Docker services"
        try {
            docker-compose down 2>$null
        }
        catch {
            Write-Warning "Failed to stop Docker services"
        }
    }
}

# Set up cleanup
Register-EngineEvent PowerShell.Exiting -Action { Cleanup }
$ErrorActionPreference = "Stop"

# Show help
if ($Help) {
    Write-Host "Usage: .\run-e2e-tests.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Headed      Run tests in headed mode (show browser)"
    Write-Host "  -UI          Run tests in UI mode"
    Write-Host "  -Debug       Run tests in debug mode"
    Write-Host "  -Test FILE   Run specific test file"
    Write-Host "  -SkipSetup   Skip environment setup"
    Write-Host "  -Help        Show this help message"
    exit 0
}

try {
    Write-Status "Starting Enhanced Pastebin E2E Tests"

    # Check prerequisites
    $commands = @("node", "go", "docker", "docker-compose")
    foreach ($cmd in $commands) {
        if (!(Get-Command $cmd -ErrorAction SilentlyContinue)) {
            Write-Error "$cmd is not installed or not in PATH"
            exit 1
        }
    }

    # Setup environment if not skipped
    if (!$SkipSetup) {
        Write-Status "Setting up environment..."
        
        # Check if database services are running
        if (!(Test-Port -Port 5432)) {
            Write-Status "Starting database services..."
            docker-compose up -d postgres redis
            $StartedDocker = $true
            
            # Wait for PostgreSQL to be ready
            $postgresReady = $false
            for ($i = 1; $i -le 30; $i++) {
                try {
                    docker exec fullstack-pastify-postgres-1 pg_isready -U postgres 2>$null
                    if ($LASTEXITCODE -eq 0) {
                        $postgresReady = $true
                        break
                    }
                }
                catch {
                    # Continue waiting
                }
                Write-Host "." -NoNewline
                Start-Sleep -Seconds 2
            }
            
            if (!$postgresReady) {
                Write-Error "PostgreSQL failed to start"
                exit 1
            }
            Write-Success "PostgreSQL is ready!"
        }
        else {
            Write-Status "Database services already running"
        }
        
        # Run database migrations
        Write-Status "Running database migrations..."
        $env:DATABASE_URL = $DatabaseUrl
        go run cmd/migrate/main.go up
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Database migrations failed"
            exit 1
        }
        
        # Check if backend is running
        if (!(Test-Port -Port $BackendPort)) {
            Write-Status "Starting backend server..."
            $env:DATABASE_URL = $DatabaseUrl
            $env:JWT_SECRET = $JwtSecret
            $env:PORT = $BackendPort
            $env:GIN_MODE = "release"
            
            $BackendProcess = Start-Process -FilePath "go" -ArgumentList "run", "cmd/server/main.go" -PassThru -NoNewWindow
            
            if (!(Wait-ForService -Url "http://localhost:$BackendPort/api/v1/health" -ServiceName "Backend server")) {
                Write-Error "Backend server failed to start"
                exit 1
            }
        }
        else {
            Write-Status "Backend server already running"
        }
        
        # Install frontend dependencies if needed
        if (!(Test-Path "frontend/node_modules")) {
            Write-Status "Installing frontend dependencies..."
            Push-Location frontend
            npm install
            if ($LASTEXITCODE -ne 0) {
                Pop-Location
                Write-Error "Failed to install frontend dependencies"
                exit 1
            }
            Pop-Location
        }
        
        # Install Playwright browsers if needed
        if (!(Test-Path "frontend/node_modules/@playwright")) {
            Write-Status "Installing Playwright browsers..."
            Push-Location frontend
            npx playwright install
            if ($LASTEXITCODE -ne 0) {
                Pop-Location
                Write-Error "Failed to install Playwright browsers"
                exit 1
            }
            Pop-Location
        }
    }
    else {
        Write-Status "Skipping environment setup"
    }

    # Change to frontend directory
    Push-Location frontend

    # Build test command
    $TestCmd = @("npx", "playwright", "test")

    if ($UI) {
        $TestCmd += "--ui"
    }
    elseif ($Debug) {
        $TestCmd += "--debug"
    }
    elseif ($Headed) {
        $TestCmd += "--headed"
    }

    if ($Test) {
        $TestCmd += $Test
    }

    # Run tests
    Write-Status "Running Playwright tests..."
    Write-Status "Command: $($TestCmd -join ' ')"

    & $TestCmd[0] $TestCmd[1..($TestCmd.Length-1)]
    $TestExitCode = $LASTEXITCODE

    Pop-Location

    if ($TestExitCode -eq 0) {
        Write-Success "All tests passed!"
    }
    else {
        Write-Error "Some tests failed!"
        
        # Show report if available
        if (Test-Path "frontend/playwright-report/index.html") {
            Write-Status "Test report available at: frontend/playwright-report/index.html"
            Write-Status "Run 'npm run test:e2e:report' to view the report"
        }
    }

    exit $TestExitCode
}
catch {
    Write-Error "An error occurred: $($_.Exception.Message)"
    exit 1
}
finally {
    Cleanup
}
