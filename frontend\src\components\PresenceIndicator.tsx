import React from 'react';
import { Users, Wifi, WifiOff, Edit3 } from 'lucide-react';
import { ActiveUser, CollaboratorState } from '../types/websocket';

interface PresenceIndicatorProps {
  isConnected: boolean;
  connectionState: string;
  activeUsers?: ActiveUser[];
  userCount?: number;
  collaborators?: CollaboratorState[];
  currentUserId?: string;
  reconnectAttempts?: number;
  isCollaborativeMode?: boolean;
}

export const PresenceIndicator: React.FC<PresenceIndicatorProps> = ({
  isConnected,
  connectionState,
  activeUsers = [],
  userCount = 0,
  collaborators = [],
  currentUserId,
  reconnectAttempts = 0,
  isCollaborativeMode = false
}) => {
  const getConnectionStatusColor = () => {
    switch (connectionState) {
      case 'OPEN':
        return 'text-green-500';
      case 'CONNECTING':
        return 'text-yellow-500';
      case 'ERROR':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionState) {
      case 'OPEN':
        return 'Connected';
      case 'CONNECTING':
        return reconnectAttempts > 0 ? `Reconnecting... (${reconnectAttempts})` : 'Connecting...';
      case 'ERROR':
        return 'Connection Error';
      case 'CLOSING':
        return 'Disconnecting...';
      default:
        return 'Disconnected';
    }
  };

  return (
    <div className="flex items-center space-x-4 p-3 bg-gray-50 border-b">
      {/* Connection Status */}
      <div className="flex items-center space-x-2">
        {isConnected ? (
          <Wifi className={`h-4 w-4 ${getConnectionStatusColor()}`} />
        ) : (
          <WifiOff className={`h-4 w-4 ${getConnectionStatusColor()}`} />
        )}
        <span className={`text-sm font-medium ${getConnectionStatusColor()}`}>
          {getConnectionStatusText()}
        </span>
      </div>

      {/* Collaborative Mode Indicator */}
      {isConnected && isCollaborativeMode && (
        <div className="flex items-center space-x-2">
          <Edit3 className="h-4 w-4 text-purple-500" />
          <span className="text-sm text-purple-600 font-medium">
            Collaborative Editing
          </span>
        </div>
      )}

      {/* Collaborators (for collaborative editing) */}
      {isConnected && isCollaborativeMode && collaborators.length > 0 && (
        <div className="flex items-center space-x-2">
          <div className="flex -space-x-2">
            {collaborators
              .filter(c => c.user_id !== currentUserId)
              .slice(0, 4)
              .map((collaborator) => (
                <div
                  key={collaborator.user_id}
                  className="inline-flex items-center justify-center h-8 w-8 rounded-full text-white text-xs font-medium border-2 border-white shadow-sm"
                  title={`${collaborator.username} is editing`}
                  style={{ backgroundColor: collaborator.color }}
                >
                  {collaborator.username.charAt(0).toUpperCase()}
                </div>
              ))}
            {collaborators.filter(c => c.user_id !== currentUserId).length > 4 && (
              <div className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-gray-500 text-white text-xs font-medium border-2 border-white">
                +{collaborators.filter(c => c.user_id !== currentUserId).length - 4}
              </div>
            )}
          </div>
          <span className="text-sm text-gray-600">
            {collaborators.filter(c => c.user_id !== currentUserId).length === 0
              ? 'Only you are editing'
              : `${collaborators.filter(c => c.user_id !== currentUserId).length} others editing`
            }
          </span>
        </div>
      )}

      {/* Regular Active Users (for non-collaborative mode) */}
      {isConnected && !isCollaborativeMode && (
        <>
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 text-blue-500" />
            <span className="text-sm text-gray-600">
              {userCount} {userCount === 1 ? 'user' : 'users'} active
            </span>
          </div>
          
          {activeUsers.length > 0 && (
            <div className="flex items-center space-x-2">
              <div className="flex -space-x-2">
                {activeUsers.slice(0, 5).map((user) => (
                  <div
                    key={user.user_id}
                    className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-500 text-white text-xs font-medium border-2 border-white"
                    title={user.username}
                    style={{
                      backgroundColor: `hsl(${(user.user_id.charCodeAt(0) * 137.508) % 360}, 70%, 50%)`
                    }}
                  >
                    {user.username.charAt(0).toUpperCase()}
                  </div>
                ))}
                {activeUsers.length > 5 && (
                  <div className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-gray-500 text-white text-xs font-medium border-2 border-white">
                    +{activeUsers.length - 5}
                  </div>
                )}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

// Component to show individual collaborator cursors in the editor
export const CollaboratorCursor: React.FC<{
  collaborator: CollaboratorState;
  isVisible: boolean;
}> = ({ collaborator, isVisible }) => {
  if (!isVisible) return null;

  return (
    <div 
      className="absolute pointer-events-none z-10"
      style={{
        borderLeft: `2px solid ${collaborator.color}`,
        height: '1.2em',
      }}
    >
      <div 
        className="absolute -top-6 left-0 px-2 py-1 text-xs text-white rounded whitespace-nowrap shadow-sm"
        style={{ backgroundColor: collaborator.color }}
      >
        {collaborator.username}
      </div>
    </div>
  );
};

// Component to show collaborator selections
export const CollaboratorSelection: React.FC<{
  collaborator: CollaboratorState;
  isVisible: boolean;
}> = ({ collaborator, isVisible }) => {
  if (!isVisible || !collaborator.selection) return null;

  return (
    <div 
      className="absolute pointer-events-none z-5 rounded"
      style={{
        backgroundColor: `${collaborator.color}20`, // 20% opacity
        border: `1px solid ${collaborator.color}40`,
      }}
    />
  );
};