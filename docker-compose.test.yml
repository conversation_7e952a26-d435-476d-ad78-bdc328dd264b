version: '3.8'

services:
  # Test database
  test-db:
    image: postgres:14-alpine
    container_name: enhanced-pastebin-test-db
    environment:
      POSTGRES_DB: enhanced_pastebin_test
      POSTGRES_USER: testuser
      POSTGRES_PASSWORD: testpass
    ports:
      - "5433:5432"
    volumes:
      - test_postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U testuser -d enhanced_pastebin_test"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Test Redis (for caching tests)
  test-redis:
    image: redis:7-alpine
    container_name: enhanced-pastebin-test-redis
    ports:
      - "6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend test runner
  backend-test:
    build:
      context: .
      dockerfile: Dockerfile.test
    container_name: enhanced-pastebin-backend-test
    environment:
      - DATABASE_URL=*****************************************/enhanced_pastebin_test?sslmode=disable
      - REDIS_URL=redis://test-redis:6379
      - JWT_SECRET=test-jwt-secret
      - GIN_MODE=test
    depends_on:
      test-db:
        condition: service_healthy
      test-redis:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/bin
    working_dir: /app
    command: >
      sh -c "
        echo 'Running database migrations...' &&
        migrate -path migrations -database $$DATABASE_URL up &&
        echo 'Running backend tests...' &&
        go test -v -coverprofile=coverage.out ./... &&
        echo 'Generating coverage report...' &&
        go tool cover -html=coverage.out -o coverage.html
      "

  # Frontend test runner
  frontend-test:
    build:
      context: ./frontend
      dockerfile: Dockerfile.test
    container_name: enhanced-pastebin-frontend-test
    environment:
      - CI=true
      - VITE_API_URL=http://backend-test:8080/api/v1
    volumes:
      - ./frontend:/app
      - /app/node_modules
    working_dir: /app
    command: >
      sh -c "
        echo 'Running frontend tests...' &&
        npm test -- --coverage --watchAll=false &&
        echo 'Running linting...' &&
        npm run lint &&
        echo 'Running type checking...' &&
        npm run type-check
      "

  # Integration test runner
  integration-test:
    build:
      context: .
      dockerfile: Dockerfile.integration
    container_name: enhanced-pastebin-integration-test
    environment:
      - DATABASE_URL=*****************************************/enhanced_pastebin_test?sslmode=disable
      - REDIS_URL=redis://test-redis:6379
      - JWT_SECRET=test-jwt-secret
      - API_URL=http://backend:8080/api/v1
      - FRONTEND_URL=http://frontend:3000
    depends_on:
      test-db:
        condition: service_healthy
      test-redis:
        condition: service_healthy
      backend:
        condition: service_started
      frontend:
        condition: service_started
    volumes:
      - .:/app
      - ./tests/integration:/tests
    working_dir: /tests
    command: >
      sh -c "
        echo 'Waiting for services to be ready...' &&
        sleep 30 &&
        echo 'Running integration tests...' &&
        npm test
      "

  # Backend service for integration tests
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: enhanced-pastebin-backend-integration
    environment:
      - DATABASE_URL=*****************************************/enhanced_pastebin_test?sslmode=disable
      - REDIS_URL=redis://test-redis:6379
      - JWT_SECRET=test-jwt-secret
      - PORT=8080
      - GIN_MODE=test
      - CORS_ORIGINS=http://frontend:3000
    depends_on:
      test-db:
        condition: service_healthy
      test-redis:
        condition: service_healthy
    ports:
      - "8081:8080"
    command: >
      sh -c "
        echo 'Running database migrations...' &&
        migrate -path migrations -database $$DATABASE_URL up &&
        echo 'Starting backend server...' &&
        ./bin/server
      "

  # Frontend service for integration tests
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: enhanced-pastebin-frontend-integration
    environment:
      - VITE_API_URL=http://backend:8080/api/v1
      - VITE_WS_URL=ws://backend:8080/api/v1/ws
    depends_on:
      - backend
    ports:
      - "3001:3000"

volumes:
  test_postgres_data:
