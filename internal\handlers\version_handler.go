package handlers

import (
	"net/http"
	"strconv"

	"enhanced-pastebin/internal/services"

	"github.com/gin-gonic/gin"
)

type VersionHandler struct {
	versionService services.VersionService
}

func NewVersionHandler(versionService services.VersionService) *VersionHandler {
	return &VersionHandler{
		versionService: versionService,
	}
}

func (h *VersionHandler) GetVersions(c *gin.Context) {
	pasteID := c.Param("pasteId")
	
	// Parse pagination parameters
	limitStr := c.Default<PERSON>y("limit", "20")
	offsetStr := c.Default<PERSON>uery("offset", "0")
	
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}
	
	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	versions, err := h.versionService.GetVersions(pasteID, limit, offset)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Get total count for pagination
	totalCount, err := h.versionService.GetVersionCount(pasteID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	response := gin.H{
		"versions":    versions,
		"total_count": totalCount,
		"limit":       limit,
		"offset":      offset,
	}

	c.JSON(http.StatusOK, response)
}

func (h *VersionHandler) GetVersion(c *gin.Context) {
	versionID := c.Param("versionId")

	version, err := h.versionService.GetVersion(versionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if version == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Version not found"})
		return
	}

	c.JSON(http.StatusOK, version)
}

func (h *VersionHandler) GetLatestVersion(c *gin.Context) {
	pasteID := c.Param("pasteId")

	version, err := h.versionService.GetLatestVersion(pasteID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if version == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No versions found"})
		return
	}

	c.JSON(http.StatusOK, version)
}

func (h *VersionHandler) CompareVersions(c *gin.Context) {
	version1ID := c.Query("version1")
	version2ID := c.Query("version2")

	if version1ID == "" || version2ID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Both version1 and version2 parameters are required"})
		return
	}

	comparison, err := h.versionService.CompareVersions(version1ID, version2ID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, comparison)
}
