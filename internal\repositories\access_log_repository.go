package repositories

import (
	"database/sql"
	"enhanced-pastebin/internal/models"
)

type AccessLogRepository interface {
	Create(log *models.AccessLog) error
	GetByPasteID(pasteID string, limit, offset int) ([]*models.AccessLog, error)
	GetByUserID(userID string, limit, offset int) ([]*models.AccessLog, error)
	GetCountByPasteID(pasteID string) (int, error)
	GetSecurityEvents(limit, offset int) ([]*models.AccessLog, error)
	DeleteOldLogs(days int) error
}

type accessLogRepository struct {
	db *sql.DB
}

func NewAccessLogRepository(db *sql.DB) AccessLogRepository {
	return &accessLogRepository{db: db}
}

func (r *accessLogRepository) Create(log *models.AccessLog) error {
	query := `
		INSERT INTO access_logs (id, paste_id, user_id, ip_address, user_agent, action, details, timestamp)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`
	
	_, err := r.db.Exec(query, log.ID, log.PasteID, log.UserID, log.IPAddress, 
		log.UserAgent, log.Action, log.Details, log.Timestamp)
	return err
}

func (r *accessLogRepository) GetByPasteID(pasteID string, limit, offset int) ([]*models.AccessLog, error) {
	query := `
		SELECT al.id, al.paste_id, al.user_id, u.username, al.ip_address, 
			   al.user_agent, al.action, al.details, al.timestamp
		FROM access_logs al
		LEFT JOIN users u ON al.user_id = u.id
		WHERE al.paste_id = $1
		ORDER BY al.timestamp DESC
		LIMIT $2 OFFSET $3`
	
	rows, err := r.db.Query(query, pasteID, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var logs []*models.AccessLog
	for rows.Next() {
		log := &models.AccessLog{}
		err := rows.Scan(&log.ID, &log.PasteID, &log.UserID, &log.Username,
			&log.IPAddress, &log.UserAgent, &log.Action, &log.Details, &log.Timestamp)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	return logs, rows.Err()
}

func (r *accessLogRepository) GetByUserID(userID string, limit, offset int) ([]*models.AccessLog, error) {
	query := `
		SELECT al.id, al.paste_id, al.user_id, u.username, al.ip_address, 
			   al.user_agent, al.action, al.details, al.timestamp
		FROM access_logs al
		LEFT JOIN users u ON al.user_id = u.id
		WHERE al.user_id = $1
		ORDER BY al.timestamp DESC
		LIMIT $2 OFFSET $3`
	
	rows, err := r.db.Query(query, userID, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var logs []*models.AccessLog
	for rows.Next() {
		log := &models.AccessLog{}
		err := rows.Scan(&log.ID, &log.PasteID, &log.UserID, &log.Username,
			&log.IPAddress, &log.UserAgent, &log.Action, &log.Details, &log.Timestamp)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	return logs, rows.Err()
}

func (r *accessLogRepository) GetCountByPasteID(pasteID string) (int, error) {
	var count int
	query := `SELECT COUNT(*) FROM access_logs WHERE paste_id = $1`
	err := r.db.QueryRow(query, pasteID).Scan(&count)
	return count, err
}

func (r *accessLogRepository) GetSecurityEvents(limit, offset int) ([]*models.AccessLog, error) {
	query := `
		SELECT al.id, al.paste_id, al.user_id, u.username, al.ip_address, 
			   al.user_agent, al.action, al.details, al.timestamp
		FROM access_logs al
		LEFT JOIN users u ON al.user_id = u.id
		WHERE al.action = 'security_event' OR al.details LIKE '%security%' OR al.details LIKE '%suspicious%'
		ORDER BY al.timestamp DESC
		LIMIT $1 OFFSET $2`
	
	rows, err := r.db.Query(query, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var logs []*models.AccessLog
	for rows.Next() {
		log := &models.AccessLog{}
		err := rows.Scan(&log.ID, &log.PasteID, &log.UserID, &log.Username,
			&log.IPAddress, &log.UserAgent, &log.Action, &log.Details, &log.Timestamp)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	return logs, rows.Err()
}

func (r *accessLogRepository) DeleteOldLogs(days int) error {
	query := `DELETE FROM access_logs WHERE timestamp < NOW() - INTERVAL '%d days'`
	_, err := r.db.Exec(query, days)
	return err
}
