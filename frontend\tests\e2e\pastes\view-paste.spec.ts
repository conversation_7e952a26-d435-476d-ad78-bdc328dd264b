import { test, expect } from '@playwright/test';
import { ViewPastePage, HomePage } from '../../pages';
import { TestHelpers } from '../../utils/test-helpers';

test.describe('View Paste', () => {
  let viewPastePage: ViewPastePage;
  let homePage: HomePage;
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    viewPastePage = new ViewPastePage(page);
    homePage = new HomePage(page);
    helpers = new TestHelpers();
  });

  test.afterEach(async () => {
    await helpers.cleanupTestData();
  });

  test('should display public paste correctly', async () => {
    const paste = await helpers.api.createTestPaste({
      title: 'Public Test Paste',
      content: 'console.log("Hello, World!");',
      language: 'javascript',
      is_public: true
    });

    await viewPastePage.visit(paste.id!);
    
    await viewPastePage.expectToBeOnPastePage(paste.id);
    await viewPastePage.expectPasteTitle(paste.title);
    await viewPastePage.expectPasteContent(paste.content);
    await viewPastePage.expectPasteLanguage(paste.language);
    await viewPastePage.expectPasteMetadata();
  });

  test('should display paste via custom URL', async () => {
    const customUrl = `test-custom-${Date.now()}`;
    const paste = await helpers.api.createTestPaste({
      title: 'Custom URL Paste',
      content: 'print("Custom URL test")',
      custom_url: customUrl,
      is_public: true
    });

    await viewPastePage.visitByCustomUrl(customUrl);
    
    await viewPastePage.expectPasteTitle(paste.title);
    await viewPastePage.expectPasteContent(paste.content);
  });

  test('should show action buttons for public paste', async () => {
    const paste = await helpers.api.createTestPaste({ is_public: true });

    await viewPastePage.visit(paste.id!);
    
    await viewPastePage.expectActionButtons();
    await viewPastePage.expectEditButtonNotVisible(); // Not owner
    await viewPastePage.expectDeleteButtonNotVisible(); // Not owner
  });

  test('should show owner actions for owned paste', async ({ page }) => {
    // Login and create paste as owner
    const user = await helpers.loginAsTestUser(page);
    const paste = await helpers.api.createTestPaste({
      title: 'Owned Paste',
      content: 'This is my paste',
      is_public: true
    });

    await viewPastePage.visit(paste.id!);
    
    await viewPastePage.expectEditButtonVisible();
    await viewPastePage.expectDeleteButtonVisible();
    await viewPastePage.expectPasteAuthor(user.username);
  });

  test('should copy paste content to clipboard', async () => {
    const paste = await helpers.api.createTestPaste({
      content: 'console.log("Copy test");',
      is_public: true
    });

    await viewPastePage.visit(paste.id!);
    await viewPastePage.copyPasteContent();
    
    // Should show success message
    await viewPastePage.expectSuccessMessage(/copied/i);
  });

  test('should download paste content', async () => {
    const paste = await helpers.api.createTestPaste({
      title: 'Download Test',
      content: 'console.log("Download test");',
      language: 'javascript',
      is_public: true
    });

    await viewPastePage.visit(paste.id!);
    const download = await viewPastePage.downloadPaste();
    
    expect(download).toBeTruthy();
    expect(download.suggestedFilename()).toContain('.js');
  });

  test('should fork paste to create new one', async ({ page }) => {
    // Login first
    await helpers.loginAsTestUser(page);
    
    const paste = await helpers.api.createTestPaste({
      title: 'Original Paste',
      content: 'console.log("Original");',
      is_public: true
    });

    await viewPastePage.visit(paste.id!);
    await viewPastePage.forkPaste();
    
    // Should be redirected to create page with pre-filled content
    await expect(page).toHaveURL('/create');
  });

  test('should like paste and update count', async ({ page }) => {
    // Login first
    await helpers.loginAsTestUser(page);
    
    const paste = await helpers.api.createTestPaste({ is_public: true });

    await viewPastePage.visit(paste.id!);
    
    const initialCount = await viewPastePage.getLikeCount();
    await viewPastePage.likePaste();
    
    // Like count should increase
    await viewPastePage.expectLikeCount(initialCount + 1);
  });

  test('should add and display comments', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);
    
    const paste = await helpers.api.createTestPaste({ is_public: true });

    await viewPastePage.visit(paste.id!);
    
    const comment = 'This is a test comment';
    await viewPastePage.addComment(comment);
    
    await viewPastePage.expectCommentInList(comment);
    await viewPastePage.expectCommentCount(1);
  });

  test('should display version history', async () => {
    const paste = await helpers.api.createTestPaste({ is_public: true });

    await viewPastePage.visit(paste.id!);
    await viewPastePage.viewVersionHistory();
    
    // Should show version history panel
    await expect(viewPastePage.versionHistory).toBeVisible();
  });

  test('should handle private paste access without permission', async () => {
    // Create private paste
    const user = await helpers.api.loginAsTestUser();
    const paste = await helpers.api.createTestPaste({ is_public: false });

    // Try to access without login
    await viewPastePage.visit(paste.id!);
    
    // Should show access denied or redirect to login
    await expect(viewPastePage.page.locator('h1, .error')).toContainText(/private|access denied|not found/i);
  });

  test('should handle non-existent paste', async () => {
    await viewPastePage.visit('nonexistent-id');
    await viewPastePage.expectPasteNotFound();
  });

  test('should handle expired paste', async () => {
    // Create paste that expires immediately (if supported by API)
    const paste = await helpers.api.createTestPaste({
      expires_at: new Date(Date.now() - 1000).toISOString() // Expired 1 second ago
    });

    await viewPastePage.visit(paste.id!);
    await viewPastePage.expectPasteExpired();
  });

  test('should display expiration warning for expiring paste', async () => {
    // Create paste that expires soon
    const paste = await helpers.api.createTestPaste({
      expires_at: new Date(Date.now() + 3600000).toISOString() // Expires in 1 hour
    });

    await viewPastePage.visit(paste.id!);
    await viewPastePage.expectExpirationWarning();
  });

  test('should handle large paste content', async () => {
    const largeContent = 'console.log("test");\n'.repeat(1000);
    const paste = await helpers.api.createTestPaste({
      content: largeContent,
      is_public: true
    });

    await viewPastePage.visit(paste.id!);
    
    await viewPastePage.expectPasteContent('console.log("test");');
    // Should handle large content without performance issues
  });

  test('should display syntax highlighting for different languages', async () => {
    const languages = ['javascript', 'python', 'java', 'html', 'css'];
    
    for (const language of languages) {
      const paste = await helpers.api.createTestPaste({
        content: `// This is ${language} code\nconsole.log("test");`,
        language,
        is_public: true
      });

      await viewPastePage.visit(paste.id!);
      await viewPastePage.expectPasteLanguage(language);
      
      // Should have syntax highlighting (Monaco editor or similar)
      await expect(viewPastePage.pasteContent).toBeVisible();
    }
  });

  test('should handle special characters and unicode', async () => {
    const specialContent = `
      const emoji = "🚀 Hello World! 🌟";
      const unicode = "Ñoño café résumé";
      const symbols = "!@#$%^&*()_+-=[]{}|;':\",./<>?";
    `;

    const paste = await helpers.api.createTestPaste({
      content: specialContent,
      is_public: true
    });

    await viewPastePage.visit(paste.id!);
    
    await viewPastePage.expectPasteContent('🚀 Hello World! 🌟');
    await viewPastePage.expectPasteContent('Ñoño café résumé');
  });

  test('should update view count on visit', async () => {
    const paste = await helpers.api.createTestPaste({ is_public: true });

    await viewPastePage.visit(paste.id!);
    
    const initialCount = await viewPastePage.getViewCount();
    
    // Refresh page to increment view count
    await viewPastePage.page.reload();
    
    const newCount = await viewPastePage.getViewCount();
    expect(newCount).toBeGreaterThan(initialCount);
  });

  test('should handle network errors gracefully', async ({ page }) => {
    const paste = await helpers.api.createTestPaste({ is_public: true });

    // Simulate network failure
    await page.route(`**/api/v1/pastes/${paste.id}`, route => route.abort());

    await viewPastePage.visit(paste.id!);
    
    // Should show error message
    await viewPastePage.expectErrorMessage(/network|connection|failed/i);
  });

  test('should handle server errors gracefully', async ({ page }) => {
    const paste = await helpers.api.createTestPaste({ is_public: true });

    // Simulate server error
    await page.route(`**/api/v1/pastes/${paste.id}`, route => 
      route.fulfill({ status: 500, body: JSON.stringify({ error: 'Internal server error' }) })
    );

    await viewPastePage.visit(paste.id!);
    
    // Should show error message
    await viewPastePage.expectErrorMessage(/server error|internal error/i);
  });

  test('should navigate back to home from paste view', async () => {
    const paste = await helpers.api.createTestPaste({ is_public: true });

    await viewPastePage.visit(paste.id!);
    await viewPastePage.navigateToHome();
    
    await homePage.expectToBeOnHomePage();
  });

  test('should handle collaborative features if enabled', async ({ page }) => {
    // Login first
    await helpers.loginAsTestUser(page);
    
    const paste = await helpers.api.createTestPaste({ is_public: true });

    await viewPastePage.visit(paste.id!);
    
    // Check if collaboration panel is available
    if (await viewPastePage.collaborationPanel.isVisible()) {
      await expect(viewPastePage.collaborationPanel).toBeVisible();
    }
    
    // Check if chat panel is available
    if (await viewPastePage.chatPanel.isVisible()) {
      await expect(viewPastePage.chatPanel).toBeVisible();
    }
  });
});
