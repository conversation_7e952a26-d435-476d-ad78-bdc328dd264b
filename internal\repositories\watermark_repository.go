package repositories

import (
	"database/sql"
	"enhanced-pastebin/internal/models"
)

type WatermarkRepository interface {
	Create(watermark *models.WatermarkInfo) error
	GetByID(id string) (*models.WatermarkInfo, error)
	GetByPasteID(pasteID string) ([]*models.WatermarkInfo, error)
	GetByUserID(userID string) ([]*models.WatermarkInfo, error)
	GetByWatermarkID(watermarkID string) (*models.WatermarkInfo, error)
	Delete(id string) error
}

type watermarkRepository struct {
	db *sql.DB
}

func NewWatermarkRepository(db *sql.DB) WatermarkRepository {
	return &watermarkRepository{db: db}
}

func (r *watermarkRepository) Create(watermark *models.WatermarkInfo) error {
	query := `
		INSERT INTO watermarks (id, paste_id, user_id, watermark_id, created_at)
		VALUES ($1, $2, $3, $4, $5)`
	
	_, err := r.db.Exec(query, watermark.ID, watermark.PasteID, watermark.UserID,
		watermark.WatermarkID, watermark.CreatedAt)
	return err
}

func (r *watermarkRepository) GetByID(id string) (*models.WatermarkInfo, error) {
	query := `
		SELECT w.id, w.paste_id, w.user_id, u.username, w.watermark_id, w.created_at
		FROM watermarks w
		LEFT JOIN users u ON w.user_id = u.id
		WHERE w.id = $1`
	
	watermark := &models.WatermarkInfo{}
	err := r.db.QueryRow(query, id).Scan(&watermark.ID, &watermark.PasteID,
		&watermark.UserID, &watermark.Username, &watermark.WatermarkID, &watermark.CreatedAt)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	
	return watermark, nil
}

func (r *watermarkRepository) GetByPasteID(pasteID string) ([]*models.WatermarkInfo, error) {
	query := `
		SELECT w.id, w.paste_id, w.user_id, u.username, w.watermark_id, w.created_at
		FROM watermarks w
		LEFT JOIN users u ON w.user_id = u.id
		WHERE w.paste_id = $1
		ORDER BY w.created_at DESC`
	
	rows, err := r.db.Query(query, pasteID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var watermarks []*models.WatermarkInfo
	for rows.Next() {
		watermark := &models.WatermarkInfo{}
		err := rows.Scan(&watermark.ID, &watermark.PasteID, &watermark.UserID,
			&watermark.Username, &watermark.WatermarkID, &watermark.CreatedAt)
		if err != nil {
			return nil, err
		}
		watermarks = append(watermarks, watermark)
	}

	return watermarks, rows.Err()
}

func (r *watermarkRepository) GetByUserID(userID string) ([]*models.WatermarkInfo, error) {
	query := `
		SELECT w.id, w.paste_id, w.user_id, u.username, w.watermark_id, w.created_at
		FROM watermarks w
		LEFT JOIN users u ON w.user_id = u.id
		WHERE w.user_id = $1
		ORDER BY w.created_at DESC`
	
	rows, err := r.db.Query(query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var watermarks []*models.WatermarkInfo
	for rows.Next() {
		watermark := &models.WatermarkInfo{}
		err := rows.Scan(&watermark.ID, &watermark.PasteID, &watermark.UserID,
			&watermark.Username, &watermark.WatermarkID, &watermark.CreatedAt)
		if err != nil {
			return nil, err
		}
		watermarks = append(watermarks, watermark)
	}

	return watermarks, rows.Err()
}

func (r *watermarkRepository) GetByWatermarkID(watermarkID string) (*models.WatermarkInfo, error) {
	query := `
		SELECT w.id, w.paste_id, w.user_id, u.username, w.watermark_id, w.created_at
		FROM watermarks w
		LEFT JOIN users u ON w.user_id = u.id
		WHERE w.watermark_id = $1`
	
	watermark := &models.WatermarkInfo{}
	err := r.db.QueryRow(query, watermarkID).Scan(&watermark.ID, &watermark.PasteID,
		&watermark.UserID, &watermark.Username, &watermark.WatermarkID, &watermark.CreatedAt)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	
	return watermark, nil
}

func (r *watermarkRepository) Delete(id string) error {
	query := `DELETE FROM watermarks WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}
