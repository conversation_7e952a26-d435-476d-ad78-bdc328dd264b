import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { User, Calendar, Award, FileText, Eye, Code, Clock } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { userAPI, PublicUserProfile } from '@/lib/api'

export default function PublicProfile() {
  const { username } = useParams<{ username: string }>()
  const { toast } = useToast()
  const [profile, setProfile] = useState<PublicUserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (username) {
      loadProfile()
    }
  }, [username])

  const loadProfile = async () => {
    if (!username) return

    try {
      setIsLoading(true)
      const response = await userAPI.getPublicProfile(username)
      setProfile(response.data)
    } catch (error) {
      console.error('Error loading profile:', error)
      toast({
        title: "Error",
        description: "Failed to load user profile",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardContent className="p-8">
            <div className="text-center">Loading profile...</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardContent className="p-8">
            <div className="text-center">User not found</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* User Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            {profile.username}
          </CardTitle>
          <CardDescription>Public Profile</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-6 text-sm text-muted-foreground">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              Joined {new Date(profile.created_at).toLocaleDateString()}
            </div>
            <div className="flex items-center">
              <Award className="h-4 w-4 mr-1" />
              {profile.reputation_score} reputation
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              Last active {new Date(profile.last_active).toLocaleDateString()}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Statistics</CardTitle>
          <CardDescription>Public activity overview</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold">{profile.stats.total_pastes}</div>
              <div className="text-sm text-muted-foreground">Public Pastes</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold">{profile.stats.total_views}</div>
              <div className="text-sm text-muted-foreground">Total Views</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-sm font-medium">{profile.stats.most_used_language || 'N/A'}</div>
              <div className="text-sm text-muted-foreground">Most Used Language</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold">
                {Object.keys(profile.stats.language_count).length}
              </div>
              <div className="text-sm text-muted-foreground">Languages Used</div>
            </div>
          </div>
          
          {Object.keys(profile.stats.language_count).length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">Language Breakdown</h4>
              <div className="flex flex-wrap gap-2">
                {Object.entries(profile.stats.language_count).map(([lang, count]) => (
                  <Badge key={lang} variant="outline">
                    {lang}: {count}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Public Pastes */}
      <Card>
        <CardHeader>
          <CardTitle>Public Pastes</CardTitle>
          <CardDescription>Recent public code snippets</CardDescription>
        </CardHeader>
        <CardContent>
          {profile.recent_pastes.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              No public pastes available
            </div>
          ) : (
            <div className="space-y-3">
              {profile.recent_pastes.map((paste) => (
                <div key={paste.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <Link 
                          to={`/paste/${paste.id}`}
                          className="font-medium hover:underline"
                        >
                          {paste.title}
                        </Link>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground mt-1">
                          <Code className="h-3 w-3" />
                          <span>{paste.language}</span>
                          <Eye className="h-3 w-3 ml-2" />
                          <span>{paste.view_count} views</span>
                          <Calendar className="h-3 w-3 ml-2" />
                          <span>{new Date(paste.created_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {paste.custom_url && (
                        <Badge variant="outline">Custom URL</Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
