package models

import (
	"time"
)

type User struct {
	ID             string    `json:"id" db:"id" validate:"required,uuid"`
	Username       string    `json:"username" db:"username" validate:"required,min=3,max=50,alphanum"`
	Email          string    `json:"email" db:"email" validate:"required,email,max=255"`
	PasswordHash   string    `json:"-" db:"password_hash" validate:"required"`
	ReputationScore int      `json:"reputation_score" db:"reputation_score" validate:"min=0"`
	CreatedAt      time.Time `json:"created_at" db:"created_at"`
	LastActive     time.Time `json:"last_active" db:"last_active"`
}

type CreateUserRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50,alphanum" validate:"required,min=3,max=50,alphanum"`
	Email    string `json:"email" binding:"required,email,max=255" validate:"required,email,max=255"`
	Password string `json:"password" binding:"required,min=6,max=128" validate:"required,min=6,max=128"`
}

type LoginRequest struct {
	Email    string `json:"email" binding:"required,email,max=255" validate:"required,email,max=255"`
	Password string `json:"password" binding:"required,min=1,max=128" validate:"required,min=1,max=128"`
}

type LoginResponse struct {
	Token string `json:"token"`
	User  User   `json:"user"`
}