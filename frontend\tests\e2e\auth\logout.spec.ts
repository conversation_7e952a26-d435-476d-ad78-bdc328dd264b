import { test, expect } from '@playwright/test';
import { LoginPage, HomePage } from '../../pages';
import { TestHelpers } from '../../utils/test-helpers';

test.describe('User Logout', () => {
  let loginPage: LoginPage;
  let homePage: HomePage;
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    homePage = new HomePage(page);
    helpers = new TestHelpers();
  });

  test.afterEach(async () => {
    await helpers.cleanupTestData();
  });

  test('should logout successfully', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);
    await homePage.visit();
    await loginPage.expectToBeLoggedIn(user.username);

    // Logout
    await loginPage.logout();
    
    // Should be logged out and redirected
    await loginPage.expectToBeLoggedOut();
    await expect(page).toHaveURL(/\/(|login)/);
  });

  test('should clear user session on logout', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);
    await homePage.visit();

    // Logout
    await loginPage.logout();
    
    // Try to access protected page
    await page.goto('/profile');
    
    // Should be redirected to login page
    await loginPage.expectToBeOnLoginPage();
  });

  test('should handle logout when already logged out', async ({ page }) => {
    await homePage.visit();
    await loginPage.expectToBeLoggedOut();

    // Try to logout (should not cause errors)
    const logoutButton = page.locator('button:has-text("Logout"), a:has-text("Logout")');
    if (await logoutButton.isVisible()) {
      await logoutButton.click();
    }

    // Should still be logged out
    await loginPage.expectToBeLoggedOut();
  });

  test('should clear local storage on logout', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);
    await homePage.visit();

    // Check that auth token is stored
    const tokenBefore = await page.evaluate(() => localStorage.getItem('authToken') || sessionStorage.getItem('authToken'));
    expect(tokenBefore).toBeTruthy();

    // Logout
    await loginPage.logout();

    // Check that auth token is cleared
    const tokenAfter = await page.evaluate(() => localStorage.getItem('authToken') || sessionStorage.getItem('authToken'));
    expect(tokenAfter).toBeFalsy();
  });

  test('should handle logout from multiple tabs', async ({ browser }) => {
    const user = await helpers.api.createTestUser();

    // Open two tabs and login in both
    const context = await browser.newContext();
    const page1 = await context.newPage();
    const page2 = await context.newPage();

    const loginPage1 = new LoginPage(page1);
    const loginPage2 = new LoginPage(page2);
    const homePage1 = new HomePage(page1);
    const homePage2 = new HomePage(page2);

    // Login in both tabs
    await loginPage1.visit();
    await loginPage1.loginAndWaitForRedirect(user.username, user.password);
    
    await loginPage2.visit();
    await loginPage2.loginAndWaitForRedirect(user.username, user.password);

    // Navigate to home in both tabs
    await homePage1.visit();
    await homePage2.visit();

    // Verify both are logged in
    await loginPage1.expectToBeLoggedIn(user.username);
    await loginPage2.expectToBeLoggedIn(user.username);

    // Logout from first tab
    await loginPage1.logout();

    // First tab should be logged out
    await loginPage1.expectToBeLoggedOut();

    // Second tab should also be logged out (if using shared session)
    await page2.reload();
    await loginPage2.expectToBeLoggedOut();

    await context.close();
  });

  test('should handle network errors during logout', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);
    await homePage.visit();

    // Simulate network failure for logout request
    await page.route('**/api/v1/users/logout', route => route.abort());

    // Logout should still work (client-side logout)
    await loginPage.logout();
    await loginPage.expectToBeLoggedOut();
  });

  test('should handle server errors during logout', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);
    await homePage.visit();

    // Simulate server error for logout request
    await page.route('**/api/v1/users/logout', route => 
      route.fulfill({ status: 500, body: JSON.stringify({ error: 'Internal server error' }) })
    );

    // Logout should still work (client-side logout)
    await loginPage.logout();
    await loginPage.expectToBeLoggedOut();
  });

  test('should redirect to home page after logout', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);
    await page.goto('/profile');

    // Logout
    await loginPage.logout();
    
    // Should be redirected to home page
    await expect(page).toHaveURL('/');
    await loginPage.expectToBeLoggedOut();
  });

  test('should show logout confirmation if configured', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);
    await homePage.visit();

    // If logout confirmation is enabled, handle it
    const logoutButton = page.locator('button:has-text("Logout"), a:has-text("Logout")').first();
    await logoutButton.click();

    // Check if confirmation dialog appears
    const confirmDialog = page.locator('.logout-confirm, [data-testid="logout-confirm"]');
    if (await confirmDialog.isVisible()) {
      const confirmButton = page.locator('button:has-text("Confirm"), button:has-text("Yes")');
      await confirmButton.click();
    }

    await loginPage.expectToBeLoggedOut();
  });

  test('should handle logout timeout', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);
    await homePage.visit();

    // Simulate slow logout request
    await page.route('**/api/v1/users/logout', route => 
      new Promise(resolve => setTimeout(() => resolve(route.fulfill({ status: 200 })), 10000))
    );

    // Logout should still work within reasonable time
    await loginPage.logout();
    await loginPage.expectToBeLoggedOut();
  });

  test('should preserve logout state after page refresh', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);
    await homePage.visit();

    // Logout
    await loginPage.logout();
    await loginPage.expectToBeLoggedOut();

    // Refresh page
    await page.reload();

    // Should still be logged out
    await loginPage.expectToBeLoggedOut();
  });

  test('should handle automatic logout on token expiration', async ({ page }) => {
    // Login first
    const user = await helpers.loginAsTestUser(page);
    await homePage.visit();
    await loginPage.expectToBeLoggedIn(user.username);

    // Simulate expired token by intercepting API calls
    await page.route('**/api/v1/**', route => {
      if (route.request().headers()['authorization']) {
        route.fulfill({ 
          status: 401, 
          body: JSON.stringify({ error: 'Token expired' })
        });
      } else {
        route.continue();
      }
    });

    // Make an API call that would trigger token validation
    await page.goto('/profile');

    // Should be automatically logged out and redirected to login
    await loginPage.expectToBeOnLoginPage();
    await loginPage.expectToBeLoggedOut();
  });
});
