package repositories

import (
	"database/sql"
	"enhanced-pastebin/internal/models"
)

type VersionRepository interface {
	Create(version *models.PasteVersion) error
	GetByPasteID(pasteID string, limit, offset int) ([]*models.PasteVersion, error)
	GetByID(id string) (*models.PasteVersion, error)
	GetLatestVersion(pasteID string) (*models.PasteVersion, error)
	GetVersionCount(pasteID string) (int, error)
	GetNextVersionNumber(pasteID string) (int, error)
	Delete(id string) error
}

type versionRepository struct {
	db *sql.DB
}

func NewVersionRepository(db *sql.DB) VersionRepository {
	return &versionRepository{db: db}
}

func (r *versionRepository) Create(version *models.PasteVersion) error {
	query := `
		INSERT INTO paste_versions (id, paste_id, version_number, content, created_at, created_by)
		VALUES ($1, $2, $3, $4, $5, $6)`
	
	_, err := r.db.Exec(query, version.ID, version.PasteID, version.VersionNumber,
		version.Content, version.CreatedAt, version.CreatedBy)
	return err
}

func (r *versionRepository) GetByPasteID(pasteID string, limit, offset int) ([]*models.PasteVersion, error) {
	query := `
		SELECT pv.id, pv.paste_id, pv.version_number, pv.content, pv.created_at, pv.created_by, u.username
		FROM paste_versions pv
		LEFT JOIN users u ON pv.created_by = u.id
		WHERE pv.paste_id = $1 
		ORDER BY pv.version_number DESC 
		LIMIT $2 OFFSET $3`
	
	rows, err := r.db.Query(query, pasteID, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var versions []*models.PasteVersion
	for rows.Next() {
		version := &models.PasteVersion{}
		err := rows.Scan(
			&version.ID, &version.PasteID, &version.VersionNumber, &version.Content,
			&version.CreatedAt, &version.CreatedBy, &version.CreatedByUsername)
		if err != nil {
			return nil, err
		}
		versions = append(versions, version)
	}

	return versions, rows.Err()
}

func (r *versionRepository) GetByID(id string) (*models.PasteVersion, error) {
	version := &models.PasteVersion{}
	query := `
		SELECT pv.id, pv.paste_id, pv.version_number, pv.content, pv.created_at, pv.created_by, u.username
		FROM paste_versions pv
		LEFT JOIN users u ON pv.created_by = u.id
		WHERE pv.id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&version.ID, &version.PasteID, &version.VersionNumber, &version.Content,
		&version.CreatedAt, &version.CreatedBy, &version.CreatedByUsername)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return version, err
}

func (r *versionRepository) GetLatestVersion(pasteID string) (*models.PasteVersion, error) {
	version := &models.PasteVersion{}
	query := `
		SELECT pv.id, pv.paste_id, pv.version_number, pv.content, pv.created_at, pv.created_by, u.username
		FROM paste_versions pv
		LEFT JOIN users u ON pv.created_by = u.id
		WHERE pv.paste_id = $1 
		ORDER BY pv.version_number DESC 
		LIMIT 1`
	
	err := r.db.QueryRow(query, pasteID).Scan(
		&version.ID, &version.PasteID, &version.VersionNumber, &version.Content,
		&version.CreatedAt, &version.CreatedBy, &version.CreatedByUsername)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return version, err
}

func (r *versionRepository) GetVersionCount(pasteID string) (int, error) {
	var count int
	query := `SELECT COUNT(*) FROM paste_versions WHERE paste_id = $1`
	err := r.db.QueryRow(query, pasteID).Scan(&count)
	return count, err
}

func (r *versionRepository) GetNextVersionNumber(pasteID string) (int, error) {
	var maxVersion sql.NullInt64
	query := `SELECT MAX(version_number) FROM paste_versions WHERE paste_id = $1`
	err := r.db.QueryRow(query, pasteID).Scan(&maxVersion)
	if err != nil {
		return 0, err
	}
	
	if maxVersion.Valid {
		return int(maxVersion.Int64) + 1, nil
	}
	return 1, nil
}

func (r *versionRepository) Delete(id string) error {
	query := `DELETE FROM paste_versions WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}