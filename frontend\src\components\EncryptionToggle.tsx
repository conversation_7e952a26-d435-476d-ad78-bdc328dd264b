import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Key, AlertTriangle } from 'lucide-react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { useEncryption } from '../hooks/useEncryption';

interface EncryptionToggleProps {
  onEncryptionChange?: (enabled: boolean) => void;
  className?: string;
}

export const EncryptionToggle: React.FC<EncryptionToggleProps> = ({
  onEncryptionChange,
  className = '',
}) => {
  const {
    isEncryptionEnabled,
    isEncryptionSupported,
    encryptionKey,
    isGeneratingKey,
    error,
    toggleEncryption,
    generateNewKey,
    clearError,
  } = useEncryption();

  React.useEffect(() => {
    onEncryptionChange?.(isEncryptionEnabled);
  }, [isEncryptionEnabled, onEncryptionChange]);

  if (!isEncryptionSupported) {
    return (
      <Card className={`p-4 border-red-200 bg-red-50 ${className}`}>
        <div className="flex items-center gap-2 text-red-700">
          <ShieldX className="h-5 w-5" />
          <span className="font-medium">Encryption Not Supported</span>
        </div>
        <p className="text-sm text-red-600 mt-1">
          Your browser doesn't support the Web Crypto API required for client-side encryption.
        </p>
      </Card>
    );
  }

  return (
    <Card className={`p-4 ${className}`}>
      <div className="space-y-3">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {isEncryptionEnabled ? (
              <ShieldCheck className="h-5 w-5 text-green-600" />
            ) : (
              <Shield className="h-5 w-5 text-gray-500" />
            )}
            <span className="font-medium">
              Client-side Encryption
            </span>
          </div>
          
          <Button
            variant={isEncryptionEnabled ? "default" : "outline"}
            size="sm"
            onClick={toggleEncryption}
            disabled={isGeneratingKey}
          >
            {isEncryptionEnabled ? 'Enabled' : 'Enable'}
          </Button>
        </div>

        {/* Description */}
        <p className="text-sm text-gray-600">
          {isEncryptionEnabled
            ? 'Your paste will be encrypted in your browser before being sent to the server. Only users with the encryption key can read it.'
            : 'Enable zero-knowledge encryption to ensure your paste content is private and secure.'
          }
        </p>

        {/* Key Management */}
        {isEncryptionEnabled && (
          <div className="space-y-2">
            {!encryptionKey ? (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={generateNewKey}
                  disabled={isGeneratingKey}
                  className="flex items-center gap-2"
                >
                  <Key className="h-4 w-4" />
                  {isGeneratingKey ? 'Generating...' : 'Generate Key'}
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-sm text-green-700">
                <Key className="h-4 w-4" />
                <span>Encryption key ready</span>
              </div>
            )}

            {/* Security Notice */}
            <div className="flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-md">
              <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800">
                <p className="font-medium">Important Security Notice:</p>
                <ul className="mt-1 space-y-1 text-xs">
                  <li>• The encryption key will be included in the share URL</li>
                  <li>• Anyone with the full URL can decrypt your paste</li>
                  <li>• Share the URL only through secure channels</li>
                  <li>• The server cannot decrypt your content</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center gap-2 text-red-700">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="text-red-600 hover:text-red-700"
            >
              Dismiss
            </Button>
          </div>
        )}
      </div>
    </Card>
  );
};

export default EncryptionToggle;