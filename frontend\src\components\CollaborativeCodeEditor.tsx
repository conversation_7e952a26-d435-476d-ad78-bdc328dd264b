import React, { useEffect, useRef, useState, useCallback } from 'react';
import Editor from '@monaco-editor/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Edit3 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useCollaborativeEditor } from '@/hooks/useCollaborativeEditor';
import { PresenceIndicator, CollaboratorCursor, CollaboratorSelection } from './PresenceIndicator';


export interface CollaborativeCodeEditorProps {
  pasteId: string;
  initialContent: string;
  language?: string;
  readOnly?: boolean;
  height?: string;
  title?: string;
  onContentChange?: (content: string) => void;
}

export const CollaborativeCodeEditor: React.FC<CollaborativeCodeEditorProps> = ({
  pasteId,
  initialContent,
  language = 'text',
  readOnly = false,
  height = '400px',
  title,
  onContentChange
}) => {
  const [theme, setTheme] = useState<string>(() => {
    const savedTheme = localStorage.getItem('monaco-theme');
    if (savedTheme) return savedTheme;
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'vs-dark' : 'vs';
  });

  const editorRef = useRef<any>(null);
  const monacoRef = useRef<any>(null);
  const [isEditorReady, setIsEditorReady] = useState(false);
  const [currentUserId] = useState(() => `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  const { toast } = useToast();

  // Use collaborative editor hook
  const {
    content,
    version,
    collaborators,
    isReady: isCollaborativeReady,
    error,
    insertText,
    deleteText,
    updateCursor,
    updateSelection
  } = useCollaborativeEditor({
    pasteId,
    initialContent,
    enabled: !readOnly
  });

  // Notify parent of content changes
  useEffect(() => {
    if (onContentChange) {
      onContentChange(content);
    }
  }, [content, onContentChange]);

  // Show error toast
  useEffect(() => {
    if (error) {
      toast({
        title: "Collaboration Error",
        description: error,
        variant: "destructive",
      });
    }
  }, [error, toast]);

  const handleEditorDidMount = useCallback((editor: any, monaco: any) => {
    editorRef.current = editor;
    monacoRef.current = monaco;
    setIsEditorReady(true);

    // Configure editor for collaboration
    editor.updateOptions({
      fontSize: 14,
      lineNumbers: 'on',
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      automaticLayout: true,
      folding: true,
      bracketMatching: 'always',
      renderWhitespace: 'selection',
      smoothScrolling: true,
      cursorBlinking: 'smooth',
      cursorSmoothCaretAnimation: 'on',
      readOnly: readOnly || !isCollaborativeReady,
    });

    // Handle cursor position changes
    const cursorDisposable = editor.onDidChangeCursorPosition((e: any) => {
      if (!readOnly && isCollaborativeReady) {
        const position = e.position;
        const model = editor.getModel();
        if (model) {
          const offset = model.getOffsetAt(position);
          updateCursor(position.lineNumber - 1, position.column - 1, offset);
        }
      }
    });

    // Handle selection changes
    const selectionDisposable = editor.onDidChangeCursorSelection((e: any) => {
      if (!readOnly && isCollaborativeReady) {
        const selection = e.selection;
        const model = editor.getModel();
        if (model && !selection.isEmpty()) {
          const startOffset = model.getOffsetAt(selection.getStartPosition());
          const endOffset = model.getOffsetAt(selection.getEndPosition());
          
          updateSelection(
            selection.startLineNumber - 1,
            selection.startColumn - 1,
            startOffset,
            selection.endLineNumber - 1,
            selection.endColumn - 1,
            endOffset
          );
        }
      }
    });

    // Handle content changes
    const contentDisposable = editor.onDidChangeModelContent((e: any) => {
      if (!readOnly && isCollaborativeReady && e.changes.length > 0) {
        // Process each change and send as operations
        for (const change of e.changes) {
          const { range, text, rangeLength } = change;
          const model = editor.getModel();
          if (model) {
            const offset = model.getOffsetAt(range.getStartPosition());
            
            if (rangeLength > 0) {
              // This is a delete operation
              deleteText(offset, rangeLength);
            }
            
            if (text.length > 0) {
              // This is an insert operation
              insertText(offset, text);
            }
          }
        }
      }
    });

    return () => {
      cursorDisposable.dispose();
      selectionDisposable.dispose();
      contentDisposable.dispose();
    };
  }, [readOnly, isCollaborativeReady, updateCursor, updateSelection, insertText, deleteText]);

  // Update editor content when collaborative content changes
  useEffect(() => {
    if (isEditorReady && editorRef.current && content !== editorRef.current.getValue()) {
      const editor = editorRef.current;
      const currentPosition = editor.getPosition();
      
      // Update content without triggering change events
      editor.setValue(content);
      
      // Restore cursor position if possible
      if (currentPosition) {
        editor.setPosition(currentPosition);
      }
    }
  }, [content, isEditorReady]);

  // Render collaborator cursors and selections
  const renderCollaboratorDecorations = useCallback(() => {
    if (!isEditorReady || !editorRef.current || !monacoRef.current) {
      return;
    }

    const editor = editorRef.current;
    const monaco = monacoRef.current;
    const model = editor.getModel();
    
    if (!model) return;

    // Clear existing decorations
    const decorations: any[] = [];

    // Add decorations for each collaborator
    collaborators.forEach((collaborator) => {
      if (collaborator.user_id === currentUserId) return;

      // Add cursor decoration
      try {
        const cursorPosition = model.getPositionAt(collaborator.cursor.offset);
        decorations.push({
          range: new monaco.Range(
            cursorPosition.lineNumber,
            cursorPosition.column,
            cursorPosition.lineNumber,
            cursorPosition.column
          ),
          options: {
            className: 'collaborator-cursor',
            beforeContentClassName: 'collaborator-cursor-line',
            stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
            hoverMessage: { value: `${collaborator.username}'s cursor` }
          }
        });

        // Add selection decoration if exists
        if (collaborator.selection) {
          const startPos = model.getPositionAt(collaborator.selection.start.offset);
          const endPos = model.getPositionAt(collaborator.selection.end.offset);
          
          decorations.push({
            range: new monaco.Range(
              startPos.lineNumber,
              startPos.column,
              endPos.lineNumber,
              endPos.column
            ),
            options: {
              className: 'collaborator-selection',
              stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
              hoverMessage: { value: `${collaborator.username}'s selection` }
            }
          });
        }
      } catch (error) {
        console.warn('Error rendering collaborator decoration:', error);
      }
    });

    // Apply decorations
    editor.deltaDecorations([], decorations);
  }, [isEditorReady, collaborators, currentUserId]);

  // Update decorations when collaborators change
  useEffect(() => {
    renderCollaboratorDecorations();
  }, [renderCollaboratorDecorations]);

  // Add CSS for collaborator decorations
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .collaborator-cursor-line {
        border-left: 2px solid var(--collaborator-color, #007acc);
        position: absolute;
        height: 1.2em;
        z-index: 10;
      }
      
      .collaborator-selection {
        background-color: var(--collaborator-selection-color, rgba(0, 122, 204, 0.2));
        border: 1px solid var(--collaborator-color, #007acc);
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === 'vs-dark' ? 'vs' : 'vs-dark';
    setTheme(newTheme);
    localStorage.setItem('monaco-theme', newTheme);
  };

  const isConnected = isCollaborativeReady;
  const connectionState = isConnected ? 'OPEN' : 'CLOSED';

  return (
    <div className="w-full space-y-4">
      {/* Presence Indicator */}
      <PresenceIndicator
        isConnected={isConnected}
        connectionState={connectionState}
        collaborators={collaborators}
        currentUserId={currentUserId}
        isCollaborativeMode={true}
      />

      {/* Editor */}
      <Card className="w-full">
        {title && (
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <CardTitle className="text-lg">{title}</CardTitle>
                {isCollaborativeReady && (
                  <div className="flex items-center gap-2 text-sm text-purple-600">
                    <Edit3 className="h-4 w-4" />
                    <span>v{version}</span>
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleTheme}
                  title={`Switch to ${theme === 'vs-dark' ? 'light' : 'dark'} theme`}
                >
                  {theme === 'vs-dark' ? '☀️' : '🌙'}
                </Button>
              </div>
            </div>
          </CardHeader>
        )}
        
        <CardContent className="p-0">
          <div className="border rounded-b-lg overflow-hidden relative">
            <Editor
              height={height}
              language={language}
              value={content}
              theme={theme}
              onMount={handleEditorDidMount}
              options={{
                readOnly: readOnly || !isCollaborativeReady,
                selectOnLineNumbers: true,
                roundedSelection: false,
                scrollBeyondLastLine: false,
                automaticLayout: true,
              }}
            />
            
            {/* Collaborator cursors overlay */}
            <div className="absolute inset-0 pointer-events-none">
              {collaborators
                .filter(c => c.user_id !== currentUserId)
                .map((collaborator) => (
                  <React.Fragment key={collaborator.user_id}>
                    <CollaboratorCursor
                      collaborator={collaborator}
                      isVisible={isEditorReady}
                    />
                    <CollaboratorSelection
                      collaborator={collaborator}
                      isVisible={isEditorReady}
                    />
                  </React.Fragment>
                ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status */}
      {!isCollaborativeReady && !readOnly && (
        <div className="text-center text-sm text-muted-foreground">
          <div className="flex items-center justify-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            Connecting to collaborative session...
          </div>
        </div>
      )}
    </div>
  );
};