package websocket

import (
	"encoding/json"
	"log"
	"time"

	"enhanced-pastebin/internal/models"
)

// Hub wraps the models.Hub with additional functionality
type Hub struct {
	*models.Hub
}

// NewHub creates a new WebSocket hub
func NewHub() *Hub {
	return &Hub{
		Hub: &models.Hub{
			Rooms:         make(map[string]map[*models.Client]bool),
			Clients:       make(map[*models.Client]bool),
			Broadcast:     make(chan []byte),
			Register:      make(chan *models.Client),
			Unregister:    make(chan *models.Client),
			RoomBroadcast: make(chan models.RoomMessage),
		},
	}
}

// Run starts the hub and handles client registration/unregistration and message broadcasting
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.Register:
			h.registerClient(client)

		case client := <-h.Unregister:
			h.unregisterClient(client)

		case message := <-h.Broadcast:
			h.broadcastToAll(message)

		case roomMessage := <-h.RoomBroadcast:
			h.broadcastToRoom(roomMessage.Room, roomMessage.Message)
		}
	}
}

// registerClient registers a new client
func (h *Hub) registerClient(client *models.Client) {
	h.Clients[client] = true
	log.Printf("Client %s registered", client.ID)
}

// unregisterClient unregisters a client and removes them from all rooms
func (h *Hub) unregisterClient(client *models.Client) {
	if _, ok := h.Clients[client]; ok {
		// Remove client from all rooms
		for room := range client.Rooms {
			h.leaveRoom(client, room)
		}

		// Remove from global clients
		delete(h.Clients, client)
		close(client.Send)
		log.Printf("Client %s unregistered", client.ID)
	}
}

// JoinRoom adds a client to a specific room (paste)
func (h *Hub) JoinRoom(client *models.Client, room string) {
	// Initialize room if it doesn't exist
	if h.Rooms[room] == nil {
		h.Rooms[room] = make(map[*models.Client]bool)
	}

	// Add client to room
	h.Rooms[room][client] = true
	client.Rooms[room] = true

	log.Printf("Client %s joined room %s", client.ID, room)

	// Broadcast join message to room
	joinMessage := models.WebSocketMessage{
		Type:      models.MessageTypeJoin,
		PasteID:   room,
		UserID:    client.UserID,
		Username:  client.Username,
		Timestamp: time.Now(),
	}

	if messageBytes, err := json.Marshal(joinMessage); err == nil {
		h.broadcastToRoom(room, messageBytes)
	}

	// Send current presence to the joining client
	h.sendPresenceUpdate(room)
}

// LeaveRoom removes a client from a specific room
func (h *Hub) LeaveRoom(client *models.Client, room string) {
	h.leaveRoom(client, room)
	h.sendPresenceUpdate(room)
}

// leaveRoom internal method to remove client from room
func (h *Hub) leaveRoom(client *models.Client, room string) {
	if roomClients, exists := h.Rooms[room]; exists {
		if _, inRoom := roomClients[client]; inRoom {
			delete(roomClients, client)
			delete(client.Rooms, room)

			// Clean up empty room
			if len(roomClients) == 0 {
				delete(h.Rooms, room)
			}

			log.Printf("Client %s left room %s", client.ID, room)

			// Broadcast leave message to remaining clients in room
			leaveMessage := models.WebSocketMessage{
				Type:      models.MessageTypeLeave,
				PasteID:   room,
				UserID:    client.UserID,
				Username:  client.Username,
				Timestamp: time.Now(),
			}

			if messageBytes, err := json.Marshal(leaveMessage); err == nil {
				h.broadcastToRoom(room, messageBytes)
			}
		}
	}
}

// broadcastToAll sends a message to all connected clients
func (h *Hub) broadcastToAll(message []byte) {
	for client := range h.Clients {
		select {
		case client.Send <- message:
		default:
			close(client.Send)
			delete(h.Clients, client)
		}
	}
}

// broadcastToRoom sends a message to all clients in a specific room
func (h *Hub) broadcastToRoom(room string, message []byte) {
	if roomClients, exists := h.Rooms[room]; exists {
		for client := range roomClients {
			select {
			case client.Send <- message:
			default:
				close(client.Send)
				delete(h.Clients, client)
				delete(roomClients, client)
			}
		}
	}
}

// sendPresenceUpdate sends current presence information to all clients in a room
func (h *Hub) sendPresenceUpdate(room string) {
	if roomClients, exists := h.Rooms[room]; exists {
		var activeUsers []models.ActiveUser
		now := time.Now()

		for client := range roomClients {
			activeUsers = append(activeUsers, models.ActiveUser{
				UserID:    client.UserID,
				Username:  client.Username,
				JoinedAt:  now, // For now, we'll use current time
				LastSeen:  now,
			})
		}

		presenceData := models.PresenceData{
			ActiveUsers: activeUsers,
			UserCount:   len(activeUsers),
		}

		dataBytes, _ := json.Marshal(presenceData)
		presenceMessage := models.WebSocketMessage{
			Type:      models.MessageTypePresence,
			PasteID:   room,
			Data:      dataBytes,
			Timestamp: now,
		}

		if messageBytes, err := json.Marshal(presenceMessage); err == nil {
			h.broadcastToRoom(room, messageBytes)
		}
	}
}

// GetRoomClients returns the number of clients in a room
func (h *Hub) GetRoomClients(room string) int {
	if roomClients, exists := h.Rooms[room]; exists {
		return len(roomClients)
	}
	return 0
}

// BroadcastToRoom sends a message to a specific room
func (h *Hub) BroadcastToRoom(room string, message []byte) {
	select {
	case h.RoomBroadcast <- models.RoomMessage{Room: room, Message: message}:
	default:
		log.Printf("Failed to broadcast message to room %s", room)
	}
}