package repositories

import (
	"database/sql"
	"enhanced-pastebin/internal/models"

	"github.com/lib/pq"
)

type ChatRepository interface {
	Create(message *models.ChatMessage) error
	GetByPasteID(pasteID string, limit, offset int) ([]*models.ChatMessage, error)
	GetByID(id string) (*models.ChatMessage, error)
	Delete(id string) error
	DeleteByPasteID(pasteID string) error
	GetMessageCount(pasteID string) (int, error)
}

type chatRepository struct {
	db *sql.DB
}

func NewChatRepository(db *sql.DB) ChatRepository {
	return &chatRepository{db: db}
}

func (r *chatRepository) Create(message *models.ChatMessage) error {
	query := `
		INSERT INTO chat_messages (id, paste_id, user_id, content, line_references, created_at)
		VALUES ($1, $2, $3, $4, $5, $6)`
	
	_, err := r.db.Exec(query, message.ID, message.PasteID, message.UserID, 
		message.Content, pq.Array(message.LineReferences), message.CreatedAt)
	return err
}

func (r *chatRepository) GetByPasteID(pasteID string, limit, offset int) ([]*models.ChatMessage, error) {
	query := `
		SELECT cm.id, cm.paste_id, cm.user_id, cm.content, cm.line_references, cm.created_at, u.username
		FROM chat_messages cm
		JOIN users u ON cm.user_id = u.id
		WHERE cm.paste_id = $1 
		ORDER BY cm.created_at ASC 
		LIMIT $2 OFFSET $3`
	
	rows, err := r.db.Query(query, pasteID, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var messages []*models.ChatMessage
	for rows.Next() {
		message := &models.ChatMessage{}
		err := rows.Scan(
			&message.ID, &message.PasteID, &message.UserID, &message.Content,
			pq.Array(&message.LineReferences), &message.CreatedAt, &message.Username)
		if err != nil {
			return nil, err
		}
		messages = append(messages, message)
	}

	return messages, rows.Err()
}

func (r *chatRepository) GetByID(id string) (*models.ChatMessage, error) {
	message := &models.ChatMessage{}
	query := `
		SELECT cm.id, cm.paste_id, cm.user_id, cm.content, cm.line_references, cm.created_at, u.username
		FROM chat_messages cm
		JOIN users u ON cm.user_id = u.id
		WHERE cm.id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&message.ID, &message.PasteID, &message.UserID, &message.Content,
		pq.Array(&message.LineReferences), &message.CreatedAt, &message.Username)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return message, err
}

func (r *chatRepository) Delete(id string) error {
	query := `DELETE FROM chat_messages WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

func (r *chatRepository) GetMessageCount(pasteID string) (int, error) {
	var count int
	query := `SELECT COUNT(*) FROM chat_messages WHERE paste_id = $1`
	err := r.db.QueryRow(query, pasteID).Scan(&count)
	return count, err
}

func (r *chatRepository) DeleteByPasteID(pasteID string) error {
	query := `DELETE FROM chat_messages WHERE paste_id = $1`
	_, err := r.db.Exec(query, pasteID)
	return err
}