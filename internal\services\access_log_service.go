package services

import (
	"time"

	"enhanced-pastebin/internal/models"
	"enhanced-pastebin/internal/repositories"

	"github.com/google/uuid"
)

type AccessLogService interface {
	LogAccess(req *models.AccessLogRequest) error
	GetAccessLogs(pasteID string, limit, offset int) ([]*models.AccessLog, error)
	GetAccessLogsByUser(userID string, limit, offset int) ([]*models.AccessLog, error)
	GetAccessLogCount(pasteID string) (int, error)
	GetSecurityEvents(limit, offset int) ([]*models.AccessLog, error)
	LogSecurityEvent(event *models.SecurityEventRequest) error
}

type accessLogService struct {
	accessLogRepo repositories.AccessLogRepository
}

func NewAccessLogService(accessLogRepo repositories.AccessLogRepository) AccessLogService {
	return &accessLogService{
		accessLogRepo: accessLogRepo,
	}
}

func (s *accessLogService) LogAccess(req *models.AccessLogRequest) error {
	accessLog := &models.AccessLog{
		ID:        uuid.New().String(),
		PasteID:   req.PasteID,
		UserID:    req.UserID,
		IPAddress: req.IPAddress,
		UserAgent: req.UserAgent,
		Action:    req.Action,
		Details:   req.Details,
		Timestamp: time.Now(),
	}

	return s.accessLogRepo.Create(accessLog)
}

func (s *accessLogService) GetAccessLogs(pasteID string, limit, offset int) ([]*models.AccessLog, error) {
	return s.accessLogRepo.GetByPasteID(pasteID, limit, offset)
}

func (s *accessLogService) GetAccessLogsByUser(userID string, limit, offset int) ([]*models.AccessLog, error) {
	return s.accessLogRepo.GetByUserID(userID, limit, offset)
}

func (s *accessLogService) GetAccessLogCount(pasteID string) (int, error) {
	return s.accessLogRepo.GetCountByPasteID(pasteID)
}

func (s *accessLogService) GetSecurityEvents(limit, offset int) ([]*models.AccessLog, error) {
	return s.accessLogRepo.GetSecurityEvents(limit, offset)
}

func (s *accessLogService) LogSecurityEvent(event *models.SecurityEventRequest) error {
	accessLog := &models.AccessLog{
		ID:        uuid.New().String(),
		PasteID:   event.PasteID,
		UserID:    event.UserID,
		IPAddress: event.IPAddress,
		UserAgent: event.UserAgent,
		Action:    "security_event",
		Details:   event.Details,
		Timestamp: time.Now(),
	}

	return s.accessLogRepo.Create(accessLog)
}
