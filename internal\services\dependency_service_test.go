package services

import (
	"context"
	"testing"

	"enhanced-pastebin/internal/models"
)

// Mock security service for testing
type mockSecurityService struct{}

func (m *mockSecurityService) ScanDependencies(ctx context.Context, dependencies []models.Dependency) ([]models.Dependency, error) {
	// Add basic security info to dependencies for testing
	for i := range dependencies {
		dependencies[i].Security = &models.DependencySecurityInfo{
			PackageName:     dependencies[i].Name,
			Version:         dependencies[i].Version,
			Vulnerabilities: []models.SecurityVulnerability{},
			SecurityScore:   100,
			HasVulns:        false,
		}
	}
	return dependencies, nil
}

func (m *mockSecurityService) ScanNPMPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error) {
	return &models.DependencySecurityInfo{
		PackageName:     packageName,
		Version:         version,
		Vulnerabilities: []models.SecurityVulnerability{},
		SecurityScore:   100,
		HasVulns:        false,
	}, nil
}

func (m *mockSecurityService) ScanPyPIPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error) {
	return &models.DependencySecurityInfo{
		PackageName:     packageName,
		Version:         version,
		Vulnerabilities: []models.SecurityVulnerability{},
		SecurityScore:   100,
		HasVulns:        false,
	}, nil
}

func (m *mockSecurityService) ScanGoPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error) {
	return &models.DependencySecurityInfo{
		PackageName:     packageName,
		Version:         version,
		Vulnerabilities: []models.SecurityVulnerability{},
		SecurityScore:   100,
		HasVulns:        false,
	}, nil
}

func (m *mockSecurityService) CalculateSecurityScore(vulnerabilities []models.SecurityVulnerability) int {
	return 100
}

func TestDependencyService_ExtractJavaScriptDependencies(t *testing.T) {
	mockSecurity := &mockSecurityService{}
	service := NewDependencyService(mockSecurity)

	tests := []struct {
		name     string
		content  string
		language string
		expected []models.Dependency
	}{
		{
			name:     "ES6 imports",
			content:  `import React from 'react';\nimport { useState } from 'react';\nimport axios from 'axios';`,
			language: "javascript",
			expected: []models.Dependency{
				{
					Name:        "react",
					Type:        "import",
					Language:    "javascript",
					Registry:    "npm",
					RegistryURL: "https://www.npmjs.com/package/react",
					InstallCmd:  "npm install react",
				},
				{
					Name:        "axios",
					Type:        "import",
					Language:    "javascript",
					Registry:    "npm",
					RegistryURL: "https://www.npmjs.com/package/axios",
					InstallCmd:  "npm install axios",
				},
			},
		},
		{
			name:     "CommonJS requires",
			content:  `const express = require('express');\nconst fs = require('fs');\nconst lodash = require('lodash');`,
			language: "javascript",
			expected: []models.Dependency{
				{
					Name:        "express",
					Type:        "require",
					Language:    "javascript",
					Registry:    "npm",
					RegistryURL: "https://www.npmjs.com/package/express",
					InstallCmd:  "npm install express",
				},
				{
					Name:        "fs",
					Type:        "require",
					Language:    "javascript",
					Registry:    "npm",
					RegistryURL: "https://www.npmjs.com/package/fs",
					InstallCmd:  "npm install fs",
				},
				{
					Name:        "lodash",
					Type:        "require",
					Language:    "javascript",
					Registry:    "npm",
					RegistryURL: "https://www.npmjs.com/package/lodash",
					InstallCmd:  "npm install lodash",
				},
			},
		},
		{
			name:     "Mixed imports with relative paths",
			content:  `import React from 'react';\nimport './styles.css';\nimport utils from '../utils';\nimport axios from 'axios';`,
			language: "javascript",
			expected: []models.Dependency{
				{
					Name:        "react",
					Type:        "import",
					Language:    "javascript",
					Registry:    "npm",
					RegistryURL: "https://www.npmjs.com/package/react",
					InstallCmd:  "npm install react",
				},
				{
					Name:        "axios",
					Type:        "import",
					Language:    "javascript",
					Registry:    "npm",
					RegistryURL: "https://www.npmjs.com/package/axios",
					InstallCmd:  "npm install axios",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.ExtractDependencies(tt.content, tt.language)
			if err != nil {
				t.Errorf("ExtractDependencies() error = %v", err)
				return
			}

			if len(result.Dependencies) != len(tt.expected) {
				t.Errorf("ExtractDependencies() got %d dependencies, want %d", len(result.Dependencies), len(tt.expected))
				return
			}

			for i, dep := range result.Dependencies {
				if dep.Name != tt.expected[i].Name {
					t.Errorf("ExtractDependencies() dependency[%d].Name = %v, want %v", i, dep.Name, tt.expected[i].Name)
				}
				if dep.Type != tt.expected[i].Type {
					t.Errorf("ExtractDependencies() dependency[%d].Type = %v, want %v", i, dep.Type, tt.expected[i].Type)
				}
				if dep.Registry != tt.expected[i].Registry {
					t.Errorf("ExtractDependencies() dependency[%d].Registry = %v, want %v", i, dep.Registry, tt.expected[i].Registry)
				}
			}
		})
	}
}

func TestDependencyService_ExtractPythonDependencies(t *testing.T) {
	mockSecurity := &mockSecurityService{}
	service := NewDependencyService(mockSecurity)

	tests := []struct {
		name     string
		content  string
		language string
		expected []models.Dependency
	}{
		{
			name:     "Python imports",
			content:  `import requests\nimport numpy as np\nfrom flask import Flask\nimport os\nimport sys`,
			language: "python",
			expected: []models.Dependency{
				{
					Name:        "requests",
					Type:        "import",
					Language:    "python",
					Registry:    "pypi",
					RegistryURL: "https://pypi.org/project/requests/",
					InstallCmd:  "pip install requests",
				},
				{
					Name:        "numpy",
					Type:        "import",
					Language:    "python",
					Registry:    "pypi",
					RegistryURL: "https://pypi.org/project/numpy/",
					InstallCmd:  "pip install numpy",
				},
				{
					Name:        "flask",
					Type:        "import",
					Language:    "python",
					Registry:    "pypi",
					RegistryURL: "https://pypi.org/project/flask/",
					InstallCmd:  "pip install flask",
				},
			},
		},
		{
			name:     "Python from imports",
			content:  `from django.http import HttpResponse\nfrom pandas import DataFrame\nfrom datetime import datetime`,
			language: "python",
			expected: []models.Dependency{
				{
					Name:        "django",
					Type:        "import",
					Language:    "python",
					Registry:    "pypi",
					RegistryURL: "https://pypi.org/project/django/",
					InstallCmd:  "pip install django",
				},
				{
					Name:        "pandas",
					Type:        "import",
					Language:    "python",
					Registry:    "pypi",
					RegistryURL: "https://pypi.org/project/pandas/",
					InstallCmd:  "pip install pandas",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.ExtractDependencies(tt.content, tt.language)
			if err != nil {
				t.Errorf("ExtractDependencies() error = %v", err)
				return
			}

			if len(result.Dependencies) != len(tt.expected) {
				t.Errorf("ExtractDependencies() got %d dependencies, want %d", len(result.Dependencies), len(tt.expected))
				return
			}

			for i, dep := range result.Dependencies {
				if dep.Name != tt.expected[i].Name {
					t.Errorf("ExtractDependencies() dependency[%d].Name = %v, want %v", i, dep.Name, tt.expected[i].Name)
				}
			}
		})
	}
}

func TestDependencyService_ExtractGoDependencies(t *testing.T) {
	mockSecurity := &mockSecurityService{}
	service := NewDependencyService(mockSecurity)

	tests := []struct {
		name     string
		content  string
		language string
		expected []models.Dependency
	}{
		{
			name: "Go imports",
			content: `package main

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/lib/pq"
	"gorm.io/gorm"
)`,
			language: "go",
			expected: []models.Dependency{
				{
					Name:        "github.com/gin-gonic/gin",
					Type:        "import",
					Language:    "go",
					Registry:    "go",
					RegistryURL: "https://pkg.go.dev/github.com/gin-gonic/gin",
					InstallCmd:  "go get github.com/gin-gonic/gin",
				},
				{
					Name:        "github.com/lib/pq",
					Type:        "import",
					Language:    "go",
					Registry:    "go",
					RegistryURL: "https://pkg.go.dev/github.com/lib/pq",
					InstallCmd:  "go get github.com/lib/pq",
				},
				{
					Name:        "gorm.io/gorm",
					Type:        "import",
					Language:    "go",
					Registry:    "go",
					RegistryURL: "https://pkg.go.dev/gorm.io/gorm",
					InstallCmd:  "go get gorm.io/gorm",
				},
			},
		},
		{
			name:     "Single line Go import",
			content:  `import "github.com/gorilla/mux"`,
			language: "go",
			expected: []models.Dependency{
				{
					Name:        "github.com/gorilla/mux",
					Type:        "import",
					Language:    "go",
					Registry:    "go",
					RegistryURL: "https://pkg.go.dev/github.com/gorilla/mux",
					InstallCmd:  "go get github.com/gorilla/mux",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.ExtractDependencies(tt.content, tt.language)
			if err != nil {
				t.Errorf("ExtractDependencies() error = %v", err)
				return
			}

			if len(result.Dependencies) != len(tt.expected) {
				t.Errorf("ExtractDependencies() got %d dependencies, want %d", len(result.Dependencies), len(tt.expected))
				return
			}

			for i, dep := range result.Dependencies {
				if dep.Name != tt.expected[i].Name {
					t.Errorf("ExtractDependencies() dependency[%d].Name = %v, want %v", i, dep.Name, tt.expected[i].Name)
				}
			}
		})
	}
}

func TestDependencyService_UnsupportedLanguage(t *testing.T) {
	mockSecurity := &mockSecurityService{}
	service := NewDependencyService(mockSecurity)

	result, err := service.ExtractDependencies("some code", "unsupported")
	if err != nil {
		t.Errorf("ExtractDependencies() error = %v", err)
		return
	}

	if len(result.Dependencies) != 0 {
		t.Errorf("ExtractDependencies() got %d dependencies, want 0", len(result.Dependencies))
	}

	if result.Language != "unsupported" {
		t.Errorf("ExtractDependencies() language = %v, want unsupported", result.Language)
	}
}

func TestDependencyService_ExtractDependenciesWithSecurity(t *testing.T) {
	mockSecurity := &mockSecurityService{}
	service := NewDependencyService(mockSecurity)

	content := `import React from 'react';
import axios from 'axios';`

	result, err := service.ExtractDependenciesWithSecurity(context.Background(), content, "javascript")
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if result.TotalCount != 2 {
		t.Errorf("Expected 2 dependencies, got %d", result.TotalCount)
	}

	if result.SecurityScore != 100 {
		t.Errorf("Expected security score 100, got %d", result.SecurityScore)
	}

	if result.VulnCount != 0 {
		t.Errorf("Expected 0 vulnerabilities, got %d", result.VulnCount)
	}

	// Check that dependencies have security info
	for _, dep := range result.Dependencies {
		if dep.Security == nil {
			t.Errorf("Expected dependency %s to have security info", dep.Name)
		}
	}
}

// Mock security service with vulnerabilities for testing
type mockSecurityServiceWithVulns struct{}

func (m *mockSecurityServiceWithVulns) ScanDependencies(ctx context.Context, dependencies []models.Dependency) ([]models.Dependency, error) {
	for i := range dependencies {
		dependencies[i].Security = &models.DependencySecurityInfo{
			PackageName: dependencies[i].Name,
			Version:     dependencies[i].Version,
			Vulnerabilities: []models.SecurityVulnerability{
				{
					ID:       "test-vuln-1",
					Title:    "Test vulnerability",
					Severity: "high",
				},
			},
			SecurityScore: 60,
			HasVulns:      true,
		}
	}
	return dependencies, nil
}

func (m *mockSecurityServiceWithVulns) ScanNPMPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error) {
	return &models.DependencySecurityInfo{
		PackageName: packageName,
		Version:     version,
		Vulnerabilities: []models.SecurityVulnerability{
			{
				ID:       "test-vuln-1",
				Title:    "Test vulnerability",
				Severity: "high",
			},
		},
		SecurityScore: 60,
		HasVulns:      true,
	}, nil
}

func (m *mockSecurityServiceWithVulns) ScanPyPIPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error) {
	return m.ScanNPMPackage(ctx, packageName, version)
}

func (m *mockSecurityServiceWithVulns) ScanGoPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error) {
	return m.ScanNPMPackage(ctx, packageName, version)
}

func (m *mockSecurityServiceWithVulns) CalculateSecurityScore(vulnerabilities []models.SecurityVulnerability) int {
	return 60
}

func TestDependencyService_ExtractDependenciesWithSecurityVulns(t *testing.T) {
	mockSecurity := &mockSecurityServiceWithVulns{}
	service := NewDependencyService(mockSecurity)

	content := `import React from 'react';
import axios from 'axios';`

	result, err := service.ExtractDependenciesWithSecurity(context.Background(), content, "javascript")
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if result.TotalCount != 2 {
		t.Errorf("Expected 2 dependencies, got %d", result.TotalCount)
	}

	if result.VulnCount != 2 {
		t.Errorf("Expected 2 vulnerable dependencies, got %d", result.VulnCount)
	}

	if result.SecurityScore != 60 {
		t.Errorf("Expected security score 60, got %d", result.SecurityScore)
	}

	// Check that dependencies have security vulnerabilities
	for _, dep := range result.Dependencies {
		if dep.Security == nil {
			t.Errorf("Expected dependency %s to have security info", dep.Name)
			continue
		}
		if !dep.Security.HasVulns {
			t.Errorf("Expected dependency %s to have vulnerabilities", dep.Name)
		}
		if len(dep.Security.Vulnerabilities) == 0 {
			t.Errorf("Expected dependency %s to have vulnerability details", dep.Name)
		}
	}
}