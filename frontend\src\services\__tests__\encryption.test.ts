import { describe, it, expect, beforeEach, vi } from 'vitest'
import EncryptionService, { EncryptedData } from '../encryption'

// Mock Web Crypto API
const mockCrypto = {
  subtle: {
    generateKey: vi.fn(),
    exportKey: vi.fn(),
    importKey: vi.fn(),
    encrypt: vi.fn(),
    decrypt: vi.fn(),
    deriveKey: vi.fn(),
  },
  getRandomValues: vi.fn(),
}

// Mock global crypto
Object.defineProperty(global, 'crypto', {
  value: mockCrypto,
  writable: true,
})

// Mock btoa and atob
global.btoa = vi.fn((str: string) => Buffer.from(str, 'binary').toString('base64'))
global.atob = vi.fn((str: string) => Buffer.from(str, 'base64').toString('binary'))

// Mock TextEncoder and TextDecoder
global.TextEncoder = vi.fn(() => ({
  encode: vi.fn((str: string) => new Uint8Array(Buffer.from(str, 'utf8'))),
})) as any

global.TextDecoder = vi.fn(() => ({
  decode: vi.fn((buffer: ArrayBuffer) => Buffer.from(buffer).toString('utf8')),
})) as any

describe('EncryptionService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup default mock implementations
    mockCrypto.getRandomValues.mockImplementation((array: Uint8Array) => {
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256)
      }
      return array
    })
  })

  describe('isSupported', () => {
    it('should return true when Web Crypto API is available', () => {
      expect(EncryptionService.isSupported()).toBe(true)
    })

    it('should return false when crypto is undefined', () => {
      const originalCrypto = global.crypto
      // @ts-ignore
      global.crypto = undefined
      
      expect(EncryptionService.isSupported()).toBe(false)
      
      global.crypto = originalCrypto
    })
  })

  describe('generateKey', () => {
    it('should generate a new encryption key', async () => {
      const mockKey = { type: 'secret' } as CryptoKey
      const mockExportedKey = 'exported-key-data'
      
      mockCrypto.subtle.generateKey.mockResolvedValue(mockKey)
      mockCrypto.subtle.exportKey.mockResolvedValue(new ArrayBuffer(32))
      
      const result = await EncryptionService.generateKey()
      
      expect(mockCrypto.subtle.generateKey).toHaveBeenCalledWith(
        {
          name: 'AES-GCM',
          length: 256,
        },
        true,
        ['encrypt', 'decrypt']
      )
      
      expect(result.key).toBe(mockKey)
      expect(typeof result.exportedKey).toBe('string')
    })
  })

  describe('exportKey', () => {
    it('should export a CryptoKey to base64 string', async () => {
      const mockKey = { type: 'secret' } as CryptoKey
      const mockArrayBuffer = new ArrayBuffer(32)
      
      mockCrypto.subtle.exportKey.mockResolvedValue(mockArrayBuffer)
      
      const result = await EncryptionService.exportKey(mockKey)
      
      expect(mockCrypto.subtle.exportKey).toHaveBeenCalledWith('raw', mockKey)
      expect(typeof result).toBe('string')
    })
  })

  describe('importKey', () => {
    it('should import a key from base64 string', async () => {
      const mockKey = { type: 'secret' } as CryptoKey
      const keyData = 'base64-key-data'
      
      mockCrypto.subtle.importKey.mockResolvedValue(mockKey)
      
      const result = await EncryptionService.importKey(keyData)
      
      expect(mockCrypto.subtle.importKey).toHaveBeenCalledWith(
        'raw',
        expect.any(ArrayBuffer),
        {
          name: 'AES-GCM',
          length: 256,
        },
        true,
        ['encrypt', 'decrypt']
      )
      
      expect(result).toBe(mockKey)
    })
  })

  describe('encrypt', () => {
    it('should encrypt content using AES-GCM', async () => {
      const content = 'Hello, World!'
      const mockKey = { type: 'secret' } as CryptoKey
      const mockEncryptedBuffer = new ArrayBuffer(16)
      
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer)
      
      const result = await EncryptionService.encrypt(content, mockKey)
      
      expect(mockCrypto.subtle.encrypt).toHaveBeenCalledWith(
        {
          name: 'AES-GCM',
          iv: expect.any(Uint8Array),
        },
        mockKey,
        expect.any(Uint8Array)
      )
      
      expect(result).toEqual({
        encryptedContent: expect.any(String),
        iv: expect.any(String),
        salt: expect.any(String),
      })
    })
  })

  describe('decrypt', () => {
    it('should decrypt content using AES-GCM', async () => {
      const encryptedData: EncryptedData = {
        encryptedContent: 'encrypted-content',
        iv: 'initialization-vector',
        salt: 'salt-value',
      }
      const mockKey = { type: 'secret' } as CryptoKey
      const originalContent = 'Hello, World!'
      const mockDecryptedBuffer = new TextEncoder().encode(originalContent)
      
      mockCrypto.subtle.decrypt.mockResolvedValue(mockDecryptedBuffer.buffer)
      
      const result = await EncryptionService.decrypt(encryptedData, mockKey)
      
      expect(mockCrypto.subtle.decrypt).toHaveBeenCalledWith(
        {
          name: 'AES-GCM',
          iv: expect.any(ArrayBuffer),
        },
        mockKey,
        expect.any(ArrayBuffer)
      )
      
      expect(result).toBe(originalContent)
    })

    it('should throw error when decryption fails', async () => {
      const encryptedData: EncryptedData = {
        encryptedContent: 'invalid-content',
        iv: 'invalid-iv',
        salt: 'invalid-salt',
      }
      const mockKey = { type: 'secret' } as CryptoKey
      
      mockCrypto.subtle.decrypt.mockRejectedValue(new Error('Decryption failed'))
      
      await expect(EncryptionService.decrypt(encryptedData, mockKey))
        .rejects.toThrow('Failed to decrypt content. Invalid key or corrupted data.')
    })
  })

  describe('URL key management', () => {
    beforeEach(() => {
      // Mock window.location
      Object.defineProperty(window, 'location', {
        value: {
          hash: '',
          href: 'http://localhost:3000/paste/123',
          pathname: '/paste/123',
          search: '',
        },
        writable: true,
      })
    })

    it('should extract key from URL fragment', () => {
      window.location.hash = '#key=test-encryption-key'
      
      const result = EncryptionService.extractKeyFromUrl()
      
      expect(result).toBe('test-encryption-key')
    })

    it('should return null when no key in URL', () => {
      window.location.hash = ''
      
      const result = EncryptionService.extractKeyFromUrl()
      
      expect(result).toBeNull()
    })

    it('should add key to URL fragment', () => {
      const key = 'test-encryption-key'
      
      const result = EncryptionService.addKeyToUrl(key)
      
      expect(result).toBe('http://localhost:3000/paste/123#key=test-encryption-key')
    })

    it('should remove key from URL fragment', () => {
      window.location.hash = '#key=test-encryption-key'
      
      // Mock history.replaceState
      const mockReplaceState = vi.fn()
      Object.defineProperty(window, 'history', {
        value: { replaceState: mockReplaceState },
        writable: true,
      })
      
      EncryptionService.removeKeyFromUrl()
      
      expect(mockReplaceState).toHaveBeenCalledWith(
        null,
        '',
        '/paste/123'
      )
    })
  })

  describe('deriveKeyFromPassword', () => {
    it('should derive key from password using PBKDF2', async () => {
      const password = 'test-password'
      const mockKeyMaterial = { type: 'secret' } as CryptoKey
      const mockDerivedKey = { type: 'secret' } as CryptoKey
      
      mockCrypto.subtle.importKey.mockResolvedValue(mockKeyMaterial)
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockDerivedKey)
      mockCrypto.subtle.exportKey.mockResolvedValue(new ArrayBuffer(32))
      
      const result = await EncryptionService.deriveKeyFromPassword(password)
      
      expect(mockCrypto.subtle.importKey).toHaveBeenCalledWith(
        'raw',
        expect.any(Uint8Array),
        'PBKDF2',
        false,
        ['deriveKey']
      )
      
      expect(mockCrypto.subtle.deriveKey).toHaveBeenCalledWith(
        {
          name: 'PBKDF2',
          salt: expect.any(Uint8Array),
          iterations: 100000,
          hash: 'SHA-256',
        },
        mockKeyMaterial,
        {
          name: 'AES-GCM',
          length: 256,
        },
        true,
        ['encrypt', 'decrypt']
      )
      
      expect(result.key).toBe(mockDerivedKey)
      expect(typeof result.exportedKey).toBe('string')
    })
  })
})