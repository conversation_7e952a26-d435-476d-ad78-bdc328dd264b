import { Routes, Route } from 'react-router-dom'
import { AuthProvider } from '@/contexts/AuthContext'
import { FormattingProvider } from '@/contexts/FormattingContext'
import { EnhancedToaster } from '@/components/ui/enhanced-toast'
import { SkipLink } from '@/components/ui/focus-trap'
import ErrorBoundary from '@/components/ErrorBoundary'
import Layout from '@/components/Layout'
import Home from '@/pages/Home'
import Login from '@/pages/Login'
import Register from '@/pages/Register'
import CreatePaste from '@/pages/CreatePaste'
import ViewPaste from '@/pages/ViewPaste'
import EditPaste from '@/pages/EditPaste'
import Profile from '@/pages/Profile'
import PublicProfile from '@/pages/PublicProfile'
import UserPastes from '@/pages/UserPastes'

function App() {
  return (
    <ErrorBoundary>
      <SkipLink href="#main-content">Skip to main content</SkipLink>
      <AuthProvider>
        <FormattingProvider>
          <Layout>
            <main id="main-content" tabIndex={-1}>
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/create" element={<CreatePaste />} />
                <Route path="/paste/:id" element={<ViewPaste />} />
                <Route path="/paste/:id/edit" element={<EditPaste />} />
                <Route path="/p/:customUrl" element={<ViewPaste />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/profile/pastes" element={<UserPastes />} />
                <Route path="/users/:username" element={<PublicProfile />} />
              </Routes>
            </main>
          </Layout>
          <EnhancedToaster />
        </FormattingProvider>
      </AuthProvider>
    </ErrorBoundary>
  )
}

export default App