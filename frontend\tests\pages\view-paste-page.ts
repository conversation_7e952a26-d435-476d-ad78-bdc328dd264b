import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base-page';

export class ViewPastePage extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  // Page-specific locators
  get pasteTitle(): Locator {
    return this.page.locator('h1, [data-testid="paste-title"]').first();
  }

  get pasteContent(): Locator {
    return this.page.locator('.monaco-editor, pre, code, [data-testid="paste-content"]').first();
  }

  get pasteMetadata(): Locator {
    return this.page.locator('.paste-metadata, [data-testid="paste-metadata"]').first();
  }

  get authorInfo(): Locator {
    return this.page.locator('.author-info, [data-testid="author"]').first();
  }

  get createdDate(): Locator {
    return this.page.locator('.created-date, [data-testid="created-date"]').first();
  }

  get language(): Locator {
    return this.page.locator('.language, [data-testid="language"]').first();
  }

  get viewCount(): Locator {
    return this.page.locator('.view-count, [data-testid="view-count"]').first();
  }

  get editButton(): Locator {
    return this.page.locator('button:has-text("Edit"), a:has-text("Edit")').first();
  }

  get deleteButton(): Locator {
    return this.page.locator('button:has-text("Delete")').first();
  }

  get copyButton(): Locator {
    return this.page.locator('button:has-text("Copy")').first();
  }

  get downloadButton(): Locator {
    return this.page.locator('button:has-text("Download")').first();
  }

  get shareButton(): Locator {
    return this.page.locator('button:has-text("Share")').first();
  }

  get rawViewButton(): Locator {
    return this.page.locator('button:has-text("Raw"), a:has-text("Raw")').first();
  }

  get forkButton(): Locator {
    return this.page.locator('button:has-text("Fork")').first();
  }

  get likeButton(): Locator {
    return this.page.locator('button:has-text("Like"), .like-button').first();
  }

  get likeCount(): Locator {
    return this.page.locator('.like-count, [data-testid="like-count"]').first();
  }

  get commentsSection(): Locator {
    return this.page.locator('.comments, [data-testid="comments"]').first();
  }

  get commentInput(): Locator {
    return this.page.locator('textarea[name="comment"], input[name="comment"]').first();
  }

  get commentSubmitButton(): Locator {
    return this.page.locator('button:has-text("Comment"), button:has-text("Add Comment")').first();
  }

  get comments(): Locator {
    return this.page.locator('.comment, [data-testid="comment"]');
  }

  get versionHistory(): Locator {
    return this.page.locator('.version-history, [data-testid="version-history"]').first();
  }

  get versionHistoryButton(): Locator {
    return this.page.locator('button:has-text("History"), button:has-text("Versions")').first();
  }

  get collaborationPanel(): Locator {
    return this.page.locator('.collaboration, [data-testid="collaboration"]').first();
  }

  get chatPanel(): Locator {
    return this.page.locator('.chat, [data-testid="chat"]').first();
  }

  get expirationWarning(): Locator {
    return this.page.locator('.expiration-warning, [data-testid="expiration-warning"]').first();
  }

  get deleteConfirmDialog(): Locator {
    return this.page.locator('.delete-confirm, [data-testid="delete-confirm"]').first();
  }

  get confirmDeleteButton(): Locator {
    return this.page.locator('button:has-text("Confirm"), button:has-text("Yes")').first();
  }

  get cancelDeleteButton(): Locator {
    return this.page.locator('button:has-text("Cancel"), button:has-text("No")').first();
  }

  // Page actions
  async visit(pasteId: string): Promise<void> {
    await this.page.goto(`/paste/${pasteId}`);
    await this.waitForPageLoad();
  }

  async visitByCustomUrl(customUrl: string): Promise<void> {
    await this.page.goto(`/p/${customUrl}`);
    await this.waitForPageLoad();
  }

  async editPaste(): Promise<void> {
    await this.editButton.click();
    await this.page.waitForURL(/\/paste\/\w+\/edit/);
  }

  async deletePaste(): Promise<void> {
    await this.deleteButton.click();
    
    // Handle confirmation dialog if it appears
    if (await this.deleteConfirmDialog.isVisible()) {
      await this.confirmDeleteButton.click();
    }
    
    // Wait for redirect after deletion
    await this.page.waitForURL(/\/(|profile|dashboard)/);
  }

  async copyPasteContent(): Promise<void> {
    await this.copyButton.click();
    await this.expectSuccessMessage(/copied/i);
  }

  async downloadPaste(): Promise<void> {
    const downloadPromise = this.page.waitForEvent('download');
    await this.downloadButton.click();
    const download = await downloadPromise;
    return download;
  }

  async sharePaste(): Promise<void> {
    await this.shareButton.click();
    // Handle share dialog/modal
  }

  async viewRaw(): Promise<void> {
    await this.rawViewButton.click();
    // Should open raw view in new tab or navigate to raw URL
  }

  async forkPaste(): Promise<void> {
    await this.forkButton.click();
    await this.page.waitForURL(/\/create/);
  }

  async likePaste(): Promise<void> {
    const initialCount = await this.getLikeCount();
    await this.likeButton.click();
    
    // Wait for like count to update
    await expect(this.likeCount).not.toContainText(initialCount.toString());
  }

  async addComment(comment: string): Promise<void> {
    await this.commentInput.fill(comment);
    await this.commentSubmitButton.click();
    
    // Wait for comment to appear
    await this.page.waitForSelector(`.comment:has-text("${comment}")`);
  }

  async viewVersionHistory(): Promise<void> {
    await this.versionHistoryButton.click();
    await expect(this.versionHistory).toBeVisible();
  }

  // Page assertions
  async expectToBeOnPastePage(pasteId?: string): Promise<void> {
    if (pasteId) {
      await expect(this.page).toHaveURL(new RegExp(`/paste/${pasteId}`));
    } else {
      await expect(this.page).toHaveURL(/\/paste\/\w+/);
    }
  }

  async expectPasteTitle(title: string): Promise<void> {
    await expect(this.pasteTitle).toContainText(title);
  }

  async expectPasteContent(content: string): Promise<void> {
    await expect(this.pasteContent).toContainText(content);
  }

  async expectPasteLanguage(language: string): Promise<void> {
    await expect(this.language).toContainText(language);
  }

  async expectPasteAuthor(author: string): Promise<void> {
    await expect(this.authorInfo).toContainText(author);
  }

  async expectPasteMetadata(): Promise<void> {
    await expect(this.pasteMetadata).toBeVisible();
    await expect(this.createdDate).toBeVisible();
    await expect(this.language).toBeVisible();
  }

  async expectEditButtonVisible(): Promise<void> {
    await expect(this.editButton).toBeVisible();
  }

  async expectEditButtonNotVisible(): Promise<void> {
    await expect(this.editButton).not.toBeVisible();
  }

  async expectDeleteButtonVisible(): Promise<void> {
    await expect(this.deleteButton).toBeVisible();
  }

  async expectDeleteButtonNotVisible(): Promise<void> {
    await expect(this.deleteButton).not.toBeVisible();
  }

  async expectActionButtons(): Promise<void> {
    await expect(this.copyButton).toBeVisible();
    await expect(this.downloadButton).toBeVisible();
    await expect(this.shareButton).toBeVisible();
  }

  async expectLikeCount(count: number): Promise<void> {
    await expect(this.likeCount).toContainText(count.toString());
  }

  async expectCommentsSection(): Promise<void> {
    await expect(this.commentsSection).toBeVisible();
  }

  async expectCommentCount(count: number): Promise<void> {
    await expect(this.comments).toHaveCount(count);
  }

  async expectCommentInList(comment: string): Promise<void> {
    const commentElement = this.comments.filter({ hasText: comment });
    await expect(commentElement).toBeVisible();
  }

  async expectExpirationWarning(): Promise<void> {
    await expect(this.expirationWarning).toBeVisible();
  }

  async expectPasteNotFound(): Promise<void> {
    await expect(this.page.locator('h1')).toContainText(/not found|404/i);
  }

  async expectPastePrivate(): Promise<void> {
    await expect(this.page.locator('.private-paste, [data-testid="private"]')).toBeVisible();
  }

  async expectPasteExpired(): Promise<void> {
    await expect(this.page.locator('.expired-paste, [data-testid="expired"]')).toBeVisible();
  }

  // Utility methods
  async getPasteId(): Promise<string> {
    const url = this.page.url();
    const match = url.match(/\/paste\/(\w+)/);
    return match ? match[1] : '';
  }

  async getPasteTitle(): Promise<string> {
    return await this.pasteTitle.textContent() || '';
  }

  async getPasteContent(): Promise<string> {
    return await this.pasteContent.textContent() || '';
  }

  async getPasteLanguage(): Promise<string> {
    return await this.language.textContent() || '';
  }

  async getPasteAuthor(): Promise<string> {
    return await this.authorInfo.textContent() || '';
  }

  async getLikeCount(): Promise<number> {
    const text = await this.likeCount.textContent() || '0';
    return parseInt(text.replace(/\D/g, ''), 10);
  }

  async getCommentCount(): Promise<number> {
    return await this.comments.count();
  }

  async getViewCount(): Promise<number> {
    const text = await this.viewCount.textContent() || '0';
    return parseInt(text.replace(/\D/g, ''), 10);
  }

  async isOwner(): Promise<boolean> {
    return await this.editButton.isVisible() && await this.deleteButton.isVisible();
  }

  async isPublic(): Promise<boolean> {
    return !await this.page.locator('.private-indicator').isVisible();
  }

  async waitForPasteLoad(): Promise<void> {
    await Promise.race([
      this.pasteContent.waitFor(),
      this.page.locator('.not-found, .error').waitFor()
    ]);
  }
}
