import { TestPaste } from '../utils/api-client';

export const pasteFixtures = {
  // Basic pastes for different languages
  javascript: {
    title: 'JavaScript Example',
    content: `
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibon<PERSON><PERSON>(n - 2);
}

console.log(fibon<PERSON><PERSON>(10));
    `.trim(),
    language: 'javascript',
    is_public: true
  },

  python: {
    title: 'Python Example',
    content: `
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

print(fibon<PERSON>ci(10))
    `.trim(),
    language: 'python',
    is_public: true
  },

  html: {
    title: 'HTML Example',
    content: `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
</head>
<body>
    <h1>Hello, World!</h1>
    <p>This is a test HTML page.</p>
</body>
</html>
    `.trim(),
    language: 'html',
    is_public: true
  },

  css: {
    title: 'CSS Example',
    content: `
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  background-color: #333;
  color: white;
  padding: 1rem;
  text-align: center;
}

.button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

.button:hover {
  background-color: #0056b3;
}
    `.trim(),
    language: 'css',
    is_public: true
  },

  json: {
    title: 'JSON Configuration',
    content: `
{
  "name": "test-project",
  "version": "1.0.0",
  "description": "A test project configuration",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "test": "jest",
    "build": "webpack --mode production"
  },
  "dependencies": {
    "express": "^4.18.0",
    "lodash": "^4.17.21"
  },
  "devDependencies": {
    "jest": "^28.0.0",
    "webpack": "^5.70.0"
  }
}
    `.trim(),
    language: 'json',
    is_public: true
  },

  // Special content types
  markdown: {
    title: 'Markdown Documentation',
    content: `
# Project Documentation

## Overview

This is a **sample** markdown document with various formatting options.

### Features

- *Italic text*
- **Bold text**
- \`Inline code\`
- [Links](https://example.com)

### Code Block

\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`

### Table

| Feature | Status | Priority |
|---------|--------|----------|
| Auth    | ✅     | High     |
| API     | 🚧     | Medium   |
| UI      | ❌     | Low      |

> This is a blockquote with important information.
    `.trim(),
    language: 'markdown',
    is_public: true
  },

  // Large content for performance testing
  largePaste: {
    title: 'Large Content Test',
    content: Array.from({ length: 1000 }, (_, i) => 
      `// Line ${i + 1}: This is a test line with some content\nconsole.log("Line ${i + 1}");`
    ).join('\n'),
    language: 'javascript',
    is_public: true
  },

  // Unicode and special characters
  unicode: {
    title: 'Unicode Test 🚀',
    content: `
// Unicode and special characters test
const emoji = "🚀 🌟 💻 🎉 ✨";
const unicode = "Ñoño café résumé naïve";
const symbols = "!@#$%^&*()_+-=[]{}|;':\",./<>?";
const math = "∑ ∆ ∞ ≠ ≤ ≥ ± × ÷";
const arrows = "← → ↑ ↓ ↔ ↕ ↖ ↗ ↘ ↙";

console.log({ emoji, unicode, symbols, math, arrows });
    `.trim(),
    language: 'javascript',
    is_public: true
  },

  // Private paste
  privatePaste: {
    title: 'Private Configuration',
    content: `
# Private Configuration
API_KEY=secret_key_12345
DATABASE_URL=postgresql://user:pass@localhost/db
JWT_SECRET=super_secret_jwt_key
    `.trim(),
    language: 'bash',
    is_public: false
  },

  // Paste with custom URL
  customUrlPaste: {
    title: 'Custom URL Example',
    content: 'console.log("This paste has a custom URL");',
    language: 'javascript',
    custom_url: 'my-custom-paste',
    is_public: true
  }
};

export const languageExamples = {
  javascript: 'console.log("Hello, JavaScript!");',
  python: 'print("Hello, Python!")',
  java: 'System.out.println("Hello, Java!");',
  cpp: '#include <iostream>\nint main() { std::cout << "Hello, C++!"; return 0; }',
  csharp: 'Console.WriteLine("Hello, C#!");',
  php: '<?php echo "Hello, PHP!"; ?>',
  ruby: 'puts "Hello, Ruby!"',
  go: 'package main\nimport "fmt"\nfunc main() { fmt.Println("Hello, Go!") }',
  rust: 'fn main() { println!("Hello, Rust!"); }',
  swift: 'print("Hello, Swift!")',
  kotlin: 'fun main() { println("Hello, Kotlin!") }',
  typescript: 'console.log("Hello, TypeScript!" as string);',
  sql: 'SELECT "Hello, SQL!" as greeting;',
  bash: 'echo "Hello, Bash!"',
  powershell: 'Write-Host "Hello, PowerShell!"',
  yaml: 'greeting: "Hello, YAML!"',
  xml: '<?xml version="1.0"?><greeting>Hello, XML!</greeting>',
  dockerfile: 'FROM node:16\nRUN echo "Hello, Docker!"'
};

export function createTestPaste(overrides: Partial<TestPaste> = {}): Omit<TestPaste, 'id'> {
  const timestamp = Date.now();
  return {
    title: `Test Paste ${timestamp}`,
    content: `console.log('Test paste content ${timestamp}');`,
    language: 'javascript',
    is_public: true,
    ...overrides
  };
}

export function createBulkTestPastes(count: number): Omit<TestPaste, 'id'>[] {
  return Array.from({ length: count }, (_, index) => ({
    title: `Bulk Paste ${Date.now()}_${index}`,
    content: `console.log('Bulk paste ${index}');`,
    language: 'javascript',
    is_public: true
  }));
}

export function getPasteByLanguage(language: string): Omit<TestPaste, 'id'> {
  const timestamp = Date.now();
  const content = languageExamples[language as keyof typeof languageExamples] || 
                  `// ${language} example\nconsole.log("Hello, ${language}!");`;
  
  return {
    title: `${language} Example ${timestamp}`,
    content,
    language,
    is_public: true
  };
}

export function getRandomPaste(): Omit<TestPaste, 'id'> {
  const pastes = Object.values(pasteFixtures);
  const randomPaste = pastes[Math.floor(Math.random() * pastes.length)];
  const timestamp = Date.now();
  
  return {
    ...randomPaste,
    title: `${randomPaste.title} ${timestamp}`,
    custom_url: randomPaste.custom_url ? `${randomPaste.custom_url}-${timestamp}` : undefined
  };
}

export function createPasteWithExpiration(hours: number): Omit<TestPaste, 'id'> {
  const expirationDate = new Date(Date.now() + hours * 60 * 60 * 1000);
  
  return {
    title: `Expiring Paste (${hours}h)`,
    content: `console.log('This paste expires in ${hours} hours');`,
    language: 'javascript',
    is_public: true,
    expires_at: expirationDate.toISOString()
  };
}

export function createEncryptedPaste(password: string): Omit<TestPaste, 'id'> {
  return {
    title: 'Encrypted Paste',
    content: 'This is secret content that should be encrypted',
    language: 'text',
    is_public: false,
    // Note: Encryption handling depends on your API implementation
    // You might need additional fields for encrypted pastes
  };
}

export const testScenarios = {
  // Common test scenarios
  createAndView: () => createTestPaste(),
  createPrivate: () => createTestPaste({ is_public: false }),
  createWithCustomUrl: () => createTestPaste({ 
    custom_url: `test-${Date.now()}` 
  }),
  createExpiring: () => createPasteWithExpiration(1),
  createLarge: () => ({ ...pasteFixtures.largePaste }),
  createUnicode: () => ({ ...pasteFixtures.unicode }),
  
  // Language-specific scenarios
  createJavaScript: () => ({ ...pasteFixtures.javascript }),
  createPython: () => ({ ...pasteFixtures.python }),
  createHTML: () => ({ ...pasteFixtures.html }),
  createMarkdown: () => ({ ...pasteFixtures.markdown })
};
