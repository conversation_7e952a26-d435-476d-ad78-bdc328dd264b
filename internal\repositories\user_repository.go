package repositories

import (
	"database/sql"
	"enhanced-pastebin/internal/models"
)

type UserRepository interface {
	Create(user *models.User) error
	GetByID(id string) (*models.User, error)
	GetByEmail(email string) (*models.User, error)
	GetByUsername(username string) (*models.User, error)
	Update(user *models.User) error
	Delete(id string) error
}

type userRepository struct {
	db *sql.DB
}

func NewUserRepository(db *sql.DB) UserRepository {
	return &userRepository{db: db}
}

func (r *userRepository) Create(user *models.User) error {
	query := `
		INSERT INTO users (id, username, email, password_hash, reputation_score, created_at, last_active)
		VALUES ($1, $2, $3, $4, $5, $6, $7)`
	
	_, err := r.db.Exec(query, user.ID, user.Username, user.Email, user.PasswordHash, 
		user.ReputationScore, user.CreatedAt, user.LastActive)
	return err
}

func (r *userRepository) GetByID(id string) (*models.User, error) {
	user := &models.User{}
	query := `
		SELECT id, username, email, password_hash, reputation_score, created_at, last_active
		FROM users WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash,
		&user.ReputationScore, &user.CreatedAt, &user.LastActive)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return user, err
}

func (r *userRepository) GetByEmail(email string) (*models.User, error) {
	user := &models.User{}
	query := `
		SELECT id, username, email, password_hash, reputation_score, created_at, last_active
		FROM users WHERE email = $1`
	
	err := r.db.QueryRow(query, email).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash,
		&user.ReputationScore, &user.CreatedAt, &user.LastActive)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return user, err
}

func (r *userRepository) GetByUsername(username string) (*models.User, error) {
	user := &models.User{}
	query := `
		SELECT id, username, email, password_hash, reputation_score, created_at, last_active
		FROM users WHERE username = $1`
	
	err := r.db.QueryRow(query, username).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash,
		&user.ReputationScore, &user.CreatedAt, &user.LastActive)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return user, err
}

func (r *userRepository) Update(user *models.User) error {
	query := `
		UPDATE users 
		SET username = $2, email = $3, reputation_score = $4, last_active = $5
		WHERE id = $1`
	
	_, err := r.db.Exec(query, user.ID, user.Username, user.Email, 
		user.ReputationScore, user.LastActive)
	return err
}

func (r *userRepository) Delete(id string) error {
	query := `DELETE FROM users WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}