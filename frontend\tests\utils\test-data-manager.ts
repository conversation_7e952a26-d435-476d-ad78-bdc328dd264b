import { TestApiClient, TestUser, TestPaste } from './api-client';
import { userFixtures, createTestUser, createBulkTestUsers } from '../fixtures/users';
import { pasteFixtures, createTestPaste, createBulkTestPastes } from '../fixtures/pastes';

export interface TestDataSet {
  users: TestUser[];
  pastes: TestPaste[];
  cleanup: () => Promise<void>;
}

export class TestDataManager {
  private api: TestApiClient;
  private createdUsers: TestUser[] = [];
  private createdPastes: TestPaste[] = [];

  constructor(apiClient: TestApiClient) {
    this.api = apiClient;
  }

  // User management
  async createUser(userData?: Partial<TestUser>): Promise<TestUser> {
    const user = await this.api.createTestUser(userData);
    this.createdUsers.push(user);
    return user;
  }

  async createUsers(count: number): Promise<TestUser[]> {
    const userDataList = createBulkTestUsers(count);
    const users: TestUser[] = [];

    for (const userData of userDataList) {
      const user = await this.createUser(userData);
      users.push(user);
    }

    return users;
  }

  async createUserWithRole(role: 'admin' | 'premium' | 'regular'): Promise<TestUser> {
    const userData = role === 'admin' ? userFixtures.adminUser :
                    role === 'premium' ? userFixtures.premiumUser :
                    userFixtures.regularUser;

    return this.createUser({
      ...userData,
      username: `${userData.username}_${Date.now()}`,
      email: `${userData.username}_${Date.now()}@example.com`
    });
  }

  async loginAsUser(user: TestUser): Promise<TestUser> {
    const loginResponse = await this.api.login({
      username: user.username,
      password: user.password
    });

    if (loginResponse.error) {
      throw new Error(`Failed to login user: ${loginResponse.error}`);
    }

    return { ...user, token: loginResponse.data?.token };
  }

  // Paste management
  async createPaste(pasteData?: Partial<TestPaste>, asUser?: TestUser): Promise<TestPaste> {
    if (asUser) {
      await this.loginAsUser(asUser);
    }

    const paste = await this.api.createTestPaste(pasteData);
    this.createdPastes.push(paste);
    return paste;
  }

  async createPastes(count: number, asUser?: TestUser): Promise<TestPaste[]> {
    if (asUser) {
      await this.loginAsUser(asUser);
    }

    const pasteDataList = createBulkTestPastes(count);
    const pastes: TestPaste[] = [];

    for (const pasteData of pasteDataList) {
      const paste = await this.api.createTestPaste(pasteData);
      this.createdPastes.push(paste);
      pastes.push(paste);
    }

    return pastes;
  }

  async createPasteWithFixture(fixtureName: keyof typeof pasteFixtures, asUser?: TestUser): Promise<TestPaste> {
    const fixtureData = pasteFixtures[fixtureName];
    return this.createPaste(fixtureData, asUser);
  }

  // Scenario builders
  async createUserWithPastes(pasteCount: number = 3): Promise<{ user: TestUser; pastes: TestPaste[] }> {
    const user = await this.createUser();
    const pastes = await this.createPastes(pasteCount, user);
    return { user, pastes };
  }

  async createCollaborationScenario(): Promise<{
    owner: TestUser;
    collaborators: TestUser[];
    paste: TestPaste;
  }> {
    const owner = await this.createUser();
    const collaborators = await this.createUsers(2);
    const paste = await this.createPaste({
      title: 'Collaboration Test Paste',
      content: 'This paste is for collaboration testing',
      is_public: true
    }, owner);

    return { owner, collaborators, paste };
  }

  async createPrivacyScenario(): Promise<{
    owner: TestUser;
    publicPastes: TestPaste[];
    privatePastes: TestPaste[];
    otherUser: TestUser;
  }> {
    const owner = await this.createUser();
    const otherUser = await this.createUser();

    const publicPastes = await Promise.all([
      this.createPaste({ title: 'Public Paste 1', is_public: true }, owner),
      this.createPaste({ title: 'Public Paste 2', is_public: true }, owner)
    ]);

    const privatePastes = await Promise.all([
      this.createPaste({ title: 'Private Paste 1', is_public: false }, owner),
      this.createPaste({ title: 'Private Paste 2', is_public: false }, owner)
    ]);

    return { owner, publicPastes, privatePastes, otherUser };
  }

  async createLanguageTestSet(): Promise<{ user: TestUser; pastes: Record<string, TestPaste> }> {
    const user = await this.createUser();
    const languages = ['javascript', 'python', 'html', 'css', 'json'];
    const pastes: Record<string, TestPaste> = {};

    for (const language of languages) {
      if (language in pasteFixtures) {
        pastes[language] = await this.createPasteWithFixture(
          language as keyof typeof pasteFixtures, 
          user
        );
      }
    }

    return { user, pastes };
  }

  async createExpirationTestSet(): Promise<{ user: TestUser; pastes: TestPaste[] }> {
    const user = await this.createUser();
    const now = new Date();
    
    const pastes = await Promise.all([
      // Paste that expires in 1 hour
      this.createPaste({
        title: 'Expires in 1 hour',
        expires_at: new Date(now.getTime() + 60 * 60 * 1000).toISOString()
      }, user),
      
      // Paste that expires in 1 day
      this.createPaste({
        title: 'Expires in 1 day',
        expires_at: new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString()
      }, user),
      
      // Paste that never expires
      this.createPaste({
        title: 'Never expires'
      }, user)
    ]);

    return { user, pastes };
  }

  async createPerformanceTestSet(): Promise<{ user: TestUser; largePastes: TestPaste[] }> {
    const user = await this.createUser();
    const largePastes: TestPaste[] = [];

    // Create pastes with different sizes
    const sizes = [1000, 5000, 10000]; // lines of code
    
    for (const size of sizes) {
      const content = Array.from({ length: size }, (_, i) => 
        `// Line ${i + 1}\nconsole.log("Performance test line ${i + 1}");`
      ).join('\n');

      const paste = await this.createPaste({
        title: `Large Paste (${size} lines)`,
        content,
        language: 'javascript'
      }, user);

      largePastes.push(paste);
    }

    return { user, largePastes };
  }

  // Cleanup methods
  async cleanupUsers(): Promise<void> {
    // Note: Actual user deletion depends on your API
    // For now, we just clear the tracking array
    this.createdUsers = [];
    this.api.clearAuthToken();
  }

  async cleanupPastes(): Promise<void> {
    // Delete all created pastes
    for (const paste of this.createdPastes) {
      try {
        if (paste.id) {
          await this.api.deletePaste(paste.id);
        }
      } catch (error) {
        console.warn(`Failed to delete paste ${paste.id}:`, error);
      }
    }
    this.createdPastes = [];
  }

  async cleanupAll(): Promise<void> {
    await this.cleanupPastes();
    await this.cleanupUsers();
  }

  // Utility methods
  getCreatedUsers(): TestUser[] {
    return [...this.createdUsers];
  }

  getCreatedPastes(): TestPaste[] {
    return [...this.createdPastes];
  }

  getUserByUsername(username: string): TestUser | undefined {
    return this.createdUsers.find(user => user.username === username);
  }

  getPasteByTitle(title: string): TestPaste | undefined {
    return this.createdPastes.find(paste => paste.title === title);
  }

  getPastesByUser(user: TestUser): TestPaste[] {
    // This would require tracking which user created which paste
    // For now, return all pastes (in a real implementation, you'd filter by user)
    return this.createdPastes;
  }

  // Statistics
  getStats(): {
    usersCreated: number;
    pastesCreated: number;
    publicPastes: number;
    privatePastes: number;
  } {
    const publicPastes = this.createdPastes.filter(paste => paste.is_public).length;
    const privatePastes = this.createdPastes.filter(paste => !paste.is_public).length;

    return {
      usersCreated: this.createdUsers.length,
      pastesCreated: this.createdPastes.length,
      publicPastes,
      privatePastes
    };
  }
}

// Factory function for creating test data manager
export function createTestDataManager(apiClient: TestApiClient): TestDataManager {
  return new TestDataManager(apiClient);
}

// Predefined test scenarios
export const testScenarios = {
  singleUser: async (manager: TestDataManager) => {
    const user = await manager.createUser();
    return { users: [user], pastes: [], cleanup: () => manager.cleanupAll() };
  },

  userWithPastes: async (manager: TestDataManager) => {
    const { user, pastes } = await manager.createUserWithPastes(5);
    return { users: [user], pastes, cleanup: () => manager.cleanupAll() };
  },

  multipleUsers: async (manager: TestDataManager) => {
    const users = await manager.createUsers(3);
    return { users, pastes: [], cleanup: () => manager.cleanupAll() };
  },

  collaboration: async (manager: TestDataManager) => {
    const { owner, collaborators, paste } = await manager.createCollaborationScenario();
    return { 
      users: [owner, ...collaborators], 
      pastes: [paste], 
      cleanup: () => manager.cleanupAll() 
    };
  },

  privacy: async (manager: TestDataManager) => {
    const { owner, publicPastes, privatePastes, otherUser } = await manager.createPrivacyScenario();
    return { 
      users: [owner, otherUser], 
      pastes: [...publicPastes, ...privatePastes], 
      cleanup: () => manager.cleanupAll() 
    };
  }
};
