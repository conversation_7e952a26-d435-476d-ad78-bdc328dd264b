import React from 'react';
import { Toast, ToastClose, ToastDescription, ToastProvider, ToastTitle, ToastViewport } from '@/components/ui/toast';
import { useToast } from '@/hooks/use-toast';
import { CheckCircle, AlertCircle, AlertTriangle, Info, X } from 'lucide-react';
import { cn } from '@/lib/utils';

const toastIcons = {
  default: Info,
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
  destructive: AlertTriangle,
};

const toastColors = {
  default: 'border-border bg-background text-foreground',
  success: 'border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-950 dark:text-green-100',
  error: 'border-red-200 bg-red-50 text-red-900 dark:border-red-800 dark:bg-red-950 dark:text-red-100',
  warning: 'border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-100',
  destructive: 'border-red-200 bg-red-50 text-red-900 dark:border-red-800 dark:bg-red-950 dark:text-red-100',
};

export function EnhancedToaster() {
  const { toasts } = useToast();

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, variant = 'default', ...props }) {
        const Icon = toastIcons[variant as keyof typeof toastIcons] || toastIcons.default;
        const colorClass = toastColors[variant as keyof typeof toastColors] || toastColors.default;

        return (
          <Toast 
            key={id} 
            className={cn(colorClass, 'flex items-start gap-3 p-4')}
            {...props}
          >
            <Icon 
              className={cn(
                'h-5 w-5 mt-0.5 flex-shrink-0',
                variant === 'success' && 'text-green-600 dark:text-green-400',
                variant === 'error' && 'text-red-600 dark:text-red-400',
                variant === 'warning' && 'text-yellow-600 dark:text-yellow-400',
                variant === 'destructive' && 'text-red-600 dark:text-red-400'
              )}
              aria-hidden="true"
            />
            <div className="flex-1 min-w-0">
              {title && (
                <ToastTitle className="font-semibold text-sm mb-1">
                  {title}
                </ToastTitle>
              )}
              {description && (
                <ToastDescription className="text-sm opacity-90">
                  {description}
                </ToastDescription>
              )}
              {action && (
                <div className="mt-3">
                  {action}
                </div>
              )}
            </div>
            <ToastClose className="flex-shrink-0">
              <X className="h-4 w-4" />
              <span className="sr-only">Close notification</span>
            </ToastClose>
          </Toast>
        );
      })}
      <ToastViewport />
    </ToastProvider>
  );
}

// Enhanced toast hook with better defaults
export function useEnhancedToast() {
  const { toast } = useToast();

  const showSuccess = (title: string, description?: string) => {
    toast({
      title,
      description,
      variant: 'default',
      duration: 4000,
    });
  };

  const showError = (title: string, description?: string) => {
    toast({
      title,
      description,
      variant: 'destructive',
      duration: 6000,
    });
  };

  const showWarning = (title: string, description?: string) => {
    toast({
      title,
      description,
      variant: 'default',
      duration: 5000,
    });
  };

  const showInfo = (title: string, description?: string) => {
    toast({
      title,
      description,
      variant: 'default',
      duration: 4000,
    });
  };

  const showLoading = (title: string, description?: string) => {
    return toast({
      title,
      description,
      variant: 'default',
      duration: Infinity, // Keep open until manually dismissed
    });
  };

  return {
    toast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,
  };
}

// Toast with action buttons
interface ActionToastProps {
  title: string;
  description?: string;
  primaryAction?: {
    label: string;
    onClick: () => void;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
  };
  variant?: 'default' | 'success' | 'error' | 'warning' | 'destructive';
}

export function useActionToast() {
  const { toast } = useToast();

  const showActionToast = ({
    title,
    description,
    primaryAction,
    secondaryAction,
    variant = 'default'
  }: ActionToastProps) => {
    return toast({
      title,
      description,
      variant,
      duration: 8000,
      action: (
        <div className="flex gap-2">
          {secondaryAction && (
            <button
              onClick={secondaryAction.onClick}
              className="px-3 py-1 text-xs font-medium rounded border border-current opacity-70 hover:opacity-100 transition-opacity"
            >
              {secondaryAction.label}
            </button>
          )}
          {primaryAction && (
            <button
              onClick={primaryAction.onClick}
              className="px-3 py-1 text-xs font-medium rounded bg-current text-background hover:opacity-90 transition-opacity"
            >
              {primaryAction.label}
            </button>
          )}
        </div>
      ),
    });
  };

  return { showActionToast };
}
