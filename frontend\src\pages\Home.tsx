import { Link } from 'react-router-dom'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Code, Shield, Users, Zap } from 'lucide-react'

export default function Home() {
  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <div className="text-center space-y-6">
        <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
          Enhanced Pastebin
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          A modern code sharing platform with real-time collaboration, smart syntax detection, 
          and enterprise-grade security features.
        </p>
        <div className="flex justify-center space-x-4">
          <Button asChild size="lg">
            <Link to="/create">Create Paste</Link>
          </Button>
          <Button asChild variant="outline" size="lg">
            <Link to="/register">Get Started</Link>
          </Button>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader>
            <Code className="h-8 w-8 text-primary" />
            <CardTitle>Smart Syntax</CardTitle>
            <CardDescription>
              Automatic language detection with beautiful syntax highlighting
            </CardDescription>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader>
            <Users className="h-8 w-8 text-primary" />
            <CardTitle>Real-time Collaboration</CardTitle>
            <CardDescription>
              Work together on code snippets with live editing and chat
            </CardDescription>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader>
            <Shield className="h-8 w-8 text-primary" />
            <CardTitle>Zero-Knowledge Encryption</CardTitle>
            <CardDescription>
              Client-side encryption ensures your sensitive code stays private
            </CardDescription>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader>
            <Zap className="h-8 w-8 text-primary" />
            <CardTitle>Dependency Aware</CardTitle>
            <CardDescription>
              Automatic dependency detection with security vulnerability scanning
            </CardDescription>
          </CardHeader>
        </Card>
      </div>

      {/* Getting Started */}
      <Card>
        <CardHeader>
          <CardTitle>Getting Started</CardTitle>
          <CardDescription>
            Start sharing code in seconds with our intuitive interface
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center space-y-2">
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto">
                1
              </div>
              <h3 className="font-semibold">Create Account</h3>
              <p className="text-sm text-muted-foreground">
                Sign up for free to unlock all features
              </p>
            </div>
            <div className="text-center space-y-2">
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto">
                2
              </div>
              <h3 className="font-semibold">Paste Your Code</h3>
              <p className="text-sm text-muted-foreground">
                Upload code with automatic language detection
              </p>
            </div>
            <div className="text-center space-y-2">
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto">
                3
              </div>
              <h3 className="font-semibold">Share & Collaborate</h3>
              <p className="text-sm text-muted-foreground">
                Share links and collaborate in real-time
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}