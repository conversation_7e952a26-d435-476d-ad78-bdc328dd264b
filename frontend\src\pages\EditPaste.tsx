import { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/hooks/use-toast'
import { pasteAPI, Paste } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import CodeEditor from '@/components/CodeEditor'

export default function EditPaste() {
  const { id } = useParams<{ id: string }>()
  const [paste, setPaste] = useState<Paste | null>(null)
  const [title, setTitle] = useState('')
  const [content, setContent] = useState('')
  const [language, setLanguage] = useState('text')
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const navigate = useNavigate()
  const { toast } = useToast()
  const { user } = useAuth()

  useEffect(() => {
    const fetchPaste = async () => {
      if (!id) return

      try {
        const response = await pasteAPI.get(id)
        const pasteData = response.data
        
        // Check if user owns this paste
        if (!user || pasteData.user_id !== user.id) {
          toast({
            title: "Error",
            description: "You don't have permission to edit this paste",
            variant: "destructive",
          })
          navigate(`/paste/${id}`)
          return
        }

        setPaste(pasteData)
        setTitle(pasteData.title)
        setContent(pasteData.content)
        setLanguage(pasteData.language)
      } catch (error: any) {
        toast({
          title: "Error",
          description: error.response?.data?.error || "Failed to load paste",
          variant: "destructive",
        })
        navigate('/')
      } finally {
        setIsLoading(false)
      }
    }

    fetchPaste()
  }, [id, user, navigate, toast])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!id || !paste) return

    setIsSaving(true)

    try {
      await pasteAPI.update(id, {
        title,
        content,
        language,
        is_encrypted: paste.is_encrypted,
        is_watermarked: paste.is_watermarked,
      })

      toast({
        title: "Success",
        description: "Paste updated successfully",
      })
      navigate(`/paste/${id}`)
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Failed to update paste",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardContent className="p-8">
            <div className="text-center">Loading paste...</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!paste) {
    return (
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardContent className="p-8">
            <div className="text-center">Paste not found</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Edit Paste</CardTitle>
          <CardDescription>
            Make changes to your paste
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="title" className="block text-sm font-medium mb-2">
                Title
              </label>
              <Input
                id="title"
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter paste title"
                required
                maxLength={500}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Code Editor
              </label>
              <CodeEditor
                value={content}
                onChange={setContent}
                language={language}
                onLanguageChange={setLanguage}
                height="500px"
                showToolbar={true}
                showLanguageSelector={true}
                title="Edit Paste Content"
              />
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate(`/paste/${id}`)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}