import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CheckCircle, AlertTriangle, XCircle, Eye, Keyboard, MousePointer } from 'lucide-react';

interface AccessibilityIssue {
  type: 'error' | 'warning' | 'info';
  category: 'color-contrast' | 'keyboard' | 'aria' | 'structure' | 'images';
  message: string;
  element?: string;
  suggestion?: string;
}

export default function AccessibilityChecker() {
  const [issues, setIssues] = useState<AccessibilityIssue[]>([]);
  const [isChecking, setIsChecking] = useState(false);
  const [score, setScore] = useState<number>(0);

  const runAccessibilityCheck = async () => {
    setIsChecking(true);
    const foundIssues: AccessibilityIssue[] = [];

    try {
      // Check for missing alt text on images
      const images = document.querySelectorAll('img');
      images.forEach((img, index) => {
        if (!img.alt && !img.getAttribute('aria-label')) {
          foundIssues.push({
            type: 'error',
            category: 'images',
            message: `Image ${index + 1} is missing alt text`,
            element: img.src || 'Unknown image',
            suggestion: 'Add descriptive alt text or aria-label to the image'
          });
        }
      });

      // Check for missing form labels
      const inputs = document.querySelectorAll('input, select, textarea');
      inputs.forEach((input, index) => {
        const hasLabel = input.getAttribute('aria-label') || 
                        input.getAttribute('aria-labelledby') ||
                        document.querySelector(`label[for="${input.id}"]`);
        
        if (!hasLabel) {
          foundIssues.push({
            type: 'error',
            category: 'aria',
            message: `Form control ${index + 1} is missing a label`,
            element: input.tagName.toLowerCase(),
            suggestion: 'Add a label element or aria-label attribute'
          });
        }
      });

      // Check for proper heading structure
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      let previousLevel = 0;
      headings.forEach((heading, index) => {
        const currentLevel = parseInt(heading.tagName.charAt(1));
        if (index === 0 && currentLevel !== 1) {
          foundIssues.push({
            type: 'warning',
            category: 'structure',
            message: 'Page should start with an h1 heading',
            element: heading.tagName.toLowerCase(),
            suggestion: 'Use h1 for the main page heading'
          });
        }
        if (currentLevel > previousLevel + 1) {
          foundIssues.push({
            type: 'warning',
            category: 'structure',
            message: `Heading level skipped from h${previousLevel} to h${currentLevel}`,
            element: heading.tagName.toLowerCase(),
            suggestion: 'Use heading levels in sequential order'
          });
        }
        previousLevel = currentLevel;
      });

      // Check for keyboard accessibility
      const interactiveElements = document.querySelectorAll('button, a, input, select, textarea, [tabindex]');
      interactiveElements.forEach((element, index) => {
        const tabIndex = element.getAttribute('tabindex');
        if (tabIndex && parseInt(tabIndex) > 0) {
          foundIssues.push({
            type: 'warning',
            category: 'keyboard',
            message: `Element ${index + 1} has positive tabindex`,
            element: element.tagName.toLowerCase(),
            suggestion: 'Avoid positive tabindex values, use 0 or -1 instead'
          });
        }
      });

      // Check for color contrast (simplified check)
      const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, a, button');
      textElements.forEach((element, index) => {
        const styles = window.getComputedStyle(element);
        const backgroundColor = styles.backgroundColor;
        const color = styles.color;
        
        // Simple check for transparent or very light backgrounds with dark text
        if (backgroundColor === 'rgba(0, 0, 0, 0)' || backgroundColor === 'transparent') {
          // Skip this check for transparent backgrounds
          return;
        }
        
        // This is a simplified check - in a real implementation, you'd calculate actual contrast ratios
        if (color === 'rgb(255, 255, 255)' && backgroundColor === 'rgb(255, 255, 255)') {
          foundIssues.push({
            type: 'error',
            category: 'color-contrast',
            message: `Element ${index + 1} may have insufficient color contrast`,
            element: element.tagName.toLowerCase(),
            suggestion: 'Ensure text has sufficient contrast against its background'
          });
        }
      });

      // Check for missing ARIA landmarks
      const landmarks = document.querySelectorAll('main, nav, header, footer, aside, section[aria-label], section[aria-labelledby]');
      if (landmarks.length === 0) {
        foundIssues.push({
          type: 'warning',
          category: 'structure',
          message: 'No ARIA landmarks found',
          suggestion: 'Add semantic HTML5 elements or ARIA landmarks for better navigation'
        });
      }

      setIssues(foundIssues);
      
      // Calculate accessibility score
      const totalChecks = 10; // Approximate number of checks
      const errorWeight = 3;
      const warningWeight = 1;
      
      const errorCount = foundIssues.filter(issue => issue.type === 'error').length;
      const warningCount = foundIssues.filter(issue => issue.type === 'warning').length;
      
      const deductions = (errorCount * errorWeight) + (warningCount * warningWeight);
      const calculatedScore = Math.max(0, Math.min(100, 100 - (deductions * 5)));
      
      setScore(calculatedScore);
    } catch (error) {
      console.error('Error running accessibility check:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 90) return 'Excellent';
    if (score >= 70) return 'Good';
    if (score >= 50) return 'Needs Improvement';
    return 'Poor';
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'keyboard': return <Keyboard className="h-4 w-4" />;
      case 'images': return <Eye className="h-4 w-4" />;
      default: return <MousePointer className="h-4 w-4" />;
    }
  };

  const getIssueIcon = (type: string) => {
    switch (type) {
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default: return <CheckCircle className="h-4 w-4 text-blue-500" />;
    }
  };

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Card className="fixed bottom-4 right-4 w-96 max-h-96 z-50 shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm">Accessibility Checker</CardTitle>
          <Button 
            size="sm" 
            onClick={runAccessibilityCheck}
            disabled={isChecking}
          >
            {isChecking ? 'Checking...' : 'Run Check'}
          </Button>
        </div>
        {score > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm">Score:</span>
            <span className={`font-bold ${getScoreColor(score)}`}>
              {score}/100
            </span>
            <Badge variant="outline" className="text-xs">
              {getScoreLabel(score)}
            </Badge>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="pt-0">
        {issues.length === 0 ? (
          <p className="text-sm text-muted-foreground">
            {isChecking ? 'Checking accessibility...' : 'Run a check to see accessibility issues'}
          </p>
        ) : (
          <ScrollArea className="h-48">
            <div className="space-y-2">
              {issues.map((issue, index) => (
                <div key={index} className="border rounded p-2 text-xs">
                  <div className="flex items-start gap-2">
                    {getIssueIcon(issue.type)}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        {getCategoryIcon(issue.category)}
                        <span className="font-medium">{issue.message}</span>
                      </div>
                      {issue.element && (
                        <p className="text-muted-foreground mb-1">
                          Element: {issue.element}
                        </p>
                      )}
                      {issue.suggestion && (
                        <p className="text-blue-600 dark:text-blue-400">
                          💡 {issue.suggestion}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
