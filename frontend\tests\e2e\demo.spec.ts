import { test, expect } from '@playwright/test';
import { HomePage, LoginPage, CreatePastePage, ViewPastePage } from '../pages';
import { TestHelpers } from '../utils/test-helpers';
import { TestDataManager, createTestDataManager } from '../utils/test-data-manager';

test.describe('Demo Test - Complete Flow', () => {
  let homePage: HomePage;
  let loginPage: LoginPage;
  let createPastePage: CreatePastePage;
  let viewPastePage: ViewPastePage;
  let helpers: TestHelpers;
  let dataManager: TestDataManager;

  test.beforeEach(async ({ page }) => {
    homePage = new HomePage(page);
    loginPage = new LoginPage(page);
    createPastePage = new CreatePastePage(page);
    viewPastePage = new ViewPastePage(page);
    helpers = new TestHelpers();
    dataManager = createTestDataManager(helpers.api);
  });

  test.afterEach(async () => {
    await dataManager.cleanupAll();
  });

  test('complete user journey - register, login, create paste, view paste', async ({ page }) => {
    // 1. Visit home page
    await homePage.visit();
    await homePage.expectToBeOnHomePage();

    // 2. Register a new user
    const userData = {
      username: `demouser_${Date.now()}`,
      email: `demo_${Date.now()}@example.com`,
      password: 'DemoPassword123!'
    };

    await homePage.navigateToRegister();
    await page.fill('input[name="username"]', userData.username);
    await page.fill('input[name="email"]', userData.email);
    await page.fill('input[name="password"]', userData.password);
    await page.fill('input[name="confirmPassword"]', userData.password);
    
    // Accept terms if checkbox exists
    const termsCheckbox = page.locator('input[type="checkbox"]').first();
    if (await termsCheckbox.isVisible()) {
      await termsCheckbox.check();
    }
    
    await page.click('button[type="submit"]');

    // 3. Login with the new user
    await loginPage.expectToBeOnLoginPage();
    await loginPage.login(userData.username, userData.password);
    await homePage.expectToBeOnHomePage();
    await loginPage.expectToBeLoggedIn(userData.username);

    // 4. Create a new paste
    await createPastePage.visit();
    const pasteData = {
      title: 'Demo Paste',
      content: 'console.log("Hello from Playwright test!");',
      language: 'javascript'
    };

    const pasteId = await createPastePage.createPasteAndWaitForRedirect(pasteData);

    // 5. Verify the paste was created correctly
    await viewPastePage.expectToBeOnPastePage(pasteId);
    await viewPastePage.expectPasteTitle(pasteData.title);
    await viewPastePage.expectPasteContent(pasteData.content);
    await viewPastePage.expectPasteLanguage(pasteData.language);
    await viewPastePage.expectPasteAuthor(userData.username);

    // 6. Test paste actions
    await viewPastePage.copyPasteContent();
    await viewPastePage.expectSuccessMessage(/copied/i);

    // 7. Edit the paste
    await viewPastePage.editPaste();
    await createPastePage.fillTitle('Updated Demo Paste');
    await createPastePage.fillContent('console.log("Updated content!");');
    await createPastePage.submitForm();

    // 8. Verify the update
    await viewPastePage.expectPasteTitle('Updated Demo Paste');
    await viewPastePage.expectPasteContent('Updated content!');

    // 9. Navigate back to home and verify paste appears in list
    await homePage.visit();
    await homePage.expectPasteInList('Updated Demo Paste');

    // 10. Logout
    await loginPage.logout();
    await loginPage.expectToBeLoggedOut();
  });

  test('demo test with test data manager', async ({ page }) => {
    // Create test data using the data manager
    const { user, pastes } = await dataManager.createUserWithPastes(3);

    // Login as the created user
    await loginPage.visit();
    await loginPage.login(user.username, user.password);
    await homePage.expectToBeOnHomePage();

    // Verify we can see the created pastes
    await page.goto('/profile/pastes');
    
    for (const paste of pastes) {
      const pasteElement = page.locator(`:text("${paste.title}")`);
      if (await pasteElement.isVisible()) {
        await expect(pasteElement).toBeVisible();
      }
    }

    // View one of the pastes
    if (pastes.length > 0) {
      await viewPastePage.visit(pastes[0].id!);
      await viewPastePage.expectPasteTitle(pastes[0].title);
      await viewPastePage.expectPasteContent(pastes[0].content);
    }

    // Check data manager stats
    const stats = dataManager.getStats();
    expect(stats.usersCreated).toBe(1);
    expect(stats.pastesCreated).toBe(3);
  });

  test('demo backend health check', async () => {
    // Test that the backend is running and responding
    const healthResponse = await helpers.api.health();
    expect(healthResponse.status).toBe(200);
    expect(healthResponse.data).toHaveProperty('status', 'ok');
  });
});
