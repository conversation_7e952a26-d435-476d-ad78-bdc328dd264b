-- Create users table
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    reputation_score INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    last_active TIMESTAMP DEFAULT NOW()
);

-- Create pastes table
CREATE TABLE pastes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    custom_url VARCHAR(255) UNIQUE,
    title VARCHAR(500) NOT NULL,
    content TEXT,
    language VARCHAR(50),
    is_encrypted BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP,
    view_count INTEGER DEFAULT 0,
    max_views INTEGER,
    edit_count INTEGER DEFAULT 0,
    is_watermarked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    user_id UUID REFERENCES users(id)
);

-- Create paste versions table
CREATE TABLE paste_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    paste_id UUID REFERENCES pastes(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    
    UNIQUE(paste_id, version_number)
);

-- Create chat messages table
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    paste_id UUID REFERENCES pastes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    content TEXT NOT NULL,
    line_references INTEGER[],
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create access logs table
CREATE TABLE access_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    paste_id VARCHAR(255),
    user_id UUID REFERENCES users(id),
    ip_address INET NOT NULL,
    user_agent TEXT,
    action VARCHAR(50) NOT NULL,
    details TEXT,
    timestamp TIMESTAMP DEFAULT NOW()
);

-- Create watermarks table
CREATE TABLE watermarks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    paste_id UUID REFERENCES pastes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    watermark_id VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_pastes_custom_url ON pastes(custom_url);
CREATE INDEX idx_pastes_user_id ON pastes(user_id);
CREATE INDEX idx_pastes_expires_at ON pastes(expires_at);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_paste_versions_paste_id ON paste_versions(paste_id);
CREATE INDEX idx_chat_messages_paste_id ON chat_messages(paste_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX idx_access_logs_paste_id ON access_logs(paste_id);
CREATE INDEX idx_access_logs_user_id ON access_logs(user_id);
CREATE INDEX idx_access_logs_timestamp ON access_logs(timestamp);
CREATE INDEX idx_access_logs_action ON access_logs(action);
CREATE INDEX idx_watermarks_paste_id ON watermarks(paste_id);
CREATE INDEX idx_watermarks_user_id ON watermarks(user_id);
CREATE INDEX idx_watermarks_watermark_id ON watermarks(watermark_id);