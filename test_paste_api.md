# Paste API Testing Guide

## Prerequisites
1. Start the database (PostgreSQL)
2. Run migrations: `go run cmd/migrate/main.go up`
3. Start the server: `go run cmd/server/main.go`

## Test Cases

### 1. Create Paste (POST /api/v1/pastes)
```bash
curl -X POST http://localhost:8080/api/v1/pastes \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Paste",
    "content": "console.log(\"Hello, World!\");",
    "language": "javascript",
    "is_encrypted": false,
    "is_watermarked": false
  }'
```

### 2. Create Paste with Custom URL
```bash
curl -X POST http://localhost:8080/api/v1/pastes \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Custom URL Paste",
    "content": "print(\"Hello from Python!\")",
    "language": "python",
    "custom_url": "my-python-code",
    "is_encrypted": false,
    "is_watermarked": false
  }'
```

### 3. Get Paste by ID (GET /api/v1/pastes/:id)
```bash
curl http://localhost:8080/api/v1/pastes/{PASTE_ID}
```

### 4. Get Paste by Custom URL (GET /api/v1/p/:customUrl)
```bash
curl http://localhost:8080/api/v1/p/my-python-code
```

### 5. Update Paste (PUT /api/v1/pastes/:id) - Requires Authentication
```bash
# First register a user
curl -X POST http://localhost:8080/api/v1/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Login to get token
curl -X POST http://localhost:8080/api/v1/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Update paste with token
curl -X PUT http://localhost:8080/api/v1/pastes/{PASTE_ID} \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {TOKEN}" \
  -d '{
    "title": "Updated Paste Title",
    "content": "console.log(\"Updated content!\");"
  }'
```

### 6. Delete Paste (DELETE /api/v1/pastes/:id) - Requires Authentication
```bash
curl -X DELETE http://localhost:8080/api/v1/pastes/{PASTE_ID} \
  -H "Authorization: Bearer {TOKEN}"
```

## Expected Behaviors

### Content Validation
- Title is required (1-500 characters)
- Content is required (max 1MB)
- Custom URL must be 3-255 characters, alphanumeric + hyphens/underscores
- Custom URL must be unique

### View Counting
- Each GET request increments view_count
- View count is returned in the response

### Authorization
- Only paste owners can update/delete their pastes
- Anonymous pastes cannot be updated/deleted

### Error Handling
- 400 for validation errors
- 401 for authentication errors
- 403 for authorization errors
- 404 for not found
- 409 for conflicts (duplicate custom URL)