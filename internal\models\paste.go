package models

import (
	"time"
)

type Paste struct {
	ID           string     `json:"id" db:"id" validate:"required,uuid"`
	CustomURL    *string    `json:"custom_url,omitempty" db:"custom_url" validate:"omitempty,min=3,max=255,alphanum"`
	Title        string     `json:"title" db:"title" validate:"required,min=1,max=500"`
	Content      string     `json:"content" db:"content" validate:"required"`
	Language     string     `json:"language" db:"language" validate:"max=50"`
	IsEncrypted  bool       `json:"is_encrypted" db:"is_encrypted"`
	ExpiresAt    *time.Time `json:"expires_at,omitempty" db:"expires_at"`
	ViewCount    int        `json:"view_count" db:"view_count" validate:"min=0"`
	MaxViews     *int       `json:"max_views,omitempty" db:"max_views" validate:"omitempty,min=1"`
	EditCount    int        `json:"edit_count" db:"edit_count" validate:"min=0"`
	IsWatermarked bool      `json:"is_watermarked" db:"is_watermarked"`
	CreatedAt    time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at" db:"updated_at"`
	UserID       *string    `json:"user_id,omitempty" db:"user_id" validate:"omitempty,uuid"`
}

type CreatePasteRequest struct {
	CustomURL     *string    `json:"custom_url,omitempty" binding:"omitempty,min=3,max=255,alphanum" validate:"omitempty,min=3,max=255,alphanum"`
	Title         string     `json:"title" binding:"required,min=1,max=500" validate:"required,min=1,max=500"`
	Content       string     `json:"content" binding:"required" validate:"required"`
	Language      string     `json:"language" binding:"max=50" validate:"max=50"`
	IsEncrypted   bool       `json:"is_encrypted"`
	ExpiresAt     *time.Time `json:"expires_at,omitempty"`
	MaxViews      *int       `json:"max_views,omitempty" binding:"omitempty,min=1" validate:"omitempty,min=1"`
	IsWatermarked bool       `json:"is_watermarked"`
}

type UpdatePasteRequest struct {
	Title         *string    `json:"title,omitempty" binding:"omitempty,min=1,max=500" validate:"omitempty,min=1,max=500"`
	Content       *string    `json:"content,omitempty" binding:"omitempty,min=1" validate:"omitempty,min=1"`
	Language      *string    `json:"language,omitempty" binding:"omitempty,max=50" validate:"omitempty,max=50"`
	ExpiresAt     *time.Time `json:"expires_at,omitempty"`
	MaxViews      *int       `json:"max_views,omitempty" binding:"omitempty,min=1" validate:"omitempty,min=1"`
	IsWatermarked *bool      `json:"is_watermarked,omitempty"`
}