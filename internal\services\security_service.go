package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os/exec"
	"strings"
	"time"

	"enhanced-pastebin/internal/models"
)

type SecurityService interface {
	ScanDependencies(ctx context.Context, dependencies []models.Dependency) ([]models.Dependency, error)
	ScanNPMPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error)
	ScanPyPIPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error)
	ScanGoPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error)
	CalculateSecurityScore(vulnerabilities []models.SecurityVulnerability) int
}

type securityService struct {
	httpClient *http.Client
}

func NewSecurityService() SecurityService {
	return &securityService{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (s *securityService) ScanDependencies(ctx context.Context, dependencies []models.Dependency) ([]models.Dependency, error) {
	scannedDeps := make([]models.Dependency, len(dependencies))
	
	for i, dep := range dependencies {
		scannedDeps[i] = dep
		
		var securityInfo *models.DependencySecurityInfo
		var err error
		
		switch strings.ToLower(dep.Language) {
		case "javascript", "typescript", "jsx", "tsx":
			securityInfo, err = s.ScanNPMPackage(ctx, dep.Name, dep.Version)
		case "python":
			securityInfo, err = s.ScanPyPIPackage(ctx, dep.Name, dep.Version)
		case "go":
			securityInfo, err = s.ScanGoPackage(ctx, dep.Name, dep.Version)
		default:
			// For unsupported languages, create empty security info
			securityInfo = &models.DependencySecurityInfo{
				PackageName:     dep.Name,
				Version:         dep.Version,
				Vulnerabilities: []models.SecurityVulnerability{},
				SecurityScore:   100, // Assume safe if we can't scan
				LastScanned:     time.Now(),
				HasVulns:        false,
			}
		}
		
		if err != nil {
			// Log error but don't fail the entire scan
			// Create a basic security info with error indication
			securityInfo = &models.DependencySecurityInfo{
				PackageName:     dep.Name,
				Version:         dep.Version,
				Vulnerabilities: []models.SecurityVulnerability{},
				SecurityScore:   50, // Unknown security status
				LastScanned:     time.Now(),
				HasVulns:        false,
			}
		}
		
		scannedDeps[i].Security = securityInfo
	}
	
	return scannedDeps, nil
}

func (s *securityService) ScanNPMPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error) {
	// Try to use npm audit for local scanning first
	auditResult, err := s.runNPMAudit(ctx, packageName, version)
	if err == nil && auditResult != nil {
		return s.convertNPMAuditToSecurityInfo(packageName, version, auditResult), nil
	}
	
	// Fallback to OSV database API
	return s.queryOSVDatabase(ctx, packageName, version, "npm")
}

func (s *securityService) ScanPyPIPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error) {
	// Query OSV database for Python packages
	return s.queryOSVDatabase(ctx, packageName, version, "PyPI")
}

func (s *securityService) ScanGoPackage(ctx context.Context, packageName, version string) (*models.DependencySecurityInfo, error) {
	// Query OSV database for Go packages
	return s.queryOSVDatabase(ctx, packageName, version, "Go")
}

func (s *securityService) runNPMAudit(ctx context.Context, packageName, version string) (*models.NPMAuditResponse, error) {
	// Create a temporary package.json with the dependency
	packageJSON := fmt.Sprintf(`{
		"name": "temp-audit",
		"version": "1.0.0",
		"dependencies": {
			"%s": "%s"
		}
	}`, packageName, version)
	
	// Create temporary directory and run npm audit
	cmd := exec.CommandContext(ctx, "npm", "audit", "--json", "--package-lock-only")
	cmd.Stdin = strings.NewReader(packageJSON)
	
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr
	
	err := cmd.Run()
	if err != nil {
		// npm audit returns non-zero exit code when vulnerabilities are found
		// So we check if we got valid JSON output
		if stdout.Len() == 0 {
			return nil, fmt.Errorf("npm audit failed: %s", stderr.String())
		}
	}
	
	var auditResponse models.NPMAuditResponse
	if err := json.Unmarshal(stdout.Bytes(), &auditResponse); err != nil {
		return nil, fmt.Errorf("failed to parse npm audit output: %w", err)
	}
	
	return &auditResponse, nil
}

func (s *securityService) queryOSVDatabase(ctx context.Context, packageName, version, ecosystem string) (*models.DependencySecurityInfo, error) {
	// OSV API query
	query := map[string]interface{}{
		"package": map[string]string{
			"name":      packageName,
			"ecosystem": ecosystem,
		},
	}
	
	if version != "" {
		query["version"] = version
	}
	
	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal OSV query: %w", err)
	}
	
	req, err := http.NewRequestWithContext(ctx, "POST", "https://api.osv.dev/v1/query", bytes.NewReader(queryJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to create OSV request: %w", err)
	}
	
	req.Header.Set("Content-Type", "application/json")
	
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to query OSV database: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("OSV API returned status %d", resp.StatusCode)
	}
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read OSV response: %w", err)
	}
	
	var osvResponse struct {
		Vulns []struct {
			ID        string    `json:"id"`
			Summary   string    `json:"summary"`
			Details   string    `json:"details"`
			Aliases   []string  `json:"aliases"`
			Published time.Time `json:"published"`
			Modified  time.Time `json:"modified"`
			Affected  []struct {
				Package struct {
					Name      string `json:"name"`
					Ecosystem string `json:"ecosystem"`
				} `json:"package"`
				Ranges []struct {
					Type   string `json:"type"`
					Events []struct {
						Introduced string `json:"introduced,omitempty"`
						Fixed      string `json:"fixed,omitempty"`
					} `json:"events"`
				} `json:"ranges"`
				Versions             []string    `json:"versions,omitempty"`
				DatabaseSpecific     interface{} `json:"database_specific,omitempty"`
				EcosystemSpecific    interface{} `json:"ecosystem_specific,omitempty"`
			} `json:"affected"`
			References []struct {
				Type string `json:"type"`
				URL  string `json:"url"`
			} `json:"references"`
			Severity []struct {
				Type  string  `json:"type"`
				Score string  `json:"score"`
			} `json:"severity,omitempty"`
			DatabaseSpecific interface{} `json:"database_specific,omitempty"`
		} `json:"vulns"`
	}
	
	if err := json.Unmarshal(body, &osvResponse); err != nil {
		return nil, fmt.Errorf("failed to parse OSV response: %w", err)
	}
	
	return s.convertOSVToSecurityInfo(packageName, version, &osvResponse), nil
}

func (s *securityService) convertNPMAuditToSecurityInfo(packageName, version string, audit *models.NPMAuditResponse) *models.DependencySecurityInfo {
	var vulnerabilities []models.SecurityVulnerability
	
	for _, vuln := range audit.Vulnerabilities {
		if vuln.Name == packageName {
			for _, via := range vuln.Via {
				if via.Title != "" {
					vulnerability := models.SecurityVulnerability{
						ID:          fmt.Sprintf("npm-%s", via.Name),
						Title:       via.Title,
						Description: via.Title,
						Severity:    via.Severity,
						CVSS:        via.CVSS.Score,
						URL:         via.URL,
						PublishedAt: time.Now(), // npm audit doesn't provide published date
					}
					
					if len(via.CWE) > 0 {
						vulnerability.CWE = strings.Join(via.CWE, ", ")
					}
					
					vulnerabilities = append(vulnerabilities, vulnerability)
				}
			}
		}
	}
	
	securityScore := s.CalculateSecurityScore(vulnerabilities)
	
	return &models.DependencySecurityInfo{
		PackageName:     packageName,
		Version:         version,
		Vulnerabilities: vulnerabilities,
		SecurityScore:   securityScore,
		LastScanned:     time.Now(),
		HasVulns:        len(vulnerabilities) > 0,
	}
}

func (s *securityService) convertOSVToSecurityInfo(packageName, version string, osv interface{}) *models.DependencySecurityInfo {
	var vulnerabilities []models.SecurityVulnerability
	
	// Type assertion to handle the OSV response structure
	if osvResp, ok := osv.(*struct {
		Vulns []struct {
			ID        string    `json:"id"`
			Summary   string    `json:"summary"`
			Details   string    `json:"details"`
			Aliases   []string  `json:"aliases"`
			Published time.Time `json:"published"`
			Modified  time.Time `json:"modified"`
			Affected  []struct {
				Package struct {
					Name      string `json:"name"`
					Ecosystem string `json:"ecosystem"`
				} `json:"package"`
				Ranges []struct {
					Type   string `json:"type"`
					Events []struct {
						Introduced string `json:"introduced,omitempty"`
						Fixed      string `json:"fixed,omitempty"`
					} `json:"events"`
				} `json:"ranges"`
				Versions             []string    `json:"versions,omitempty"`
				DatabaseSpecific     interface{} `json:"database_specific,omitempty"`
				EcosystemSpecific    interface{} `json:"ecosystem_specific,omitempty"`
			} `json:"affected"`
			References []struct {
				Type string `json:"type"`
				URL  string `json:"url"`
			} `json:"references"`
			Severity []struct {
				Type  string  `json:"type"`
				Score string  `json:"score"`
			} `json:"severity,omitempty"`
			DatabaseSpecific interface{} `json:"database_specific,omitempty"`
		} `json:"vulns"`
	}); ok {
		for _, vuln := range osvResp.Vulns {
			severity := "unknown"
			var cvss float64
			
			// Extract severity information
			for _, sev := range vuln.Severity {
				if sev.Type == "CVSS_V3" {
					severity = s.cvssToSeverity(sev.Score)
					// Parse CVSS score if available
					if strings.Contains(sev.Score, "CVSS:3") {
						// Extract numeric score from CVSS vector
						parts := strings.Split(sev.Score, "/")
						for _, part := range parts {
							if strings.HasPrefix(part, "S:") {
								// This is a simplified extraction
								severity = "moderate"
								cvss = 5.0
								break
							}
						}
					}
				}
			}
			
			// Find CVE aliases
			var cve string
			for _, alias := range vuln.Aliases {
				if strings.HasPrefix(alias, "CVE-") {
					cve = alias
					break
				}
			}
			
			// Get reference URL
			var url string
			for _, ref := range vuln.References {
				if ref.Type == "ADVISORY" || ref.Type == "WEB" {
					url = ref.URL
					break
				}
			}
			
			vulnerability := models.SecurityVulnerability{
				ID:          vuln.ID,
				Title:       vuln.Summary,
				Description: vuln.Details,
				Severity:    severity,
				CVSS:        cvss,
				CVE:         cve,
				URL:         url,
				PublishedAt: vuln.Published,
			}
			
			vulnerabilities = append(vulnerabilities, vulnerability)
		}
	}
	
	securityScore := s.CalculateSecurityScore(vulnerabilities)
	
	return &models.DependencySecurityInfo{
		PackageName:     packageName,
		Version:         version,
		Vulnerabilities: vulnerabilities,
		SecurityScore:   securityScore,
		LastScanned:     time.Now(),
		HasVulns:        len(vulnerabilities) > 0,
	}
}

func (s *securityService) CalculateSecurityScore(vulnerabilities []models.SecurityVulnerability) int {
	if len(vulnerabilities) == 0 {
		return 100
	}
	
	score := 100
	for _, vuln := range vulnerabilities {
		switch strings.ToLower(vuln.Severity) {
		case "critical":
			score -= 30
		case "high":
			score -= 20
		case "moderate", "medium":
			score -= 10
		case "low":
			score -= 5
		default:
			score -= 5
		}
	}
	
	if score < 0 {
		score = 0
	}
	
	return score
}

func (s *securityService) cvssToSeverity(cvssVector string) string {
	// This is a simplified CVSS to severity mapping
	// In a real implementation, you'd parse the full CVSS vector
	if strings.Contains(cvssVector, "9.") || strings.Contains(cvssVector, "10.") {
		return "critical"
	} else if strings.Contains(cvssVector, "7.") || strings.Contains(cvssVector, "8.") {
		return "high"
	} else if strings.Contains(cvssVector, "4.") || strings.Contains(cvssVector, "5.") || strings.Contains(cvssVector, "6.") {
		return "moderate"
	} else {
		return "low"
	}
}