import { WebSocketClient } from './websocketClient';
import {
  Operation,
  TextOperation,
  CursorPosition,
  Selection,
  DocumentState,
  CollaboratorState,
  OperationResult
} from '../types/websocket';

export interface CollaborativeEditorOptions {
  pasteId: string;
  initialContent: string;
  websocketClient: WebSocketClient;
  onContentChange: (content: string) => void;
  onCollaboratorsChange: (collaborators: CollaboratorState[]) => void;
  onError: (error: string) => void;
}

export class CollaborativeEditor {
  private pasteId: string;
  private content: string;
  private version: number = 0;
  private websocketClient: WebSocketClient;
  private collaborators: Map<string, CollaboratorState> = new Map();
  private pendingOperations: Map<string, Operation> = new Map();
  private operationId: number = 0;

  private onContentChange: (content: string) => void;
  private onCollaboratorsChange: (collaborators: CollaboratorState[]) => void;
  private onError: (error: string) => void;

  constructor(options: CollaborativeEditorOptions) {
    this.pasteId = options.pasteId;
    this.content = options.initialContent;
    this.websocketClient = options.websocketClient;
    this.onContentChange = options.onContentChange;
    this.onCollaboratorsChange = options.onCollaboratorsChange;
    this.onError = options.onError;

    this.setupWebSocketHandlers();
  }

  // Initialize the collaborative editor
  public initialize(): void {
    // Request initial document state
    this.websocketClient.requestSync(this.pasteId);
  }

  // Insert text at a specific position
  public insertText(position: number, text: string): void {
    const operation = this.createOperation([{
      type: 'insert',
      position,
      content: text
    }]);

    this.applyOperationLocally(operation);
    this.sendOperation(operation);
  }

  // Delete text at a specific position
  public deleteText(position: number, length: number): void {
    const operation = this.createOperation([{
      type: 'delete',
      position,
      length
    }]);

    this.applyOperationLocally(operation);
    this.sendOperation(operation);
  }

  // Update cursor position
  public updateCursor(cursor: CursorPosition): void {
    this.websocketClient.sendCursorUpdate(this.pasteId, cursor);
  }

  // Update text selection
  public updateSelection(selection: Selection): void {
    this.websocketClient.sendSelectionUpdate(this.pasteId, selection);
  }

  // Get current content
  public getContent(): string {
    return this.content;
  }

  // Get current version
  public getVersion(): number {
    return this.version;
  }

  // Get collaborators
  public getCollaborators(): CollaboratorState[] {
    return Array.from(this.collaborators.values());
  }

  // Private methods

  private setupWebSocketHandlers(): void {
    this.websocketClient.setHandlers({
      onOperation: (operation: Operation) => {
        this.handleRemoteOperation(operation);
      },
      onCursorUpdate: (userId: string, cursor: CursorPosition) => {
        this.handleCursorUpdate(userId, cursor);
      },
      onSelectionUpdate: (userId: string, selection: Selection) => {
        this.handleSelectionUpdate(userId, selection);
      },
      onDocumentState: (state: DocumentState) => {
        this.handleDocumentState(state);
      },
      onOperationAck: (result: OperationResult) => {
        this.handleOperationAck(result);
      },
      onError: (_error: Event) => {
        this.onError('WebSocket error occurred');
      }
    });
  }

  private createOperation(textOperations: TextOperation[]): Operation {
    const operationId = `${Date.now()}-${++this.operationId}`;
    
    return {
      id: operationId,
      paste_id: this.pasteId,
      user_id: '', // Will be set by server
      username: '', // Will be set by server
      operations: textOperations,
      timestamp: new Date().toISOString(),
      version: this.version
    };
  }

  private sendOperation(operation: Operation): void {
    // Store pending operation
    this.pendingOperations.set(operation.id, operation);
    
    // Send to server
    this.websocketClient.sendOperation(operation);
  }

  private applyOperationLocally(operation: Operation): void {
    try {
      const newContent = this.applyTextOperations(this.content, operation.operations);
      this.content = newContent;
      this.onContentChange(newContent);
    } catch (error) {
      console.error('Error applying operation locally:', error);
      this.onError('Failed to apply text operation');
    }
  }

  private handleRemoteOperation(operation: Operation): void {
    try {
      // Apply the operation to our content
      const newContent = this.applyTextOperations(this.content, operation.operations);
      this.content = newContent;
      this.version = Math.max(this.version, operation.version);
      
      this.onContentChange(newContent);
    } catch (error) {
      console.error('Error applying remote operation:', error);
      this.onError('Failed to apply remote operation');
    }
  }

  private handleCursorUpdate(userId: string, cursor: CursorPosition): void {
    const collaborator = this.collaborators.get(userId);
    if (collaborator) {
      collaborator.cursor = cursor;
      collaborator.last_seen = new Date().toISOString();
      this.onCollaboratorsChange(this.getCollaborators());
    }
  }

  private handleSelectionUpdate(userId: string, selection: Selection): void {
    const collaborator = this.collaborators.get(userId);
    if (collaborator) {
      collaborator.selection = selection;
      collaborator.last_seen = new Date().toISOString();
      this.onCollaboratorsChange(this.getCollaborators());
    }
  }

  private handleDocumentState(state: DocumentState): void {
    // Update our local state with the server state
    this.content = state.content;
    this.version = state.version;
    
    // Update collaborators
    this.collaborators.clear();
    state.collaborators.forEach(collaborator => {
      this.collaborators.set(collaborator.user_id, collaborator);
    });

    this.onContentChange(this.content);
    this.onCollaboratorsChange(this.getCollaborators());
  }

  private handleOperationAck(result: OperationResult): void {
    if (result.success) {
      // Operation was successfully applied
      if (result.new_version) {
        this.version = result.new_version;
      }
      
      // Remove from pending operations if we can identify it
      // For now, we'll clear all pending operations on success
      this.pendingOperations.clear();
    } else {
      // Operation failed, handle error
      this.onError(result.error || 'Operation failed');
      
      // Request sync to get current state
      this.websocketClient.requestSync(this.pasteId);
    }
  }

  private applyTextOperations(content: string, operations: TextOperation[]): string {
    let result = content;
    
    // Apply operations in reverse order to maintain position integrity
    const sortedOps = [...operations].sort((a, b) => b.position - a.position);
    
    for (const op of sortedOps) {
      result = this.applyTextOperation(result, op);
    }
    
    return result;
  }

  private applyTextOperation(content: string, operation: TextOperation): string {
    const chars = Array.from(content); // Handle Unicode properly
    
    switch (operation.type) {
      case 'insert':
        if (operation.content) {
          const insertChars = Array.from(operation.content);
          chars.splice(operation.position, 0, ...insertChars);
        }
        break;
        
      case 'delete':
        if (operation.length) {
          chars.splice(operation.position, operation.length);
        }
        break;
        
      case 'retain':
        // Retain operations don't change content
        break;
        
      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
    
    return chars.join('');
  }

  // Cleanup method
  public destroy(): void {
    this.collaborators.clear();
    this.pendingOperations.clear();
  }
}