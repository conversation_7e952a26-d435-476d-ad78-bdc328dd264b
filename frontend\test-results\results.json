{"config": {"configFile": "C:\\Users\\<USER>\\Desktop\\Coding\\fullstack-pastify\\frontend\\playwright-simple.config.ts", "rootDir": "C:/Users/<USER>/Desktop/Coding/fullstack-pastify/frontend/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Desktop/Coding/fullstack-pastify/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Desktop/Coding/fullstack-pastify/frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 6, "webServer": null}, "suites": [{"title": "simple.spec.ts", "file": "simple.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Simple Test", "file": "simple.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should be able to navigate to a website", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "timedOut", "duration": 33702, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-15T14:38:48.766Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\Coding\\fullstack-pastify\\frontend\\test-results\\simple-Simple-Test-should-be-able-to-navigate-to-a-website-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\Coding\\fullstack-pastify\\frontend\\test-results\\simple-Simple-Test-should-be-able-to-navigate-to-a-website-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\Coding\\fullstack-pastify\\frontend\\test-results\\simple-Simple-Test-should-be-able-to-navigate-to-a-website-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "b095e72a376a9d09f724-a008e008a5dd5781f5ab", "file": "simple.spec.ts", "line": 4, "column": 3}, {"title": "should be able to check if localhost is accessible", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 27982, "errors": [], "stdout": [{"text": "Frontend not accessible: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"load\"\u001b[22m\n\n"}, {"text": "Backend not accessible: apiRequestContext.get: connect ECONNREFUSED ::1:8080\nCall log:\n\u001b[2m  - → GET http://localhost:8080/api/v1/health\u001b[22m\n\u001b[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.5 Safari/537.36\u001b[22m\n\u001b[2m    - accept: */*\u001b[22m\n\u001b[2m    - accept-encoding: gzip,deflate,br\u001b[22m\n\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-15T14:38:48.766Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b095e72a376a9d09f724-a49a8fa06f5068018a9c", "file": "simple.spec.ts", "line": 9, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-08-15T14:38:44.069Z", "duration": 46811.462, "expected": 1, "skipped": 0, "unexpected": 1, "flaky": 0}}