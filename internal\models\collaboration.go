package models

import (
	"encoding/json"
	"time"
)

// OperationType represents the type of text operation
type OperationType string

const (
	OperationTypeInsert OperationType = "insert"
	OperationTypeDelete OperationType = "delete"
	OperationTypeRetain OperationType = "retain"
)

// TextOperation represents a single text editing operation
type TextOperation struct {
	Type     OperationType `json:"type"`
	Position int           `json:"position"`
	Content  string        `json:"content,omitempty"` // For insert operations
	Length   int           `json:"length,omitempty"`  // For delete/retain operations
}

// Operation represents a collaborative editing operation
type Operation struct {
	ID        string          `json:"id"`
	PasteID   string          `json:"paste_id"`
	UserID    string          `json:"user_id"`
	Username  string          `json:"username"`
	Operations []TextOperation `json:"operations"`
	Timestamp time.Time       `json:"timestamp"`
	Version   int             `json:"version"` // Document version when operation was created
}

// CursorPosition represents a user's cursor position
type CursorPosition struct {
	Line   int `json:"line"`
	Column int `json:"column"`
	Offset int `json:"offset"` // Character offset from start of document
}

// Selection represents a user's text selection
type Selection struct {
	Start CursorPosition `json:"start"`
	End   CursorPosition `json:"end"`
}

// CollaboratorState represents the state of a collaborator
type CollaboratorState struct {
	UserID    string         `json:"user_id"`
	Username  string         `json:"username"`
	Color     string         `json:"color"`
	Cursor    CursorPosition `json:"cursor"`
	Selection *Selection     `json:"selection,omitempty"`
	LastSeen  time.Time      `json:"last_seen"`
}

// DocumentState represents the current state of a collaborative document
type DocumentState struct {
	PasteID       string              `json:"paste_id"`
	Content       string              `json:"content"`
	Version       int                 `json:"version"`
	Collaborators []CollaboratorState `json:"collaborators"`
	LastModified  time.Time           `json:"last_modified"`
}

// CollaborationMessage represents messages for collaborative editing
type CollaborationMessage struct {
	Type      string          `json:"type"`
	PasteID   string          `json:"paste_id"`
	UserID    string          `json:"user_id"`
	Username  string          `json:"username"`
	Operation *Operation      `json:"operation,omitempty"`
	Cursor    *CursorPosition `json:"cursor,omitempty"`
	Selection *Selection      `json:"selection,omitempty"`
	Data      json.RawMessage `json:"data,omitempty"`
	Timestamp time.Time       `json:"timestamp"`
}

// Add new message types for collaboration
const (
	MessageTypeOperation      MessageType = "operation"
	MessageTypeCursor         MessageType = "cursor"
	MessageTypeSelection      MessageType = "selection"
	MessageTypeSync           MessageType = "sync"
	MessageTypeAck            MessageType = "ack"
	MessageTypeConflict       MessageType = "conflict"
	MessageTypeDocumentState  MessageType = "document_state"
)

// OperationResult represents the result of applying an operation
type OperationResult struct {
	Success       bool        `json:"success"`
	NewContent    string      `json:"new_content,omitempty"`
	NewVersion    int         `json:"new_version,omitempty"`
	TransformedOp *Operation  `json:"transformed_operation,omitempty"`
	Error         string      `json:"error,omitempty"`
}

// ConflictResolution represents how to resolve operation conflicts
type ConflictResolution struct {
	OriginalOperation   *Operation `json:"original_operation"`
	ConflictingOperation *Operation `json:"conflicting_operation"`
	ResolvedOperation   *Operation `json:"resolved_operation"`
	Strategy            string     `json:"strategy"` // "transform", "reject", "merge"
}