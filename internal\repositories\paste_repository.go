package repositories

import (
	"database/sql"
	"time"
	"enhanced-pastebin/internal/models"
)

type PasteRepository interface {
	Create(paste *models.Paste) error
	GetByID(id string) (*models.Paste, error)
	GetByCustomURL(customURL string) (*models.Paste, error)
	Update(paste *models.Paste) error
	Delete(id string) error
	IncrementViewCount(id string) error
	GetByUserID(userID string, limit, offset int) ([]*models.Paste, error)
	GetExpiredPastes() ([]*models.Paste, error)
	GetExpiringPastes(cutoffTime time.Time) ([]*models.Paste, error)
}

type pasteRepository struct {
	db *sql.DB
}

func NewPasteRepository(db *sql.DB) PasteRepository {
	return &pasteRepository{db: db}
}

func (r *pasteRepository) Create(paste *models.Paste) error {
	query := `
		INSERT INTO pastes (id, custom_url, title, content, language, is_encrypted,
			expires_at, view_count, max_views, edit_count, is_watermarked, created_at, updated_at, user_id)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)`

	_, err := r.db.Exec(query, paste.ID, paste.CustomURL, paste.Title, paste.Content,
		paste.Language, paste.IsEncrypted, paste.ExpiresAt, paste.ViewCount,
		paste.MaxViews, paste.EditCount, paste.IsWatermarked, paste.CreatedAt, paste.UpdatedAt, paste.UserID)
	return err
}

func (r *pasteRepository) GetByID(id string) (*models.Paste, error) {
	paste := &models.Paste{}
	query := `
		SELECT id, custom_url, title, content, language, is_encrypted, expires_at,
			view_count, max_views, edit_count, is_watermarked, created_at, updated_at, user_id
		FROM pastes WHERE id = $1`

	err := r.db.QueryRow(query, id).Scan(
		&paste.ID, &paste.CustomURL, &paste.Title, &paste.Content, &paste.Language,
		&paste.IsEncrypted, &paste.ExpiresAt, &paste.ViewCount, &paste.MaxViews,
		&paste.EditCount, &paste.IsWatermarked, &paste.CreatedAt, &paste.UpdatedAt, &paste.UserID)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return paste, err
}

func (r *pasteRepository) GetByCustomURL(customURL string) (*models.Paste, error) {
	paste := &models.Paste{}
	query := `
		SELECT id, custom_url, title, content, language, is_encrypted, expires_at,
			view_count, max_views, edit_count, is_watermarked, created_at, updated_at, user_id
		FROM pastes WHERE custom_url = $1`

	err := r.db.QueryRow(query, customURL).Scan(
		&paste.ID, &paste.CustomURL, &paste.Title, &paste.Content, &paste.Language,
		&paste.IsEncrypted, &paste.ExpiresAt, &paste.ViewCount, &paste.MaxViews,
		&paste.EditCount, &paste.IsWatermarked, &paste.CreatedAt, &paste.UpdatedAt, &paste.UserID)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return paste, err
}

func (r *pasteRepository) Update(paste *models.Paste) error {
	query := `
		UPDATE pastes
		SET title = $2, content = $3, language = $4, expires_at = $5,
			max_views = $6, edit_count = $7, is_watermarked = $8, updated_at = $9
		WHERE id = $1`

	_, err := r.db.Exec(query, paste.ID, paste.Title, paste.Content, paste.Language,
		paste.ExpiresAt, paste.MaxViews, paste.EditCount, paste.IsWatermarked, paste.UpdatedAt)
	return err
}

func (r *pasteRepository) Delete(id string) error {
	query := `DELETE FROM pastes WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

func (r *pasteRepository) IncrementViewCount(id string) error {
	query := `UPDATE pastes SET view_count = view_count + 1 WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

func (r *pasteRepository) GetByUserID(userID string, limit, offset int) ([]*models.Paste, error) {
	query := `
		SELECT id, custom_url, title, content, language, is_encrypted, expires_at,
			view_count, max_views, edit_count, is_watermarked, created_at, updated_at, user_id
		FROM pastes WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3`

	rows, err := r.db.Query(query, userID, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var pastes []*models.Paste
	for rows.Next() {
		paste := &models.Paste{}
		err := rows.Scan(
			&paste.ID, &paste.CustomURL, &paste.Title, &paste.Content, &paste.Language,
			&paste.IsEncrypted, &paste.ExpiresAt, &paste.ViewCount, &paste.MaxViews,
			&paste.EditCount, &paste.IsWatermarked, &paste.CreatedAt, &paste.UpdatedAt, &paste.UserID)
		if err != nil {
			return nil, err
		}
		pastes = append(pastes, paste)
	}

	return pastes, rows.Err()
}

func (r *pasteRepository) GetExpiredPastes() ([]*models.Paste, error) {
	query := `
		SELECT id, custom_url, title, content, language, is_encrypted, expires_at,
			   view_count, max_views, edit_count, is_watermarked, created_at, updated_at, user_id
		FROM pastes
		WHERE (expires_at IS NOT NULL AND expires_at < NOW())
		   OR (max_views IS NOT NULL AND view_count >= max_views)`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var pastes []*models.Paste
	for rows.Next() {
		paste := &models.Paste{}
		err := rows.Scan(&paste.ID, &paste.CustomURL, &paste.Title, &paste.Content,
			&paste.Language, &paste.IsEncrypted, &paste.ExpiresAt, &paste.ViewCount,
			&paste.MaxViews, &paste.EditCount, &paste.IsWatermarked, &paste.CreatedAt, &paste.UpdatedAt,
			&paste.UserID)
		if err != nil {
			return nil, err
		}
		pastes = append(pastes, paste)
	}

	return pastes, rows.Err()
}

func (r *pasteRepository) GetExpiringPastes(cutoffTime time.Time) ([]*models.Paste, error) {
	query := `
		SELECT id, custom_url, title, content, language, is_encrypted, expires_at,
			   view_count, max_views, edit_count, is_watermarked, created_at, updated_at, user_id
		FROM pastes
		WHERE (expires_at IS NOT NULL AND expires_at BETWEEN NOW() AND $1)
		   OR (max_views IS NOT NULL AND view_count >= max_views - 5 AND view_count < max_views)`

	rows, err := r.db.Query(query, cutoffTime)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var pastes []*models.Paste
	for rows.Next() {
		paste := &models.Paste{}
		err := rows.Scan(&paste.ID, &paste.CustomURL, &paste.Title, &paste.Content,
			&paste.Language, &paste.IsEncrypted, &paste.ExpiresAt, &paste.ViewCount,
			&paste.MaxViews, &paste.EditCount, &paste.IsWatermarked, &paste.CreatedAt, &paste.UpdatedAt,
			&paste.UserID)
		if err != nil {
			return nil, err
		}
		pastes = append(pastes, paste)
	}

	return pastes, rows.Err()
}