package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	"enhanced-pastebin/internal/config"
	"enhanced-pastebin/internal/database"
	"enhanced-pastebin/internal/handlers"
	"enhanced-pastebin/internal/middleware"
	"enhanced-pastebin/internal/repositories"
	"enhanced-pastebin/internal/services"
	websocketPkg "enhanced-pastebin/internal/websocket"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Connect(cfg.DatabaseURL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Initialize WebSocket hub
	hub := websocketPkg.NewHub()
	go hub.Run()

	// Initialize repositories
	userRepo := repositories.NewUserRepository(db)
	pasteRepo := repositories.NewPasteRepository(db)
	chatRepo := repositories.NewChatRepository(db)
	versionRepo := repositories.NewVersionRepository(db)
	accessLogRepo := repositories.NewAccessLogRepository(db)
	watermarkRepo := repositories.NewWatermarkRepository(db)

	// Initialize services
	userService := services.NewUserService(userRepo, pasteRepo)
	pasteService := services.NewPasteService(pasteRepo, versionRepo)
	chatService := services.NewChatService(chatRepo)
	versionService := services.NewVersionService(versionRepo, pasteRepo)
	cleanupService := services.NewCleanupService(pasteRepo, chatRepo)
	accessLogService := services.NewAccessLogService(accessLogRepo)
	watermarkService := services.NewWatermarkService(watermarkRepo)
	securityService := services.NewSecurityService()
	dependencyService := services.NewDependencyService(securityService)
	collaborationService := services.NewCollaborationService()

	// Start cleanup worker
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	go cleanupService.StartCleanupWorker(ctx)

	// Initialize handlers
	userHandler := handlers.NewUserHandler(userService)
	pasteHandler := handlers.NewPasteHandler(pasteService, dependencyService, securityService)
	chatHandler := handlers.NewChatHandler(chatService, hub)
	versionHandler := handlers.NewVersionHandler(versionService)
	expirationHandler := handlers.NewExpirationHandler(cleanupService)
	accessLogHandler := handlers.NewAccessLogHandler(accessLogService, watermarkService)
	wsHandler := handlers.NewWebSocketHandler(hub, collaborationService)

	// Setup router
	router := gin.Default()

	// Add middleware
	router.Use(middleware.CORS())
	router.Use(middleware.Logger())
	router.Use(middleware.AccessLogger(middleware.AccessLoggerConfig{
		AccessLogService: accessLogService,
		SkipPaths:        []string{"/api/v1/health", "/api/v1/ws"},
		LogBody:          false,
	}))

	// API routes
	api := router.Group("/api/v1")
	{
		// Health check
		api.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{"status": "ok"})
		})

		// User routes
		users := api.Group("/users")
		{
			users.POST("/register", userHandler.Register)
			users.POST("/login", userHandler.Login)
			users.GET("/profile", middleware.AuthRequired(), userHandler.GetProfile)
			users.GET("/profile/pastes", middleware.AuthRequired(), userHandler.GetUserPastes)
			users.GET("/:username/public", userHandler.GetPublicProfile)
		}

		// Paste routes
		pastes := api.Group("/pastes")
		{
			pastes.POST("/", pasteHandler.CreatePaste)
			pastes.GET("/check-url", pasteHandler.CheckCustomURLAvailability)
			pastes.GET("/:id", pasteHandler.GetPaste)
			pastes.GET("/:id/dependencies", pasteHandler.GetPasteDependencies)
			pastes.GET("/:id/security", pasteHandler.GetPasteSecurityScan)
			pastes.PUT("/:id", middleware.AuthRequired(), pasteHandler.UpdatePaste)
			pastes.DELETE("/:id", middleware.AuthRequired(), pasteHandler.DeletePaste)
		}

		// Custom URL route (should be after /pastes/:id to avoid conflicts)
		api.GET("/p/:customUrl", pasteHandler.GetPasteByCustomURL)

		// Chat routes
		chat := api.Group("/chat")
		{
			chat.POST("/messages", middleware.AuthRequired(), chatHandler.CreateMessage)
			chat.GET("/pastes/:pasteId/messages", chatHandler.GetMessages)
			chat.DELETE("/messages/:messageId", middleware.AuthRequired(), chatHandler.DeleteMessage)
		}

		// Version routes
		versions := api.Group("/versions")
		{
			versions.GET("/pastes/:pasteId", versionHandler.GetVersions)
			versions.GET("/:versionId", versionHandler.GetVersion)
			versions.GET("/pastes/:pasteId/latest", versionHandler.GetLatestVersion)
			versions.GET("/compare", versionHandler.CompareVersions)
		}

		// Expiration routes
		expiration := api.Group("/expiration")
		{
			expiration.GET("/expiring", expirationHandler.GetExpiringPastes)
			expiration.POST("/cleanup", middleware.AuthRequired(), expirationHandler.TriggerCleanup)
		}

		// Access logging and audit routes
		audit := api.Group("/audit")
		{
			audit.GET("/pastes/:pasteId/logs", middleware.AuthRequired(), accessLogHandler.GetAccessLogs)
			audit.GET("/user/logs", middleware.AuthRequired(), accessLogHandler.GetUserAccessLogs)
			audit.GET("/security-events", middleware.AuthRequired(), accessLogHandler.GetSecurityEvents)
			audit.GET("/pastes/:pasteId/watermarks", middleware.AuthRequired(), accessLogHandler.GetWatermarks)
			audit.GET("/user/watermarks", middleware.AuthRequired(), accessLogHandler.GetUserWatermarks)
			audit.POST("/watermark/extract", middleware.AuthRequired(), accessLogHandler.ExtractWatermark)
		}

		// WebSocket routes
		ws := api.Group("/ws")
		{
			ws.GET("/connect", wsHandler.HandleWebSocket)
			ws.GET("/stats", wsHandler.GetRoomStats)
		}
	}

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	// Setup graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		log.Printf("Server starting on port %s", port)
		if err := router.Run(":" + port); err != nil {
			log.Fatal("Failed to start server:", err)
		}
	}()

	// Wait for shutdown signal
	<-quit
	log.Println("Shutting down server...")

	// Cancel cleanup worker
	cancel()

	log.Println("Server stopped")
}