package models

import (
	"testing"
	"time"

	"enhanced-pastebin/internal/utils"
)

func TestUserValidation(t *testing.T) {
	tests := []struct {
		name    string
		user    User
		wantErr bool
	}{
		{
			name: "valid user",
			user: User{
				ID:             "550e8400-e29b-41d4-a716-446655440000",
				Username:       "testuser",
				Email:          "<EMAIL>",
				PasswordHash:   "hashedpassword",
				ReputationScore: 0,
				CreatedAt:      time.Now(),
				LastActive:     time.Now(),
			},
			wantErr: false,
		},
		{
			name: "invalid email",
			user: User{
				ID:             "550e8400-e29b-41d4-a716-446655440000",
				Username:       "testuser",
				Email:          "invalid-email",
				PasswordHash:   "hashedpassword",
				ReputationScore: 0,
				CreatedAt:      time.Now(),
				LastActive:     time.Now(),
			},
			wantErr: true,
		},
		{
			name: "short username",
			user: User{
				ID:             "550e8400-e29b-41d4-a716-446655440000",
				Username:       "ab",
				Email:          "<EMAIL>",
				PasswordHash:   "hashedpassword",
				ReputationScore: 0,
				CreatedAt:      time.Now(),
				LastActive:     time.Now(),
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := utils.ValidateStruct(tt.user)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateStruct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCreateUserRequestValidation(t *testing.T) {
	tests := []struct {
		name    string
		req     CreateUserRequest
		wantErr bool
	}{
		{
			name: "valid request",
			req: CreateUserRequest{
				Username: "testuser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			wantErr: false,
		},
		{
			name: "short password",
			req: CreateUserRequest{
				Username: "testuser",
				Email:    "<EMAIL>",
				Password: "123",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := utils.ValidateStruct(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateStruct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPasteValidation(t *testing.T) {
	tests := []struct {
		name    string
		paste   Paste
		wantErr bool
	}{
		{
			name: "valid paste",
			paste: Paste{
				ID:        "550e8400-e29b-41d4-a716-446655440000",
				Title:     "Test Paste",
				Content:   "console.log('hello world');",
				Language:  "javascript",
				ViewCount: 0,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			wantErr: false,
		},
		{
			name: "empty title",
			paste: Paste{
				ID:        "550e8400-e29b-41d4-a716-446655440000",
				Title:     "",
				Content:   "console.log('hello world');",
				Language:  "javascript",
				ViewCount: 0,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := utils.ValidateStruct(tt.paste)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateStruct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}