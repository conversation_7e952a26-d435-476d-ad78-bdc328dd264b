# Playwright E2E Tests

This directory contains end-to-end tests for the Enhanced Pastebin frontend application using Playwright.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ installed
- Docker and Docker Compose (for backend services)
- Go 1.21+ (for backend server)

### Installation

1. Install Playwright dependencies:
```bash
cd frontend
npm install
npx playwright install
```

2. Start the backend services:
```bash
# From project root
make setup  # Sets up environment and starts database
make migrate-up  # Run database migrations
```

### Running Tests

```bash
# Run all tests
npm run test:e2e

# Run tests in headed mode (see browser)
npm run test:e2e:headed

# Run tests with UI mode
npm run test:e2e:ui

# Run specific test file
npx playwright test auth/login.spec.ts

# Run tests in debug mode
npm run test:e2e:debug
```

## 📁 Project Structure

```
tests/
├── e2e/                    # End-to-end test files
│   ├── auth/              # Authentication tests
│   │   ├── login.spec.ts
│   │   ├── registration.spec.ts
│   │   ├── logout.spec.ts
│   │   └── protected-routes.spec.ts
│   ├── pastes/            # Paste management tests
│   │   ├── create-paste.spec.ts
│   │   ├── view-paste.spec.ts
│   │   └── edit-delete-paste.spec.ts
│   └── demo.spec.ts       # Demo test showing complete flow
├── pages/                 # Page Object Models
│   ├── base-page.ts       # Base page class
│   ├── home-page.ts       # Home page
│   ├── login-page.ts      # Login page
│   ├── register-page.ts   # Registration page
│   ├── create-paste-page.ts # Create paste page
│   ├── view-paste-page.ts # View paste page
│   └── index.ts           # Page exports
├── utils/                 # Test utilities
│   ├── test-environment.ts # Environment management
│   ├── database.ts        # Database utilities
│   ├── api-client.ts      # API client for tests
│   ├── test-helpers.ts    # Common test helpers
│   └── test-data-manager.ts # Test data management
├── fixtures/              # Test data fixtures
│   ├── users.ts           # User test data
│   └── pastes.ts          # Paste test data
├── global-setup.ts        # Global test setup
├── global-teardown.ts     # Global test teardown
└── README.md              # This file
```

## 🧪 Test Categories

### Authentication Tests (`auth/`)
- User registration with validation
- User login/logout flows
- Protected route access
- Session management
- Error handling

### Paste Management Tests (`pastes/`)
- Creating pastes with various options
- Viewing public and private pastes
- Editing and deleting pastes
- Custom URLs and expiration
- Language detection and syntax highlighting

### Demo Tests (`demo.spec.ts`)
- Complete user journey examples
- Integration with test data manager
- Backend health checks

## 🏗️ Architecture

### Page Object Model
Tests use the Page Object Model pattern for maintainable and reusable code:

```typescript
// Example usage
const loginPage = new LoginPage(page);
await loginPage.visit();
await loginPage.login('username', 'password');
await loginPage.expectToBeLoggedIn();
```

### Test Data Management
The `TestDataManager` provides utilities for creating and cleaning up test data:

```typescript
const dataManager = createTestDataManager(apiClient);
const { user, pastes } = await dataManager.createUserWithPastes(5);
// ... run tests
await dataManager.cleanupAll();
```

### API Client
Direct API access for test setup and verification:

```typescript
const apiClient = new TestApiClient();
const user = await apiClient.createTestUser();
const paste = await apiClient.createTestPaste();
```

## ⚙️ Configuration

### Playwright Configuration (`playwright.config.ts`)
- Runs tests against `http://localhost:3000` (frontend)
- Automatically starts frontend dev server
- Configures multiple browsers (Chrome, Firefox, Safari)
- Sets up global setup/teardown for backend services

### Environment Variables
Tests use these environment variables:
- `DATABASE_URL`: PostgreSQL connection string
- `JWT_SECRET`: JWT secret for authentication
- `PORT`: Backend server port (default: 8080)

## 🔧 Backend Setup

The tests automatically manage the backend through global setup:

1. **Database Services**: Starts PostgreSQL and Redis via Docker Compose
2. **Migrations**: Runs database migrations
3. **Backend Server**: Starts the Go backend server
4. **Health Checks**: Waits for services to be ready

### Manual Backend Setup
If you prefer to manage the backend manually:

```bash
# Start database services
docker-compose up -d postgres redis

# Run migrations
make migrate-up

# Start backend server
make dev-backend
```

Then set `reuseExistingServer: true` in `playwright.config.ts`.

## 📊 Test Reports

Playwright generates multiple report formats:
- **HTML Report**: Interactive report with screenshots and videos
- **JSON Report**: Machine-readable results
- **JUnit Report**: For CI/CD integration

View reports:
```bash
npm run test:e2e:report
```

## 🐛 Debugging

### Debug Mode
```bash
npm run test:e2e:debug
```

### Screenshots and Videos
- Screenshots taken on test failure
- Videos recorded for failed tests
- Traces captured for debugging

### Verbose Logging
```bash
DEBUG=pw:api npx playwright test
```

## 🚀 CI/CD Integration

### GitHub Actions Example
```yaml
name: E2E Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Install dependencies
        run: |
          cd frontend
          npm ci
          npx playwright install --with-deps
      - name: Start services
        run: |
          docker-compose up -d
          make migrate-up
      - name: Run tests
        run: |
          cd frontend
          npm run test:e2e
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: frontend/playwright-report/
```

## 📝 Writing Tests

### Basic Test Structure
```typescript
import { test, expect } from '@playwright/test';
import { HomePage } from '../pages';

test.describe('Feature Name', () => {
  let homePage: HomePage;

  test.beforeEach(async ({ page }) => {
    homePage = new HomePage(page);
    await homePage.visit();
  });

  test('should do something', async () => {
    // Test implementation
    await homePage.expectToBeOnHomePage();
  });
});
```

### Using Test Data
```typescript
import { TestHelpers } from '../utils/test-helpers';

test('test with data', async ({ page }) => {
  const helpers = new TestHelpers();
  const user = await helpers.loginAsTestUser(page);
  const paste = await helpers.createTestPasteViaAPI();
  
  // Test implementation
});
```

## 🔍 Best Practices

1. **Use Page Objects**: Encapsulate page interactions in page object classes
2. **Clean Test Data**: Always clean up test data after tests
3. **Stable Selectors**: Use data-testid attributes for reliable element selection
4. **Wait Strategies**: Use appropriate wait strategies for dynamic content
5. **Error Handling**: Test both success and error scenarios
6. **Isolation**: Keep tests independent and isolated

## 🆘 Troubleshooting

### Common Issues

**Backend not starting:**
- Check if ports 8080, 5432, 6379 are available
- Verify Docker is running
- Check database connection string

**Tests timing out:**
- Increase timeout in playwright.config.ts
- Check if backend services are healthy
- Verify network connectivity

**Flaky tests:**
- Add proper wait conditions
- Use stable selectors
- Check for race conditions

### Getting Help
- Check test logs and screenshots in `test-results/`
- Use debug mode to step through tests
- Review Playwright documentation: https://playwright.dev/
