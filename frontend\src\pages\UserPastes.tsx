import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { FileText, Eye, Code, Calendar, Plus, ArrowLeft, Lock } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/contexts/AuthContext'
import { userAPI, Paste } from '@/lib/api'

export default function UserPastes() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [pastes, setPastes] = useState<Paste[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [hasMore, setHasMore] = useState(true)
  const [offset, setOffset] = useState(0)
  const limit = 20

  useEffect(() => {
    if (user) {
      loadPastes(0, true)
    }
  }, [user])

  const loadPastes = async (newOffset: number = offset, reset: boolean = false) => {
    try {
      setIsLoading(true)
      const response = await userAPI.getUserPastes(limit, newOffset)
      const newPastes = response.data.pastes

      if (reset) {
        setPastes(newPastes)
      } else {
        setPastes(prev => [...prev, ...newPastes])
      }

      setHasMore(newPastes.length === limit)
      setOffset(newOffset + limit)
    } catch (error) {
      console.error('Error loading pastes:', error)
      toast({
        title: "Error",
        description: "Failed to load your pastes",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const loadMore = () => {
    if (!isLoading && hasMore) {
      loadPastes()
    }
  }

  if (!user) {
    return (
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardContent className="p-8">
            <div className="text-center">Please log in to view your pastes</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm" asChild>
                <Link to="/profile">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Profile
                </Link>
              </Button>
              <div>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  My Pastes
                </CardTitle>
                <CardDescription>All your code snippets</CardDescription>
              </div>
            </div>
            <Button asChild>
              <Link to="/create">
                <Plus className="h-4 w-4 mr-2" />
                Create New
              </Link>
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Pastes List */}
      <Card>
        <CardContent className="p-6">
          {isLoading && pastes.length === 0 ? (
            <div className="text-center py-8">Loading your pastes...</div>
          ) : pastes.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No pastes yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first paste to get started
              </p>
              <Button asChild>
                <Link to="/create">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Paste
                </Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {pastes.map((paste) => (
                <div key={paste.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <Link 
                            to={`/paste/${paste.id}`}
                            className="font-medium hover:underline truncate"
                          >
                            {paste.title}
                          </Link>
                          {paste.is_encrypted && (
                            <Lock className="h-4 w-4 text-muted-foreground" />
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                          <div className="flex items-center space-x-1">
                            <Code className="h-3 w-3" />
                            <span>{paste.language}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Eye className="h-3 w-3" />
                            <span>{paste.view_count} views</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>{new Date(paste.created_at).toLocaleDateString()}</span>
                          </div>
                          {paste.updated_at !== paste.created_at && (
                            <span className="text-xs">
                              (updated {new Date(paste.updated_at).toLocaleDateString()})
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      {paste.is_encrypted && (
                        <Badge variant="secondary">Encrypted</Badge>
                      )}
                      {paste.custom_url && (
                        <Badge variant="outline">Custom URL</Badge>
                      )}
                      {paste.expires_at && (
                        <Badge variant="destructive">Expires</Badge>
                      )}
                      {paste.max_views && (
                        <Badge variant="warning">
                          {paste.max_views - paste.view_count} views left
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Load More Button */}
              {hasMore && (
                <div className="text-center pt-4">
                  <Button 
                    variant="outline" 
                    onClick={loadMore}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Loading...' : 'Load More'}
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
