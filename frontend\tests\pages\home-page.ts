import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base-page';

export class HomePage extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  // Page-specific locators
  get welcomeMessage(): Locator {
    return this.page.locator('h1, .welcome-message').first();
  }

  get featuresSection(): Locator {
    return this.page.locator('.features, [data-testid="features"]').first();
  }

  get recentPastesSection(): Locator {
    return this.page.locator('.recent-pastes, [data-testid="recent-pastes"]').first();
  }

  get publicPastesList(): Locator {
    return this.page.locator('.paste-list, [data-testid="paste-list"]').first();
  }

  get pasteItems(): Locator {
    return this.publicPastesList.locator('.paste-item, [data-testid="paste-item"]');
  }

  get createPasteButton(): Locator {
    return this.page.locator('button:has-text("Create Paste"), a:has-text("Create Paste")').first();
  }

  get searchInput(): Locator {
    return this.page.locator('input[type="search"], input[placeholder*="search" i]').first();
  }

  get searchButton(): Locator {
    return this.page.locator('button:has-text("Search")').first();
  }

  get languageFilter(): Locator {
    return this.page.locator('select[name="language"], [data-testid="language-filter"]').first();
  }

  get sortOptions(): Locator {
    return this.page.locator('select[name="sort"], [data-testid="sort-options"]').first();
  }

  // Page actions
  async visit(): Promise<void> {
    await this.page.goto('/');
    await this.waitForPageLoad();
  }

  async clickCreatePaste(): Promise<void> {
    await this.createPasteButton.click();
    await this.page.waitForURL('/create');
  }

  async searchPastes(query: string): Promise<void> {
    await this.searchInput.fill(query);
    await this.searchButton.click();
    await this.waitForPageLoad();
  }

  async filterByLanguage(language: string): Promise<void> {
    await this.languageFilter.selectOption(language);
    await this.waitForPageLoad();
  }

  async sortPastes(sortOption: string): Promise<void> {
    await this.sortOptions.selectOption(sortOption);
    await this.waitForPageLoad();
  }

  async clickPasteItem(index: number): Promise<void> {
    const pasteItem = this.pasteItems.nth(index);
    await pasteItem.click();
    await this.page.waitForURL(/\/paste\/\w+/);
  }

  async clickPasteByTitle(title: string): Promise<void> {
    const pasteItem = this.pasteItems.filter({ hasText: title }).first();
    await pasteItem.click();
    await this.page.waitForURL(/\/paste\/\w+/);
  }

  // Page assertions
  async expectToBeOnHomePage(): Promise<void> {
    await expect(this.page).toHaveURL('/');
    await expect(this.page).toHaveTitle(/Enhanced Pastebin/);
  }

  async expectWelcomeMessage(): Promise<void> {
    await expect(this.welcomeMessage).toBeVisible();
    await expect(this.welcomeMessage).toContainText(/welcome|pastebin/i);
  }

  async expectFeaturesSection(): Promise<void> {
    await expect(this.featuresSection).toBeVisible();
  }

  async expectRecentPastesSection(): Promise<void> {
    await expect(this.recentPastesSection).toBeVisible();
  }

  async expectPastesList(): Promise<void> {
    await expect(this.publicPastesList).toBeVisible();
  }

  async expectPastesCount(count: number): Promise<void> {
    await expect(this.pasteItems).toHaveCount(count);
  }

  async expectPasteInList(title: string): Promise<void> {
    const pasteItem = this.pasteItems.filter({ hasText: title });
    await expect(pasteItem).toBeVisible();
  }

  async expectNoPastesMessage(): Promise<void> {
    const noPastesMessage = this.page.locator('.no-pastes, [data-testid="no-pastes"]');
    await expect(noPastesMessage).toBeVisible();
  }

  async expectSearchResults(query: string): Promise<void> {
    // Expect search results to be displayed
    const searchResults = this.page.locator('.search-results, [data-testid="search-results"]');
    await expect(searchResults).toBeVisible();
    
    // Expect search query to be highlighted or mentioned
    const searchQuery = this.page.locator(`:has-text("${query}")`);
    await expect(searchQuery).toBeVisible();
  }

  async expectLanguageFilter(language: string): Promise<void> {
    await expect(this.languageFilter).toHaveValue(language);
    
    // Expect pastes to be filtered by language
    const languageLabels = this.pasteItems.locator('.language, [data-testid="language"]');
    const count = await languageLabels.count();
    
    for (let i = 0; i < count; i++) {
      await expect(languageLabels.nth(i)).toContainText(language);
    }
  }

  // Utility methods
  async getPasteCount(): Promise<number> {
    return await this.pasteItems.count();
  }

  async getPasteTitles(): Promise<string[]> {
    const titles: string[] = [];
    const count = await this.pasteItems.count();
    
    for (let i = 0; i < count; i++) {
      const titleElement = this.pasteItems.nth(i).locator('.title, h3, [data-testid="paste-title"]').first();
      const title = await titleElement.textContent();
      if (title) {
        titles.push(title.trim());
      }
    }
    
    return titles;
  }

  async getPasteLanguages(): Promise<string[]> {
    const languages: string[] = [];
    const count = await this.pasteItems.count();
    
    for (let i = 0; i < count; i++) {
      const languageElement = this.pasteItems.nth(i).locator('.language, [data-testid="language"]').first();
      const language = await languageElement.textContent();
      if (language) {
        languages.push(language.trim());
      }
    }
    
    return languages;
  }

  async waitForPastesToLoad(): Promise<void> {
    // Wait for either pastes to load or no-pastes message to appear
    await Promise.race([
      this.pasteItems.first().waitFor(),
      this.page.locator('.no-pastes, [data-testid="no-pastes"]').waitFor()
    ]);
  }
}
