package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"enhanced-pastebin/internal/utils"

	"github.com/gin-gonic/gin"
)

func TestAuthRequired_ValidToken(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	// Generate a valid token
	userID := "test-user-id"
	token, err := utils.GenerateJWT(userID)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// Create test router
	router := gin.New()
	router.Use(AuthRequired())
	router.GET("/protected", func(c *gin.Context) {
		// Check if user_id was set by middleware
		if uid, exists := c.Get("user_id"); exists {
			c.JSON(http.StatusOK, gin.H{"user_id": uid})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "user_id not set"})
		}
	})

	// Create request with valid token
	req := httptest.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	
	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	// Check if user_id was passed through
	if !contains(w.Body.String(), userID) {
		t.Errorf("Expected response to contain user ID %s, got %s", userID, w.Body.String())
	}
}

func TestAuthRequired_MissingToken(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	// Create test router
	router := gin.New()
	router.Use(AuthRequired())
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create request without token
	req := httptest.NewRequest("GET", "/protected", nil)
	
	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusUnauthorized {
		t.Errorf("Expected status 401, got %d", w.Code)
	}

	if !contains(w.Body.String(), "Authorization header required") {
		t.Errorf("Expected error message about missing authorization header, got %s", w.Body.String())
	}
}

func TestAuthRequired_InvalidTokenFormat(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	// Create test router
	router := gin.New()
	router.Use(AuthRequired())
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create request with invalid token format (missing "Bearer ")
	req := httptest.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "invalid-token-format")
	
	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusUnauthorized {
		t.Errorf("Expected status 401, got %d", w.Code)
	}

	if !contains(w.Body.String(), "Bearer token required") {
		t.Errorf("Expected error message about Bearer token, got %s", w.Body.String())
	}
}

func TestAuthRequired_InvalidToken(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	// Create test router
	router := gin.New()
	router.Use(AuthRequired())
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create request with invalid token
	req := httptest.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "Bearer invalid.token.here")
	
	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusUnauthorized {
		t.Errorf("Expected status 401, got %d", w.Code)
	}

	if !contains(w.Body.String(), "Invalid token") {
		t.Errorf("Expected error message about invalid token, got %s", w.Body.String())
	}
}

// Helper function to check if string contains substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 || 
		(len(s) > len(substr) && (s[:len(substr)] == substr || 
		s[len(s)-len(substr):] == substr || 
		containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}