import { spawn, ChildProcess } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export interface TestEnvironmentConfig {
  backendUrl: string;
  frontendUrl: string;
  databaseUrl: string;
  jwtSecret: string;
}

export const DEFAULT_TEST_CONFIG: TestEnvironmentConfig = {
  backendUrl: 'http://localhost:8080',
  frontendUrl: 'http://localhost:3000',
  databaseUrl: 'postgres://postgres:password@localhost:5432/enhanced_pastebin?sslmode=disable',
  jwtSecret: 'test-jwt-secret-key-for-playwright-tests'
};

export class TestEnvironment {
  private backendProcess: ChildProcess | null = null;
  private config: TestEnvironmentConfig;

  constructor(config: TestEnvironmentConfig = DEFAULT_TEST_CONFIG) {
    this.config = config;
  }

  async startBackend(): Promise<void> {
    if (this.backendProcess) {
      throw new Error('Backend is already running');
    }

    return new Promise((resolve, reject) => {
      const projectRoot = path.resolve(__dirname, '../../../..');
      
      this.backendProcess = spawn('go', ['run', 'cmd/server/main.go'], {
        cwd: projectRoot,
        stdio: 'pipe',
        shell: true,
        env: {
          ...process.env,
          DATABASE_URL: this.config.databaseUrl,
          JWT_SECRET: this.config.jwtSecret,
          PORT: '8080',
          GIN_MODE: 'release'
        }
      });

      let output = '';
      this.backendProcess.stdout?.on('data', (data) => {
        const text = data.toString();
        output += text;
        if (text.includes('Server starting on port 8080')) {
          resolve();
        }
      });

      this.backendProcess.stderr?.on('data', (data) => {
        output += data.toString();
      });

      this.backendProcess.on('close', (code) => {
        if (code !== 0 && code !== null) {
          reject(new Error(`Backend server failed to start: ${output}`));
        }
      });

      this.backendProcess.on('error', (error) => {
        reject(new Error(`Failed to start backend server: ${error.message}`));
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        if (this.backendProcess && !this.backendProcess.killed) {
          reject(new Error('Backend server failed to start within timeout'));
        }
      }, 30000);
    });
  }

  async stopBackend(): Promise<void> {
    if (!this.backendProcess) {
      return;
    }

    return new Promise((resolve) => {
      if (!this.backendProcess) {
        resolve();
        return;
      }

      this.backendProcess.on('close', () => {
        this.backendProcess = null;
        resolve();
      });

      // Try graceful shutdown first
      this.backendProcess.kill('SIGTERM');

      // Force kill after 5 seconds if still running
      setTimeout(() => {
        if (this.backendProcess && !this.backendProcess.killed) {
          this.backendProcess.kill('SIGKILL');
          this.backendProcess = null;
          resolve();
        }
      }, 5000);
    });
  }

  async waitForBackend(maxAttempts: number = 30, delay: number = 1000): Promise<void> {
    for (let i = 0; i < maxAttempts; i++) {
      try {
        const response = await fetch(`${this.config.backendUrl}/api/v1/health`);
        if (response.ok) {
          return;
        }
      } catch (error) {
        // Continue trying
      }

      await new Promise(resolve => setTimeout(resolve, delay));
    }

    throw new Error('Backend server failed to become ready within timeout');
  }

  async isBackendRunning(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.backendUrl}/api/v1/health`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  getConfig(): TestEnvironmentConfig {
    return { ...this.config };
  }
}
