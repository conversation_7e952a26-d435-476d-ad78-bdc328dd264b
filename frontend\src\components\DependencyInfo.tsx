import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ExternalLink, Package, Copy, Alert<PERSON><PERSON>gle, Shield, ShieldAlert, ShieldCheck } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export interface SecurityVulnerability {
  id: string
  title: string
  description: string
  severity: string
  cvss?: number
  cve?: string
  cwe?: string
  url?: string
  published_at: string
  patched_in?: string
}

export interface DependencySecurityInfo {
  package_name: string
  version?: string
  vulnerabilities: SecurityVulnerability[]
  security_score: number
  last_scanned: string
  has_vulnerabilities: boolean
}

export interface Dependency {
  name: string
  version?: string
  type: string
  language: string
  registry?: string
  registry_url?: string
  install_cmd?: string
  security?: DependencySecurityInfo
}

export interface DependencyInfoData {
  dependencies: Dependency[]
  language: string
  total_count: number
  vulnerability_count?: number
  critical_count?: number
  high_count?: number
  moderate_count?: number
  low_count?: number
  security_score?: number
  last_scanned?: string
}

interface DependencyInfoProps {
  pasteId: string
  language: string
  content?: string // For encrypted pastes, we can pass the decrypted content
  includeSecurity?: boolean // Whether to include security scanning
}

export default function DependencyInfo({ pasteId, language, content, includeSecurity = false }: DependencyInfoProps) {
  const [dependencyInfo, setDependencyInfo] = useState<DependencyInfoData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isExpanded, setIsExpanded] = useState(false)
  const [showSecurityDetails, setShowSecurityDetails] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    const fetchDependencies = async () => {
      if (!pasteId || !language) return

      setIsLoading(true)
      setError(null)

      try {
        let response
        const securityParam = includeSecurity ? '?security=true' : ''
        if (content) {
          // For encrypted content, we need to analyze it client-side
          // For now, we'll use the server endpoint but this could be enhanced
          // to do client-side analysis for encrypted content
          response = await fetch(`/api/v1/pastes/${pasteId}/dependencies${securityParam}`)
        } else {
          response = await fetch(`/api/v1/pastes/${pasteId}/dependencies${securityParam}`)
        }
        
        if (!response.ok) {
          throw new Error('Failed to fetch dependencies')
        }

        const data: DependencyInfoData = await response.json()
        setDependencyInfo(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load dependencies')
      } finally {
        setIsLoading(false)
      }
    }

    fetchDependencies()
  }, [pasteId, language, content, includeSecurity])

  const copyInstallCommand = async (command: string) => {
    try {
      await navigator.clipboard.writeText(command)
      toast({
        title: "Copied",
        description: "Install command copied to clipboard",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      })
    }
  }

  const copyAllCommands = async () => {
    if (!dependencyInfo?.dependencies.length) return

    const commands = dependencyInfo.dependencies
      .filter(dep => dep.install_cmd)
      .map(dep => dep.install_cmd)
      .join('\n')

    try {
      await navigator.clipboard.writeText(commands)
      toast({
        title: "Copied",
        description: "All install commands copied to clipboard",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Package className="h-4 w-4 animate-pulse" />
            <span className="text-sm text-muted-foreground">Analyzing dependencies...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="border-yellow-200 bg-yellow-50">
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <span className="text-sm text-yellow-800">Unable to analyze dependencies: {error}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!dependencyInfo || dependencyInfo.total_count === 0) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Package className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              No dependencies detected for {language}
            </span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <CardTitle className="text-lg">
              Dependencies ({dependencyInfo.total_count})
            </CardTitle>
            {includeSecurity && dependencyInfo.vulnerability_count !== undefined && (
              <div className="flex items-center space-x-2 ml-4">
                {dependencyInfo.vulnerability_count > 0 ? (
                  <div className="flex items-center space-x-1">
                    <ShieldAlert className="h-4 w-4 text-red-500" />
                    <span className="text-sm text-red-600 font-medium">
                      {dependencyInfo.vulnerability_count} vulnerabilities
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-1">
                    <ShieldCheck className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-600 font-medium">
                      No vulnerabilities
                    </span>
                  </div>
                )}
                {dependencyInfo.security_score !== undefined && (
                  <span className={`text-xs px-2 py-1 rounded ${
                    dependencyInfo.security_score >= 80 ? 'bg-green-100 text-green-800' :
                    dependencyInfo.security_score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    Score: {dependencyInfo.security_score}/100
                  </span>
                )}
              </div>
            )}
          </div>
          <div className="flex space-x-2">
            {includeSecurity && dependencyInfo.vulnerability_count !== undefined && dependencyInfo.vulnerability_count > 0 && (
              <Button
                onClick={() => setShowSecurityDetails(!showSecurityDetails)}
                variant="outline"
                size="sm"
              >
                <Shield className="h-4 w-4 mr-2" />
                {showSecurityDetails ? 'Hide' : 'Show'} Security Details
              </Button>
            )}
            {dependencyInfo.dependencies.some(dep => dep.install_cmd) && (
              <Button
                onClick={copyAllCommands}
                variant="outline"
                size="sm"
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy All Commands
              </Button>
            )}
            <Button
              onClick={() => setIsExpanded(!isExpanded)}
              variant="outline"
              size="sm"
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {dependencyInfo.dependencies.slice(0, isExpanded ? undefined : 5).map((dep, index) => (
            <div
              key={`${dep.name}-${index}`}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-sm">{dep.name}</span>
                  {dep.version && (
                    <span className="text-xs text-muted-foreground bg-gray-200 px-2 py-1 rounded">
                      {dep.version}
                    </span>
                  )}
                  <span className="text-xs text-muted-foreground bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {dep.type}
                  </span>
                  {dep.registry && (
                    <span className="text-xs text-muted-foreground bg-green-100 text-green-800 px-2 py-1 rounded">
                      {dep.registry}
                    </span>
                  )}
                  {includeSecurity && dep.security && (
                    <>
                      {dep.security.has_vulnerabilities ? (
                        <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded flex items-center space-x-1">
                          <AlertTriangle className="h-3 w-3" />
                          <span>{dep.security.vulnerabilities.length} vuln{dep.security.vulnerabilities.length !== 1 ? 's' : ''}</span>
                        </span>
                      ) : (
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded flex items-center space-x-1">
                          <ShieldCheck className="h-3 w-3" />
                          <span>Secure</span>
                        </span>
                      )}
                      <span className={`text-xs px-2 py-1 rounded ${
                        dep.security.security_score >= 80 ? 'bg-green-100 text-green-800' :
                        dep.security.security_score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {dep.security.security_score}/100
                      </span>
                    </>
                  )}
                </div>
                {dep.install_cmd && (
                  <div className="mt-2 flex items-center space-x-2">
                    <code className="text-xs bg-gray-800 text-gray-100 px-2 py-1 rounded font-mono">
                      {dep.install_cmd}
                    </code>
                    <Button
                      onClick={() => copyInstallCommand(dep.install_cmd!)}
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                )}
                {includeSecurity && showSecurityDetails && dep.security && dep.security.has_vulnerabilities && (
                  <div className="mt-3 space-y-2">
                    <div className="text-xs font-medium text-red-700">Security Vulnerabilities:</div>
                    {dep.security.vulnerabilities.slice(0, 3).map((vuln, vulnIndex) => (
                      <div key={vulnIndex} className="bg-red-50 border border-red-200 rounded p-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className={`text-xs px-2 py-1 rounded font-medium ${
                              vuln.severity === 'critical' ? 'bg-red-600 text-white' :
                              vuln.severity === 'high' ? 'bg-red-500 text-white' :
                              vuln.severity === 'moderate' || vuln.severity === 'medium' ? 'bg-yellow-500 text-white' :
                              'bg-gray-500 text-white'
                            }`}>
                              {vuln.severity.toUpperCase()}
                            </span>
                            {vuln.cve && (
                              <span className="text-xs bg-gray-200 text-gray-800 px-2 py-1 rounded font-mono">
                                {vuln.cve}
                              </span>
                            )}
                            {vuln.cvss && (
                              <span className="text-xs text-gray-600">
                                CVSS: {vuln.cvss}
                              </span>
                            )}
                          </div>
                          {vuln.url && (
                            <Button
                              asChild
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                            >
                              <a
                                href={vuln.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                title="View vulnerability details"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </a>
                            </Button>
                          )}
                        </div>
                        <div className="mt-1 text-xs text-gray-700">
                          {vuln.title || vuln.description}
                        </div>
                        {vuln.patched_in && (
                          <div className="mt-1 text-xs text-green-700">
                            Fixed in: {vuln.patched_in}
                          </div>
                        )}
                      </div>
                    ))}
                    {dep.security.vulnerabilities.length > 3 && (
                      <div className="text-xs text-gray-600">
                        ... and {dep.security.vulnerabilities.length - 3} more vulnerabilities
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="flex space-x-1">
                {dep.registry_url && (
                  <Button
                    asChild
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                  >
                    <a
                      href={dep.registry_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      title={`View ${dep.name} on ${dep.registry}`}
                    >
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                )}
              </div>
            </div>
          ))}
          
          {!isExpanded && dependencyInfo.dependencies.length > 5 && (
            <div className="text-center pt-2">
              <Button
                onClick={() => setIsExpanded(true)}
                variant="ghost"
                size="sm"
              >
                Show {dependencyInfo.dependencies.length - 5} more dependencies
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}