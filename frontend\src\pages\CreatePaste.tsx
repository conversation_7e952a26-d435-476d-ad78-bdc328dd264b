import { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/hooks/use-toast'
import { pasteAPI } from '@/lib/api'
import { CheckCircle, XCircle, Loader2 } from 'lucide-react'
import CodeEditor from '@/components/CodeEditor'
import EncryptionToggle from '@/components/EncryptionToggle'
import { useEncryption } from '@/hooks/useEncryption'
import { detectLanguage, getLanguageDisplayName } from '@/utils/languageDetection'

export default function CreatePaste() {
  const [title, setTitle] = useState('')
  const [content, setContent] = useState('')
  const [language, setLanguage] = useState('text')
  const [customUrl, setCustomUrl] = useState('')
  const [expiresAt, setExpiresAt] = useState('')
  const [maxViews, setMaxViews] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isCheckingUrl, setIsCheckingUrl] = useState(false)
  const [isEncryptionEnabled, setIsEncryptionEnabled] = useState(false)
  const [urlAvailability, setUrlAvailability] = useState<{
    available: boolean | null
    message: string
  }>({ available: null, message: '' })
  const navigate = useNavigate()
  const { toast } = useToast()
  const { encryptContent, getShareableUrl } = useEncryption()

  // Debounced URL availability check
  const checkUrlAvailability = useCallback(async (url: string) => {
    if (!url.trim()) {
      setUrlAvailability({ available: null, message: '' })
      return
    }

    if (url.length < 3) {
      setUrlAvailability({ 
        available: false, 
        message: 'Custom URL must be at least 3 characters' 
      })
      return
    }

    setIsCheckingUrl(true)
    try {
      const response = await pasteAPI.checkCustomURLAvailability(url)
      if (response.data.available) {
        setUrlAvailability({ 
          available: true, 
          message: 'Custom URL is available' 
        })
      } else {
        setUrlAvailability({ 
          available: false, 
          message: 'Custom URL is already taken' 
        })
      }
    } catch (error: any) {
      setUrlAvailability({ 
        available: false, 
        message: error.response?.data?.error || 'Invalid custom URL' 
      })
    } finally {
      setIsCheckingUrl(false)
    }
  }, [])

  // Debounce URL checking
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      checkUrlAvailability(customUrl)
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [customUrl, checkUrlAvailability])

  // Auto-detect language when content changes
  useEffect(() => {
    if (content.trim() && language === 'text') {
      const detectedLanguage = detectLanguage(content, title);
      if (detectedLanguage !== 'text') {
        setLanguage(detectedLanguage);
        toast({
          title: "Language Detected",
          description: `Detected ${getLanguageDisplayName(detectedLanguage)}`,
        });
      }
    }
  }, [content, title, language, toast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Check if custom URL is valid before submitting
    if (customUrl && urlAvailability.available === false) {
      toast({
        title: "Error",
        description: "Please fix the custom URL before submitting",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      let finalContent = content
      let encryptedData = null

      // Encrypt content if encryption is enabled
      if (isEncryptionEnabled) {
        encryptedData = await encryptContent(content)
        if (!encryptedData) {
          toast({
            title: "Error",
            description: "Failed to encrypt content. Please try again.",
            variant: "destructive",
          })
          setIsLoading(false)
          return
        }
        // Store encrypted data as JSON string
        finalContent = JSON.stringify(encryptedData)
      }

      const response = await pasteAPI.create({
        title,
        content: finalContent,
        language,
        custom_url: customUrl || undefined,
        is_encrypted: isEncryptionEnabled,
        is_watermarked: false,
        expires_at: expiresAt || undefined,
        max_views: maxViews ? parseInt(maxViews, 10) : undefined,
      })

      toast({
        title: "Success",
        description: isEncryptionEnabled 
          ? "Encrypted paste created successfully" 
          : "Paste created successfully",
      })
      
      // Navigate to the paste using custom URL if available, otherwise use ID
      let targetUrl: string
      if (customUrl && urlAvailability.available) {
        targetUrl = `/p/${customUrl}`
      } else {
        targetUrl = `/paste/${response.data.id}`
      }

      // Add encryption key to URL if encryption is enabled
      if (isEncryptionEnabled) {
        const fullUrl = `${window.location.origin}${targetUrl}`
        const shareableUrl = getShareableUrl(fullUrl)
        // Extract just the path and hash from the shareable URL
        const url = new URL(shareableUrl)
        navigate(`${url.pathname}${url.hash}`)
      } else {
        navigate(targetUrl)
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Failed to create paste",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Create New Paste</CardTitle>
          <CardDescription>
            Share your code with the world
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="title" className="block text-sm font-medium mb-2">
                  Title
                </label>
                <Input
                  id="title"
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter paste title"
                  required
                  maxLength={500}
                />
              </div>
              <div>
                <label htmlFor="customUrl" className="block text-sm font-medium mb-2">
                  Custom URL (optional)
                </label>
                <div className="relative">
                  <Input
                    id="customUrl"
                    type="text"
                    value={customUrl}
                    onChange={(e) => setCustomUrl(e.target.value.toLowerCase().replace(/[^a-z0-9\-_]/g, ''))}
                    placeholder="my-custom-url"
                    className={`pr-10 ${
                      customUrl && urlAvailability.available === false 
                        ? 'border-red-500 focus-visible:ring-red-500' 
                        : customUrl && urlAvailability.available === true 
                        ? 'border-green-500 focus-visible:ring-green-500' 
                        : ''
                    }`}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    {isCheckingUrl && customUrl && (
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    )}
                    {!isCheckingUrl && customUrl && urlAvailability.available === true && (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                    {!isCheckingUrl && customUrl && urlAvailability.available === false && (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                </div>
                {customUrl && urlAvailability.message && (
                  <p className={`text-sm mt-1 ${
                    urlAvailability.available === false ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {urlAvailability.message}
                  </p>
                )}
                {customUrl && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Your paste will be available at: /p/{customUrl}
                  </p>
                )}
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium">
                  Code Editor
                </label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const detectedLanguage = detectLanguage(content, title);
                    if (detectedLanguage !== 'text') {
                      setLanguage(detectedLanguage);
                      toast({
                        title: "Language Detected",
                        description: `Detected ${getLanguageDisplayName(detectedLanguage)}`,
                      });
                    } else {
                      toast({
                        title: "No Language Detected",
                        description: "Could not automatically detect the language",
                      });
                    }
                  }}
                  disabled={!content.trim()}
                >
                  Auto-detect Language
                </Button>
              </div>
              <CodeEditor
                value={content}
                onChange={setContent}
                language={language}
                onLanguageChange={setLanguage}
                height="500px"
                showToolbar={true}
                showLanguageSelector={true}
                title="Paste Content"
              />
            </div>

            {/* Expiration Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Expiration Settings (Optional)</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="expiresAt" className="block text-sm font-medium mb-2">
                    Expires At
                  </label>
                  <Input
                    id="expiresAt"
                    type="datetime-local"
                    value={expiresAt}
                    onChange={(e) => setExpiresAt(e.target.value)}
                    min={new Date().toISOString().slice(0, 16)}
                    placeholder="Select expiration date and time"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Paste will be automatically deleted after this date
                  </p>
                </div>
                <div>
                  <label htmlFor="maxViews" className="block text-sm font-medium mb-2">
                    Max Views
                  </label>
                  <Input
                    id="maxViews"
                    type="number"
                    value={maxViews}
                    onChange={(e) => setMaxViews(e.target.value)}
                    min="1"
                    placeholder="e.g., 100"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Paste will be deleted after this many views
                  </p>
                </div>
              </div>
            </div>

            <EncryptionToggle
              onEncryptionChange={setIsEncryptionEnabled}
              className="mt-4"
            />

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/')}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Creating...' : 'Create Paste'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}