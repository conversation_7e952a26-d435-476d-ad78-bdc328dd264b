import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base-page';

export class RegisterPage extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  // Page-specific locators
  get pageTitle(): Locator {
    return this.page.locator('h1').first();
  }

  get registerForm(): Locator {
    return this.page.locator('form, [data-testid="register-form"]').first();
  }

  get usernameInput(): Locator {
    return this.page.locator('input[name="username"]').first();
  }

  get emailInput(): Locator {
    return this.page.locator('input[name="email"], input[type="email"]').first();
  }

  get passwordInput(): Locator {
    return this.page.locator('input[name="password"], input[type="password"]').first();
  }

  get confirmPasswordInput(): Locator {
    return this.page.locator('input[name="confirmPassword"], input[name="password_confirmation"]').first();
  }

  get submitButton(): Locator {
    return this.page.locator('button[type="submit"], button:has-text("Register")').first();
  }

  get loginLink(): Locator {
    return this.page.locator('a[href="/login"], a:has-text("Login")').first();
  }

  get termsCheckbox(): Locator {
    return this.page.locator('input[type="checkbox"][name="terms"], input[type="checkbox"]:near(:text("terms"))').first();
  }

  get privacyCheckbox(): Locator {
    return this.page.locator('input[type="checkbox"][name="privacy"], input[type="checkbox"]:near(:text("privacy"))').first();
  }

  get validationErrors(): Locator {
    return this.page.locator('.field-error, .validation-error, [data-testid="field-error"]');
  }

  get usernameError(): Locator {
    return this.page.locator('.field-error:near(input[name="username"]), [data-testid="username-error"]').first();
  }

  get emailError(): Locator {
    return this.page.locator('.field-error:near(input[name="email"]), [data-testid="email-error"]').first();
  }

  get passwordError(): Locator {
    return this.page.locator('.field-error:near(input[name="password"]), [data-testid="password-error"]').first();
  }

  get confirmPasswordError(): Locator {
    return this.page.locator('.field-error:near(input[name="confirmPassword"]), [data-testid="confirm-password-error"]').first();
  }

  get passwordStrengthIndicator(): Locator {
    return this.page.locator('.password-strength, [data-testid="password-strength"]').first();
  }

  // Page actions
  async visit(): Promise<void> {
    await this.page.goto('/register');
    await this.waitForPageLoad();
  }

  async fillUsername(username: string): Promise<void> {
    await this.usernameInput.fill(username);
  }

  async fillEmail(email: string): Promise<void> {
    await this.emailInput.fill(email);
  }

  async fillPassword(password: string): Promise<void> {
    await this.passwordInput.fill(password);
  }

  async fillConfirmPassword(password: string): Promise<void> {
    await this.confirmPasswordInput.fill(password);
  }

  async checkTerms(): Promise<void> {
    if (await this.termsCheckbox.isVisible()) {
      await this.termsCheckbox.check();
    }
  }

  async checkPrivacy(): Promise<void> {
    if (await this.privacyCheckbox.isVisible()) {
      await this.privacyCheckbox.check();
    }
  }

  async submitForm(): Promise<void> {
    await this.submitButton.click();
  }

  async register(userData: {
    username: string;
    email: string;
    password: string;
    confirmPassword?: string;
    acceptTerms?: boolean;
  }): Promise<void> {
    await this.fillUsername(userData.username);
    await this.fillEmail(userData.email);
    await this.fillPassword(userData.password);
    
    if (userData.confirmPassword !== undefined) {
      await this.fillConfirmPassword(userData.confirmPassword);
    } else {
      await this.fillConfirmPassword(userData.password);
    }
    
    if (userData.acceptTerms !== false) {
      await this.checkTerms();
      await this.checkPrivacy();
    }
    
    await this.submitForm();
  }

  async registerAndWaitForRedirect(userData: {
    username: string;
    email: string;
    password: string;
    confirmPassword?: string;
  }, expectedUrl: string | RegExp = '/login'): Promise<void> {
    await this.register(userData);
    await this.page.waitForURL(expectedUrl);
  }

  async clickLoginLink(): Promise<void> {
    await this.loginLink.click();
    await this.page.waitForURL('/login');
  }

  // Page assertions
  async expectToBeOnRegisterPage(): Promise<void> {
    await expect(this.page).toHaveURL('/register');
    await expect(this.pageTitle).toContainText(/register|sign up/i);
  }

  async expectRegisterForm(): Promise<void> {
    await expect(this.registerForm).toBeVisible();
    await expect(this.usernameInput).toBeVisible();
    await expect(this.emailInput).toBeVisible();
    await expect(this.passwordInput).toBeVisible();
    await expect(this.submitButton).toBeVisible();
  }

  async expectUsernameError(message: string): Promise<void> {
    await expect(this.usernameError).toBeVisible();
    await expect(this.usernameError).toContainText(message);
  }

  async expectEmailError(message: string): Promise<void> {
    await expect(this.emailError).toBeVisible();
    await expect(this.emailError).toContainText(message);
  }

  async expectPasswordError(message: string): Promise<void> {
    await expect(this.passwordError).toBeVisible();
    await expect(this.passwordError).toContainText(message);
  }

  async expectConfirmPasswordError(message: string): Promise<void> {
    await expect(this.confirmPasswordError).toBeVisible();
    await expect(this.confirmPasswordError).toContainText(message);
  }

  async expectValidationErrors(): Promise<void> {
    await expect(this.validationErrors.first()).toBeVisible();
  }

  async expectNoValidationErrors(): Promise<void> {
    await expect(this.validationErrors).toHaveCount(0);
  }

  async expectRegistrationSuccess(): Promise<void> {
    // Should be redirected away from register page
    await expect(this.page).not.toHaveURL('/register');
    
    // Usually redirected to login page with success message
    await this.expectSuccessMessage(/registered|created|welcome/i);
  }

  async expectRegistrationFailure(errorMessage?: string): Promise<void> {
    // Should still be on register page
    await expect(this.page).toHaveURL('/register');
    
    if (errorMessage) {
      await this.expectErrorMessage(errorMessage);
    } else {
      // Should show some error indication
      await expect(this.errorMessage.or(this.validationErrors.first())).toBeVisible();
    }
  }

  async expectPasswordStrength(strength: 'weak' | 'medium' | 'strong'): Promise<void> {
    await expect(this.passwordStrengthIndicator).toBeVisible();
    await expect(this.passwordStrengthIndicator).toContainText(new RegExp(strength, 'i'));
  }

  async expectLoginLinkVisible(): Promise<void> {
    await expect(this.loginLink).toBeVisible();
  }

  // Form validation helpers
  async expectRequiredFieldValidation(): Promise<void> {
    // Try to submit empty form
    await this.submitForm();
    
    // Should show validation errors
    await this.expectValidationErrors();
  }

  async expectUsernameAlreadyExistsError(): Promise<void> {
    await this.expectUsernameError(/already exists|taken|unavailable/i);
  }

  async expectEmailAlreadyExistsError(): Promise<void> {
    await this.expectEmailError(/already exists|taken|registered/i);
  }

  async expectPasswordMismatchError(): Promise<void> {
    await this.expectConfirmPasswordError(/match|same|different/i);
  }

  async expectWeakPasswordError(): Promise<void> {
    await this.expectPasswordError(/weak|strong|requirements/i);
  }

  async expectInvalidEmailError(): Promise<void> {
    await this.expectEmailError(/invalid|format|valid email/i);
  }

  // Utility methods
  async clearForm(): Promise<void> {
    await this.usernameInput.clear();
    await this.emailInput.clear();
    await this.passwordInput.clear();
    if (await this.confirmPasswordInput.isVisible()) {
      await this.confirmPasswordInput.clear();
    }
  }

  async isFormValid(): Promise<boolean> {
    const username = await this.usernameInput.inputValue();
    const email = await this.emailInput.inputValue();
    const password = await this.passwordInput.inputValue();
    const confirmPassword = await this.confirmPasswordInput.inputValue();
    
    return username.length > 0 && 
           email.length > 0 && 
           password.length > 0 && 
           password === confirmPassword;
  }

  async getFormData(): Promise<{
    username: string;
    email: string;
    password: string;
    confirmPassword: string;
    termsAccepted: boolean;
  }> {
    const username = await this.usernameInput.inputValue();
    const email = await this.emailInput.inputValue();
    const password = await this.passwordInput.inputValue();
    const confirmPassword = await this.confirmPasswordInput.inputValue();
    const termsAccepted = await this.termsCheckbox.isChecked();
    
    return { username, email, password, confirmPassword, termsAccepted };
  }

  async waitForFormSubmission(): Promise<void> {
    // Wait for form submission to complete (either success or error)
    await Promise.race([
      this.page.waitForURL(url => !url.includes('/register')),
      this.errorMessage.waitFor(),
      this.validationErrors.first().waitFor()
    ]);
  }
}
