import { TestUser } from '../utils/api-client';

export const userFixtures = {
  // Standard test users
  regularUser: {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },

  adminUser: {
    username: 'admin',
    email: '<EMAIL>',
    password: 'AdminPassword123!'
  },

  // Users with special characteristics
  longUsernameUser: {
    username: 'verylongusernamethatmightcauseissues',
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },

  specialCharUser: {
    username: 'user_with-special.chars',
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },

  unicodeUser: {
    username: 'ユーザー名',
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },

  // Users for specific test scenarios
  inactiveUser: {
    username: 'inactive_user',
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },

  premiumUser: {
    username: 'premium_user',
    email: '<EMAIL>',
    password: 'TestPassword123!'
  }
};

export const passwordTestCases = {
  weak: [
    '123',
    'password',
    'abc',
    '111111'
  ],
  
  medium: [
    'Password123',
    'TestPass1',
    'MyPassword1'
  ],
  
  strong: [
    'VeryStrongPassword123!@#',
    'ComplexP@ssw0rd!',
    'Secure123!@#$%'
  ]
};

export const invalidEmails = [
  'invalid-email',
  '@example.com',
  'user@',
  '<EMAIL>',
  'user@.com',
  'user@com',
  ''
];

export const validEmails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

export const invalidUsernames = [
  '', // empty
  'a', // too short
  'ab', // too short
  'user with spaces',
  'user@invalid',
  'user#invalid',
  'a'.repeat(51), // too long
  '123numeric', // starts with number
  '-invalid', // starts with special char
  'invalid-' // ends with special char
];

export const validUsernames = [
  'user',
  'user123',
  'user_name',
  'user-name',
  'user.name',
  'testuser',
  'a'.repeat(20), // max length
  'User123' // mixed case
];

export function createTestUser(overrides: Partial<TestUser> = {}): Omit<TestUser, 'id' | 'token'> {
  const timestamp = Date.now();
  return {
    username: `testuser_${timestamp}`,
    email: `test_${timestamp}@example.com`,
    password: 'TestPassword123!',
    ...overrides
  };
}

export function createBulkTestUsers(count: number): Omit<TestUser, 'id' | 'token'>[] {
  return Array.from({ length: count }, (_, index) => ({
    username: `bulkuser_${Date.now()}_${index}`,
    email: `bulk_${Date.now()}_${index}@example.com`,
    password: 'TestPassword123!'
  }));
}

export function getUserByRole(role: 'admin' | 'premium' | 'regular' | 'inactive'): Omit<TestUser, 'id' | 'token'> {
  const timestamp = Date.now();
  
  switch (role) {
    case 'admin':
      return {
        ...userFixtures.adminUser,
        username: `admin_${timestamp}`,
        email: `admin_${timestamp}@example.com`
      };
    
    case 'premium':
      return {
        ...userFixtures.premiumUser,
        username: `premium_${timestamp}`,
        email: `premium_${timestamp}@example.com`
      };
    
    case 'inactive':
      return {
        ...userFixtures.inactiveUser,
        username: `inactive_${timestamp}`,
        email: `inactive_${timestamp}@example.com`
      };
    
    default:
      return {
        ...userFixtures.regularUser,
        username: `regular_${timestamp}`,
        email: `regular_${timestamp}@example.com`
      };
  }
}

export function getRandomUser(): Omit<TestUser, 'id' | 'token'> {
  const users = Object.values(userFixtures);
  const randomUser = users[Math.floor(Math.random() * users.length)];
  const timestamp = Date.now();
  
  return {
    ...randomUser,
    username: `${randomUser.username}_${timestamp}`,
    email: `${randomUser.username}_${timestamp}@example.com`
  };
}
