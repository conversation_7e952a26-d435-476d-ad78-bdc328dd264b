package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"time"

	"enhanced-pastebin/internal/models"

	"github.com/gorilla/websocket"
)

const (
	// Time allowed to write a message to the peer
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer
	pongWait = 60 * time.Second

	// Send pings to peer with this period. Must be less than pongWait
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer
	maxMessageSize = 512
)

var Upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// Allow connections from any origin for development
		// In production, you should check the origin properly
		return true
	},
}

// MessageHandler interface for handling WebSocket messages
type MessageHandler interface {
	HandleCollaborativeMessage(client *Client, message models.WebSocketMessage)
	JoinCollaborativeSession(client *Client, pasteID string)
	LeaveCollaborativeSession(client *Client, pasteID string)
}

// Client wraps the models.Client with additional functionality
type Client struct {
	*models.Client
	hub            *Hub
	messageHandler MessageHandler
}

// NewClient creates a new WebSocket client
func NewClient(hub *Hub, conn *websocket.Conn, userID, username string, messageHandler MessageHandler) *Client {
	modelClient := &models.Client{
		ID:       generateClientID(),
		UserID:   userID,
		Username: username,
		Conn:     conn,
		Send:     make(chan []byte, 256),
		Hub:      hub.Hub,
		Rooms:    make(map[string]bool),
	}
	
	return &Client{
		Client:         modelClient,
		hub:            hub,
		messageHandler: messageHandler,
	}
}

// ReadPump pumps messages from the websocket connection to the hub
func (c *Client) ReadPump() {
	defer func() {
		c.Hub.Unregister <- c.Client
		c.Conn.Close()
	}()

	c.Conn.SetReadLimit(maxMessageSize)
	c.Conn.SetReadDeadline(time.Now().Add(pongWait))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, messageBytes, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		// Parse the incoming message
		var message models.WebSocketMessage
		if err := json.Unmarshal(messageBytes, &message); err != nil {
			log.Printf("Error parsing WebSocket message: %v", err)
			continue
		}

		// Handle different message types
		c.handleMessage(message)
	}
}

// WritePump pumps messages from the hub to the websocket connection
func (c *Client) WritePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// Add queued messages to the current websocket message
			n := len(c.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.Send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage processes incoming WebSocket messages
func (c *Client) handleMessage(message models.WebSocketMessage) {
	switch message.Type {
	case models.MessageTypeJoin:
		if message.PasteID != "" {
			c.hub.JoinRoom(c.Client, message.PasteID)
			if c.messageHandler != nil {
				c.messageHandler.JoinCollaborativeSession(c, message.PasteID)
			}
		}

	case models.MessageTypeLeave:
		if message.PasteID != "" {
			c.hub.LeaveRoom(c.Client, message.PasteID)
			if c.messageHandler != nil {
				c.messageHandler.LeaveCollaborativeSession(c, message.PasteID)
			}
		}

	case models.MessageTypeBroadcast:
		// Broadcast message to all clients in the same rooms
		for room := range c.Rooms {
			broadcastMessage := models.WebSocketMessage{
				Type:      models.MessageTypeBroadcast,
				PasteID:   room,
				UserID:    c.UserID,
				Username:  c.Username,
				Data:      message.Data,
				Timestamp: time.Now(),
			}

			if messageBytes, err := json.Marshal(broadcastMessage); err == nil {
				c.hub.BroadcastToRoom(room, messageBytes)
			}
		}

	case models.MessageTypeOperation, models.MessageTypeCursor, models.MessageTypeSelection, models.MessageTypeSync:
		// Handle collaborative editing messages
		if c.messageHandler != nil {
			c.messageHandler.HandleCollaborativeMessage(c, message)
		} else {
			log.Printf("No message handler available for collaborative message type: %s", message.Type)
		}

	default:
		log.Printf("Unknown message type: %s", message.Type)
	}
}

// generateClientID generates a unique client ID
func generateClientID() string {
	return time.Now().Format("20060102150405") + "-" + randomString(8)
}

// randomString generates a random string of specified length
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}