import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from '@/contexts/AuthContext';
import { FormattingProvider } from '@/contexts/FormattingContext';

// Mock user for testing
export const mockUser = {
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  created_at: '2023-01-01T00:00:00Z',
  reputation_score: 100,
};

// Mock auth context
const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const mockAuthValue = {
    user: mockUser,
    login: jest.fn(),
    logout: jest.fn(),
    register: jest.fn(),
    isLoading: false,
  };

  return (
    <AuthProvider value={mockAuthValue}>
      {children}
    </AuthProvider>
  );
};

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <BrowserRouter>
      <MockAuthProvider>
        <FormattingProvider>
          {children}
        </FormattingProvider>
      </MockAuthProvider>
    </BrowserRouter>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };

// Mock API responses
export const mockPaste = {
  id: '1',
  title: 'Test Paste',
  content: 'console.log("Hello, World!");',
  language: 'javascript',
  is_encrypted: false,
  expires_at: null,
  view_count: 5,
  max_views: null,
  edit_count: 1,
  is_watermarked: false,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
  user_id: '1',
  username: 'testuser',
  custom_url: null,
};

export const mockChatMessage = {
  id: '1',
  paste_id: '1',
  user_id: '1',
  username: 'testuser',
  content: 'This is a test message',
  line_references: [1, 2],
  created_at: '2023-01-01T00:00:00Z',
};

export const mockVersion = {
  id: '1',
  paste_id: '1',
  version_number: 1,
  content: 'console.log("Hello, World!");',
  created_at: '2023-01-01T00:00:00Z',
  created_by: '1',
  created_by_username: 'testuser',
};

export const mockAccessLog = {
  id: '1',
  paste_id: '1',
  user_id: '1',
  username: 'testuser',
  ip_address: '127.0.0.1',
  user_agent: 'Mozilla/5.0',
  action: 'view',
  details: '{}',
  timestamp: '2023-01-01T00:00:00Z',
};

// Mock API functions
export const mockAPI = {
  // Auth API
  login: jest.fn(),
  register: jest.fn(),
  
  // Paste API
  createPaste: jest.fn(),
  getPaste: jest.fn(),
  updatePaste: jest.fn(),
  deletePaste: jest.fn(),
  
  // Chat API
  sendMessage: jest.fn(),
  getMessages: jest.fn(),
  deleteMessage: jest.fn(),
  
  // Version API
  getVersions: jest.fn(),
  getVersion: jest.fn(),
  compareVersions: jest.fn(),
  
  // User API
  getProfile: jest.fn(),
  getUserPastes: jest.fn(),
  getPublicProfile: jest.fn(),
  
  // Audit API
  getAccessLogs: jest.fn(),
  getWatermarks: jest.fn(),
  extractWatermark: jest.fn(),
};

// Setup function for tests
export const setupTest = () => {
  // Reset all mocks
  Object.values(mockAPI).forEach(mock => {
    if (jest.isMockFunction(mock)) {
      mock.mockReset();
    }
  });
  
  // Setup default mock implementations
  mockAPI.getPaste.mockResolvedValue({ data: mockPaste });
  mockAPI.getMessages.mockResolvedValue({ data: { messages: [mockChatMessage] } });
  mockAPI.getVersions.mockResolvedValue({ data: { versions: [mockVersion] } });
  mockAPI.getProfile.mockResolvedValue({ 
    data: { 
      user: mockUser, 
      stats: { total_pastes: 5, total_views: 100 },
      recent_pastes: [mockPaste]
    } 
  });
};

// Cleanup function for tests
export const cleanupTest = () => {
  jest.clearAllMocks();
};

// Custom matchers for testing
expect.extend({
  toBeAccessible(received) {
    // Simple accessibility check
    const hasAriaLabel = received.getAttribute('aria-label');
    const hasRole = received.getAttribute('role');
    const hasTabIndex = received.getAttribute('tabindex');
    
    const pass = hasAriaLabel || hasRole || hasTabIndex !== null;
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be accessible`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be accessible (have aria-label, role, or tabindex)`,
        pass: false,
      };
    }
  },
});

// Test data generators
export const generateMockPaste = (overrides = {}) => ({
  ...mockPaste,
  ...overrides,
});

export const generateMockUser = (overrides = {}) => ({
  ...mockUser,
  ...overrides,
});

export const generateMockChatMessage = (overrides = {}) => ({
  ...mockChatMessage,
  ...overrides,
});

// Async test helpers
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0));
};

export const mockIntersectionObserver = () => {
  global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));
};

export const mockResizeObserver = () => {
  global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));
};

// Performance testing helpers
export const measureRenderTime = async (renderFn: () => void) => {
  const start = performance.now();
  await renderFn();
  const end = performance.now();
  return end - start;
};

// Accessibility testing helpers
export const checkKeyboardNavigation = (element: HTMLElement) => {
  const focusableElements = element.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );
  
  return {
    hasFocusableElements: focusableElements.length > 0,
    focusableCount: focusableElements.length,
    elements: Array.from(focusableElements),
  };
};

export const simulateKeyPress = (element: HTMLElement, key: string, options = {}) => {
  const event = new KeyboardEvent('keydown', {
    key,
    bubbles: true,
    cancelable: true,
    ...options,
  });
  
  element.dispatchEvent(event);
  return event;
};
