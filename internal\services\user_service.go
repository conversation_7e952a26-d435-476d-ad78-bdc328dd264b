package services

import (
	"errors"
	"time"

	"enhanced-pastebin/internal/models"
	"enhanced-pastebin/internal/repositories"
	"enhanced-pastebin/internal/utils"

	"golang.org/x/crypto/bcrypt"
)

type UserService interface {
	Register(req *models.CreateUserRequest) (*models.User, error)
	Login(req *models.LoginRequest) (*models.LoginResponse, error)
	GetByID(id string) (*models.User, error)
	GetByUsername(username string) (*models.User, error)
	GetUserStats(userID string) (*models.UserStats, error)
	GetUserPastes(userID string, limit, offset int) ([]*models.Paste, error)
	GetPublicUserProfile(username string) (*models.PublicUserProfile, error)
	UpdateLastActive(userID string) error
}

type userService struct {
	userRepo  repositories.UserRepository
	pasteRepo repositories.PasteRepository
}

func NewUserService(userRepo repositories.UserRepository, pasteRepo repositories.PasteRepository) UserService {
	return &userService{
		userRepo:  userRepo,
		pasteRepo: pasteRepo,
	}
}

func (s *userService) Register(req *models.CreateUserRequest) (*models.User, error) {
	// Check if user already exists
	existingUser, err := s.userRepo.GetByEmail(req.Email)
	if err != nil {
		return nil, err
	}
	if existingUser != nil {
		return nil, errors.New("user with this email already exists")
	}

	existingUser, err = s.userRepo.GetByUsername(req.Username)
	if err != nil {
		return nil, err
	}
	if existingUser != nil {
		return nil, errors.New("user with this username already exists")
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// Create user
	user := &models.User{
		ID:             utils.GenerateID(),
		Username:       req.Username,
		Email:          req.Email,
		PasswordHash:   string(hashedPassword),
		ReputationScore: 0,
		CreatedAt:      time.Now(),
		LastActive:     time.Now(),
	}

	if err := s.userRepo.Create(user); err != nil {
		return nil, err
	}

	return user, nil
}

func (s *userService) Login(req *models.LoginRequest) (*models.LoginResponse, error) {
	// Get user by email
	user, err := s.userRepo.GetByEmail(req.Email)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.New("invalid credentials")
	}

	// Check password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		return nil, errors.New("invalid credentials")
	}

	// Update last active
	user.LastActive = time.Now()
	if err := s.userRepo.Update(user); err != nil {
		return nil, err
	}

	// Generate JWT token
	token, err := utils.GenerateJWT(user.ID)
	if err != nil {
		return nil, err
	}

	return &models.LoginResponse{
		Token: token,
		User:  *user,
	}, nil
}

func (s *userService) GetByID(id string) (*models.User, error) {
	return s.userRepo.GetByID(id)
}

func (s *userService) GetByUsername(username string) (*models.User, error) {
	return s.userRepo.GetByUsername(username)
}

func (s *userService) GetUserStats(userID string) (*models.UserStats, error) {
	// Get user's pastes count and total views
	pastes, err := s.pasteRepo.GetByUserID(userID, 1000, 0) // Get all pastes for stats
	if err != nil {
		return nil, err
	}

	totalViews := 0
	totalPastes := len(pastes)

	for _, paste := range pastes {
		totalViews += paste.ViewCount
	}

	// Calculate most used language
	languageCount := make(map[string]int)
	for _, paste := range pastes {
		if paste.Language != "" {
			languageCount[paste.Language]++
		}
	}

	var mostUsedLanguage string
	maxCount := 0
	for lang, count := range languageCount {
		if count > maxCount {
			maxCount = count
			mostUsedLanguage = lang
		}
	}

	return &models.UserStats{
		TotalPastes:       totalPastes,
		TotalViews:        totalViews,
		MostUsedLanguage:  mostUsedLanguage,
		LanguageCount:     languageCount,
	}, nil
}

func (s *userService) GetUserPastes(userID string, limit, offset int) ([]*models.Paste, error) {
	return s.pasteRepo.GetByUserID(userID, limit, offset)
}

func (s *userService) GetPublicUserProfile(username string) (*models.PublicUserProfile, error) {
	user, err := s.userRepo.GetByUsername(username)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	// Get user stats
	stats, err := s.GetUserStats(user.ID)
	if err != nil {
		return nil, err
	}

	// Get recent public pastes (non-encrypted)
	allPastes, err := s.pasteRepo.GetByUserID(user.ID, 10, 0)
	if err != nil {
		return nil, err
	}

	// Filter out encrypted pastes for public view
	var publicPastes []*models.Paste
	for _, paste := range allPastes {
		if !paste.IsEncrypted {
			publicPastes = append(publicPastes, paste)
		}
	}

	return &models.PublicUserProfile{
		Username:      user.Username,
		ReputationScore: user.ReputationScore,
		CreatedAt:     user.CreatedAt,
		LastActive:    user.LastActive,
		Stats:         *stats,
		RecentPastes:  publicPastes,
	}, nil
}

func (s *userService) UpdateLastActive(userID string) error {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return err
	}
	if user == nil {
		return errors.New("user not found")
	}

	user.LastActive = time.Now()
	return s.userRepo.Update(user)
}