import { codeFormatterService } from '../codeFormatter'

describe('CodeFormatterService', () => {
  describe('formatCode', () => {
    it('should format JavaScript code', async () => {
      const code = 'function test(){console.log("hello");}'
      const result = await codeFormatterService.formatCode(code, 'javascript')
      
      expect(result.success).toBe(true)
      expect(result.formattedCode).toContain('function test()')
      expect(result.formattedCode).toContain('console.log("hello");')
    })

    it('should format JSON code', async () => {
      const code = '{"name":"test","value":123}'
      const result = await codeFormatterService.formatCode(code, 'json')
      
      expect(result.success).toBe(true)
      expect(result.formattedCode).toContain('{\n  "name": "test"')
    })

    it('should handle invalid JSON', async () => {
      const code = '{"name":"test"'
      const result = await codeFormatterService.formatCode(code, 'json')
      
      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid JSON')
    })

    it('should handle unsupported language', async () => {
      const code = 'some code'
      const result = await codeFormatterService.formatCode(code, 'unsupported')
      
      expect(result.success).toBe(false)
      expect(result.error).toContain('Formatting not supported')
    })
  })

  describe('lintCode', () => {
    it('should detect JavaScript warnings', () => {
      const code = 'console.log("test")\nlet x = 5'
      const result = codeFormatterService.lintCode(code, 'javascript')
      
      expect(result.warnings.length).toBeGreaterThan(0)
      expect(result.warnings[0].message).toContain('console.log')
    })

    it('should detect JSON syntax errors', () => {
      const code = '{"name":"test"'
      const result = codeFormatterService.lintCode(code, 'json')
      
      expect(result.errors.length).toBeGreaterThan(0)
    })

    it('should detect Python syntax issues', () => {
      const code = 'if True\n    print("test")'
      const result = codeFormatterService.lintCode(code, 'python')
      
      expect(result.errors.length).toBeGreaterThan(0)
      expect(result.errors[0].message).toContain('Missing colon')
    })

    it('should return empty results for unsupported language', () => {
      const code = 'some code'
      const result = codeFormatterService.lintCode(code, 'unsupported')
      
      expect(result.errors).toHaveLength(0)
      expect(result.warnings).toHaveLength(0)
    })
  })
})