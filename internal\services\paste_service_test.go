package services

import (
	"testing"
	"enhanced-pastebin/internal/models"
)

func TestValidateCustomURL(t *testing.T) {
	service := &pasteService{}
	
	testCases := []struct {
		url         string
		expectError bool
		description string
	}{
		{"my-paste", false, "Valid custom URL with hyphen"},
		{"my_paste", false, "Valid custom URL with underscore"},
		{"myPaste123", false, "Valid custom URL with mixed case and numbers"},
		{"ab", true, "Too short (less than 3 characters)"},
		{"", true, "Empty string"},
		{"my@paste", true, "Invalid character (@)"},
		{"my paste", true, "Invalid character (space)"},
		{"my.paste", true, "Invalid character (.)"},
		{"api", true, "Reserved word"},
		{"admin", true, "Reserved word"},
		{"users", true, "Reserved word"},
		{"create", true, "Reserved word"},
		{"a" + string(make([]byte, 255)), true, "Too long (over 255 characters)"},
	}
	
	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			err := service.validateCustomURL(tc.url)
			
			if tc.expectError && err == nil {
				t.Errorf("Expected error for URL '%s', but got none", tc.url)
			}
			
			if !tc.expectError && err != nil {
				t.Errorf("Expected no error for URL '%s', but got: %v", tc.url, err)
			}
		})
	}
}

func TestValidatePasteContent(t *testing.T) {
	service := &pasteService{}
	
	validURL := "my-custom-url"
	invalidURL := "api"
	
	testCases := []struct {
		req         *models.CreatePasteRequest
		expectError bool
		description string
	}{
		{
			req: &models.CreatePasteRequest{
				Title:     "Test Paste",
				Content:   "Test content",
				CustomURL: &validURL,
			},
			expectError: false,
			description: "Valid paste with custom URL",
		},
		{
			req: &models.CreatePasteRequest{
				Title:     "Test Paste",
				Content:   "Test content",
				CustomURL: &invalidURL,
			},
			expectError: true,
			description: "Invalid paste with reserved custom URL",
		},
		{
			req: &models.CreatePasteRequest{
				Title:   "",
				Content: "Test content",
			},
			expectError: true,
			description: "Empty title",
		},
		{
			req: &models.CreatePasteRequest{
				Title:   "Test",
				Content: "",
			},
			expectError: true,
			description: "Empty content",
		},
	}
	
	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			err := service.validatePasteContent(tc.req)
			
			if tc.expectError && err == nil {
				t.Errorf("Expected error for request, but got none")
			}
			
			if !tc.expectError && err != nil {
				t.Errorf("Expected no error for request, but got: %v", err)
			}
		})
	}
}