import { useState, useCallback, useEffect } from 'react';
import EncryptionService, { EncryptedData, EncryptionKey } from '../services/encryption';

export interface UseEncryptionReturn {
  isEncryptionEnabled: boolean;
  isEncryptionSupported: boolean;
  encryptionKey: EncryptionKey | null;
  isGeneratingKey: boolean;
  error: string | null;
  
  // Actions
  toggleEncryption: () => void;
  generateNewKey: () => Promise<void>;
  importKeyFromUrl: () => Promise<boolean>;
  encryptContent: (content: string) => Promise<EncryptedData | null>;
  decryptContent: (encryptedData: EncryptedData) => Promise<string | null>;
  getShareableUrl: (baseUrl: string) => string;
  clearError: () => void;
}

export const useEncryption = (): UseEncryptionReturn => {
  const [isEncryptionEnabled, setIsEncryptionEnabled] = useState(false);
  const [encryptionKey, setEncryptionKey] = useState<EncryptionKey | null>(null);
  const [isGeneratingKey, setIsGeneratingKey] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isEncryptionSupported = EncryptionService.isSupported();

  // Try to import key from URL on mount
  useEffect(() => {
    if (isEncryptionSupported) {
      importKeyFromUrl();
    }
  }, [isEncryptionSupported]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const toggleEncryption = useCallback(() => {
    if (!isEncryptionSupported) {
      setError('Encryption is not supported in this browser');
      return;
    }

    setIsEncryptionEnabled(prev => {
      const newValue = !prev;
      if (!newValue) {
        // Clear key when disabling encryption
        setEncryptionKey(null);
        EncryptionService.removeKeyFromUrl();
      }
      return newValue;
    });
  }, [isEncryptionSupported]);

  const generateNewKey = useCallback(async () => {
    if (!isEncryptionSupported) {
      setError('Encryption is not supported in this browser');
      return;
    }

    setIsGeneratingKey(true);
    setError(null);

    try {
      const newKey = await EncryptionService.generateKey();
      setEncryptionKey(newKey);
      setIsEncryptionEnabled(true);
    } catch (err) {
      setError('Failed to generate encryption key');
      console.error('Key generation error:', err);
    } finally {
      setIsGeneratingKey(false);
    }
  }, [isEncryptionSupported]);

  const importKeyFromUrl = useCallback(async (): Promise<boolean> => {
    if (!isEncryptionSupported) {
      return false;
    }

    try {
      const keyFromUrl = EncryptionService.extractKeyFromUrl();
      if (keyFromUrl) {
        const importedKey = await EncryptionService.importKey(keyFromUrl);
        setEncryptionKey({
          key: importedKey,
          exportedKey: keyFromUrl
        });
        setIsEncryptionEnabled(true);
        return true;
      }
    } catch (err) {
      setError('Failed to import encryption key from URL');
      console.error('Key import error:', err);
    }
    
    return false;
  }, [isEncryptionSupported]);

  const encryptContent = useCallback(async (content: string): Promise<EncryptedData | null> => {
    if (!encryptionKey || !isEncryptionEnabled) {
      return null;
    }

    try {
      setError(null);
      return await EncryptionService.encrypt(content, encryptionKey.key);
    } catch (err) {
      setError('Failed to encrypt content');
      console.error('Encryption error:', err);
      return null;
    }
  }, [encryptionKey, isEncryptionEnabled]);

  const decryptContent = useCallback(async (encryptedData: EncryptedData): Promise<string | null> => {
    if (!encryptionKey) {
      setError('No encryption key available for decryption');
      return null;
    }

    try {
      setError(null);
      return await EncryptionService.decrypt(encryptedData, encryptionKey.key);
    } catch (err) {
      setError('Failed to decrypt content. Invalid key or corrupted data.');
      console.error('Decryption error:', err);
      return null;
    }
  }, [encryptionKey]);

  const getShareableUrl = useCallback((baseUrl: string): string => {
    if (!encryptionKey || !isEncryptionEnabled) {
      return baseUrl;
    }

    const url = new URL(baseUrl);
    url.hash = `key=${encodeURIComponent(encryptionKey.exportedKey)}`;
    return url.toString();
  }, [encryptionKey, isEncryptionEnabled]);

  return {
    isEncryptionEnabled,
    isEncryptionSupported,
    encryptionKey,
    isGeneratingKey,
    error,
    
    toggleEncryption,
    generateNewKey,
    importKeyFromUrl,
    encryptContent,
    decryptContent,
    getShareableUrl,
    clearError,
  };
};

export default useEncryption;