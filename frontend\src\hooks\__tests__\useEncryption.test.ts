import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useEncryption } from '../useEncryption'
import EncryptionService from '../../services/encryption'

// Mock the EncryptionService
vi.mock('../../services/encryption', () => ({
  default: {
    isSupported: vi.fn(),
    generateKey: vi.fn(),
    importKey: vi.fn(),
    encrypt: vi.fn(),
    decrypt: vi.fn(),
    extractKeyFromUrl: vi.fn(),
    removeKeyFromUrl: vi.fn(),
  },
}))

const mockEncryptionService = EncryptionService as any

describe('useEncryption', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockEncryptionService.isSupported.mockReturnValue(true)
    mockEncryptionService.extractKeyFromUrl.mockReturnValue(null)
  })

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useEncryption())

    expect(result.current.isEncryptionEnabled).toBe(false)
    expect(result.current.isEncryptionSupported).toBe(true)
    expect(result.current.encryptionKey).toBeNull()
    expect(result.current.isGeneratingKey).toBe(false)
    expect(result.current.error).toBeNull()
  })

  it('should detect when encryption is not supported', () => {
    mockEncryptionService.isSupported.mockReturnValue(false)

    const { result } = renderHook(() => useEncryption())

    expect(result.current.isEncryptionSupported).toBe(false)
  })

  it('should toggle encryption on and off', async () => {
    const { result } = renderHook(() => useEncryption())

    await act(async () => {
      result.current.toggleEncryption()
    })

    expect(result.current.isEncryptionEnabled).toBe(true)

    await act(async () => {
      result.current.toggleEncryption()
    })

    expect(result.current.isEncryptionEnabled).toBe(false)
    expect(result.current.encryptionKey).toBeNull()
    expect(mockEncryptionService.removeKeyFromUrl).toHaveBeenCalled()
  })

  it('should show error when toggling encryption on unsupported browser', async () => {
    mockEncryptionService.isSupported.mockReturnValue(false)

    const { result } = renderHook(() => useEncryption())

    await act(async () => {
      result.current.toggleEncryption()
    })

    expect(result.current.isEncryptionEnabled).toBe(false)
    expect(result.current.error).toBe('Encryption is not supported in this browser')
  })

  it('should generate new encryption key', async () => {
    const mockKey = {
      key: { type: 'secret' } as CryptoKey,
      exportedKey: 'exported-key-data',
    }

    mockEncryptionService.generateKey.mockResolvedValue(mockKey)

    const { result } = renderHook(() => useEncryption())

    await act(async () => {
      await result.current.generateNewKey()
    })

    expect(result.current.encryptionKey).toBe(mockKey)
    expect(result.current.isEncryptionEnabled).toBe(true)
    expect(result.current.isGeneratingKey).toBe(false)
  })

  it('should handle key generation error', async () => {
    mockEncryptionService.generateKey.mockRejectedValue(new Error('Generation failed'))

    const { result } = renderHook(() => useEncryption())

    await act(async () => {
      await result.current.generateNewKey()
    })

    expect(result.current.error).toBe('Failed to generate encryption key')
    expect(result.current.encryptionKey).toBeNull()
    expect(result.current.isGeneratingKey).toBe(false)
  })

  it('should import key from URL on mount', async () => {
    const mockKey = { type: 'secret' } as CryptoKey
    const keyFromUrl = 'url-key-data'

    mockEncryptionService.extractKeyFromUrl.mockReturnValue(keyFromUrl)
    mockEncryptionService.importKey.mockResolvedValue(mockKey)

    const { result } = renderHook(() => useEncryption())

    // Wait for useEffect to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0))
    })

    expect(result.current.encryptionKey).toEqual({
      key: mockKey,
      exportedKey: keyFromUrl,
    })
    expect(result.current.isEncryptionEnabled).toBe(true)
  })

  it('should encrypt content', async () => {
    const mockKey = {
      key: { type: 'secret' } as CryptoKey,
      exportedKey: 'exported-key-data',
    }
    const mockEncryptedData = {
      encryptedContent: 'encrypted',
      iv: 'iv',
      salt: 'salt',
    }

    mockEncryptionService.generateKey.mockResolvedValue(mockKey)
    mockEncryptionService.encrypt.mockResolvedValue(mockEncryptedData)

    const { result } = renderHook(() => useEncryption())

    // First generate a key
    await act(async () => {
      await result.current.generateNewKey()
    })

    // Then encrypt content
    let encryptedResult: any
    await act(async () => {
      encryptedResult = await result.current.encryptContent('test content')
    })

    expect(encryptedResult).toBe(mockEncryptedData)
    expect(mockEncryptionService.encrypt).toHaveBeenCalledWith('test content', mockKey.key)
  })

  it('should decrypt content', async () => {
    const mockKey = {
      key: { type: 'secret' } as CryptoKey,
      exportedKey: 'exported-key-data',
    }
    const mockEncryptedData = {
      encryptedContent: 'encrypted',
      iv: 'iv',
      salt: 'salt',
    }
    const decryptedContent = 'decrypted content'

    mockEncryptionService.generateKey.mockResolvedValue(mockKey)
    mockEncryptionService.decrypt.mockResolvedValue(decryptedContent)

    const { result } = renderHook(() => useEncryption())

    // First generate a key
    await act(async () => {
      await result.current.generateNewKey()
    })

    // Then decrypt content
    let decryptedResult: any
    await act(async () => {
      decryptedResult = await result.current.decryptContent(mockEncryptedData)
    })

    expect(decryptedResult).toBe(decryptedContent)
    expect(mockEncryptionService.decrypt).toHaveBeenCalledWith(mockEncryptedData, mockKey.key)
  })

  it('should generate shareable URL with encryption key', async () => {
    const mockKey = {
      key: { type: 'secret' } as CryptoKey,
      exportedKey: 'exported-key-data',
    }

    mockEncryptionService.generateKey.mockResolvedValue(mockKey)

    const { result } = renderHook(() => useEncryption())

    // Generate a key first
    await act(async () => {
      await result.current.generateNewKey()
    })

    const baseUrl = 'http://localhost:3000/paste/123'
    const shareableUrl = result.current.getShareableUrl(baseUrl)

    expect(shareableUrl).toBe(`${baseUrl}#key=${encodeURIComponent(mockKey.exportedKey)}`)
  })

  it('should clear error', () => {
    const { result } = renderHook(() => useEncryption())

    // Set an error first
    act(() => {
      result.current.toggleEncryption() // This should set an error if not supported
    })

    act(() => {
      result.current.clearError()
    })

    expect(result.current.error).toBeNull()
  })

  it('should return null when encrypting without key', async () => {
    const { result } = renderHook(() => useEncryption())

    let encryptedResult: any
    await act(async () => {
      encryptedResult = await result.current.encryptContent('test content')
    })

    expect(encryptedResult).toBeNull()
  })

  it('should return null when decrypting without key', async () => {
    const mockEncryptedData = {
      encryptedContent: 'encrypted',
      iv: 'iv',
      salt: 'salt',
    }

    const { result } = renderHook(() => useEncryption())

    let decryptedResult: any
    await act(async () => {
      decryptedResult = await result.current.decryptContent(mockEncryptedData)
    })

    expect(decryptedResult).toBeNull()
    expect(result.current.error).toBe('No encryption key available for decryption')
  })
})