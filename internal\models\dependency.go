package models

import "time"

// Dependency represents a detected dependency in code
type Dependency struct {
	Name        string                   `json:"name"`
	Version     string                   `json:"version,omitempty"`
	Type        string                   `json:"type"` // "import", "require", "go_module", etc.
	Language    string                   `json:"language"`
	Registry    string                   `json:"registry,omitempty"` // "npm", "pypi", "go", etc.
	RegistryURL string                   `json:"registry_url,omitempty"`
	InstallCmd  string                   `json:"install_cmd,omitempty"`
	Security    *DependencySecurityInfo  `json:"security,omitempty"`
}

// DependencyInfo contains all dependencies detected in a paste
type DependencyInfo struct {
	Dependencies    []Dependency `json:"dependencies"`
	Language        string       `json:"language"`
	TotalCount      int          `json:"total_count"`
	VulnCount       int          `json:"vulnerability_count"`
	CriticalCount   int          `json:"critical_count"`
	HighCount       int          `json:"high_count"`
	ModerateCount   int          `json:"moderate_count"`
	LowCount        int          `json:"low_count"`
	SecurityScore   int          `json:"security_score"` // Overall security score 0-100
	LastScanned     time.Time    `json:"last_scanned"`
}