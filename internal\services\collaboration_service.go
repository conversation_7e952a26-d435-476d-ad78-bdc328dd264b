package services

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"enhanced-pastebin/internal/models"
)

// CollaborationService handles real-time collaborative editing
type CollaborationService struct {
	// In-memory storage for active document states
	// In production, this should be backed by Redis or similar
	documentStates map[string]*models.DocumentState
	operationLog   map[string][]*models.Operation
	mutex          sync.RWMutex
	
	// Color palette for collaborators
	colors []string
}

// NewCollaborationService creates a new collaboration service
func NewCollaborationService() *CollaborationService {
	return &CollaborationService{
		documentStates: make(map[string]*models.DocumentState),
		operationLog:   make(map[string][]*models.Operation),
		colors: []string{
			"#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
			"#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9",
		},
	}
}

// JoinDocument adds a user to a collaborative document session
func (cs *CollaborationService) JoinDocument(ctx context.Context, pasteID, userID, username string) (*models.DocumentState, error) {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	// Get or create document state
	docState, exists := cs.documentStates[pasteID]
	if !exists {
		// Initialize new document state
		docState = &models.DocumentState{
			PasteID:       pasteID,
			Content:       "", // Will be loaded from database
			Version:       0,
			Collaborators: []models.CollaboratorState{},
			LastModified:  time.Now(),
		}
		cs.documentStates[pasteID] = docState
		cs.operationLog[pasteID] = []*models.Operation{}
	}

	// Check if user is already in the document
	userExists := false
	for i, collaborator := range docState.Collaborators {
		if collaborator.UserID == userID {
			// Update existing collaborator
			docState.Collaborators[i].LastSeen = time.Now()
			userExists = true
			break
		}
	}

	// Add new collaborator if not exists
	if !userExists {
		color := cs.assignColor(docState.Collaborators)
		collaborator := models.CollaboratorState{
			UserID:   userID,
			Username: username,
			Color:    color,
			Cursor: models.CursorPosition{
				Line:   0,
				Column: 0,
				Offset: 0,
			},
			LastSeen: time.Now(),
		}
		docState.Collaborators = append(docState.Collaborators, collaborator)
	}

	return docState, nil
}

// LeaveDocument removes a user from a collaborative document session
func (cs *CollaborationService) LeaveDocument(ctx context.Context, pasteID, userID string) error {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	docState, exists := cs.documentStates[pasteID]
	if !exists {
		return nil // Document doesn't exist, nothing to do
	}

	// Remove collaborator
	for i, collaborator := range docState.Collaborators {
		if collaborator.UserID == userID {
			docState.Collaborators = append(docState.Collaborators[:i], docState.Collaborators[i+1:]...)
			break
		}
	}

	// Clean up empty document states
	if len(docState.Collaborators) == 0 {
		delete(cs.documentStates, pasteID)
		delete(cs.operationLog, pasteID)
	}

	return nil
}

// ApplyOperation applies a text operation to a document with operational transformation
func (cs *CollaborationService) ApplyOperation(ctx context.Context, operation *models.Operation) (*models.OperationResult, error) {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	docState, exists := cs.documentStates[operation.PasteID]
	if !exists {
		return &models.OperationResult{
			Success: false,
			Error:   "Document not found",
		}, fmt.Errorf("document %s not found", operation.PasteID)
	}

	// Get operations that happened after this operation's version
	conflictingOps := cs.getOperationsAfterVersion(operation.PasteID, operation.Version)

	// Transform the operation against conflicting operations
	transformedOp := operation
	for _, conflictOp := range conflictingOps {
		var err error
		transformedOp, err = cs.transformOperation(transformedOp, conflictOp)
		if err != nil {
			return &models.OperationResult{
				Success: false,
				Error:   fmt.Sprintf("Failed to transform operation: %v", err),
			}, err
		}
	}

	// Apply the transformed operation to the document
	newContent, err := cs.applyOperationToContent(docState.Content, transformedOp)
	if err != nil {
		return &models.OperationResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to apply operation: %v", err),
		}, err
	}

	// Update document state
	docState.Content = newContent
	docState.Version++
	docState.LastModified = time.Now()
	transformedOp.Version = docState.Version

	// Add to operation log
	cs.operationLog[operation.PasteID] = append(cs.operationLog[operation.PasteID], transformedOp)

	return &models.OperationResult{
		Success:       true,
		NewContent:    newContent,
		NewVersion:    docState.Version,
		TransformedOp: transformedOp,
	}, nil
}

// UpdateCursor updates a user's cursor position
func (cs *CollaborationService) UpdateCursor(ctx context.Context, pasteID, userID string, cursor models.CursorPosition) error {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	docState, exists := cs.documentStates[pasteID]
	if !exists {
		return fmt.Errorf("document %s not found", pasteID)
	}

	// Update collaborator's cursor
	for i, collaborator := range docState.Collaborators {
		if collaborator.UserID == userID {
			docState.Collaborators[i].Cursor = cursor
			docState.Collaborators[i].LastSeen = time.Now()
			break
		}
	}

	return nil
}

// UpdateSelection updates a user's text selection
func (cs *CollaborationService) UpdateSelection(ctx context.Context, pasteID, userID string, selection *models.Selection) error {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	docState, exists := cs.documentStates[pasteID]
	if !exists {
		return fmt.Errorf("document %s not found", pasteID)
	}

	// Update collaborator's selection
	for i, collaborator := range docState.Collaborators {
		if collaborator.UserID == userID {
			docState.Collaborators[i].Selection = selection
			docState.Collaborators[i].LastSeen = time.Now()
			break
		}
	}

	return nil
}

// GetDocumentState returns the current state of a document
func (cs *CollaborationService) GetDocumentState(ctx context.Context, pasteID string) (*models.DocumentState, error) {
	cs.mutex.RLock()
	defer cs.mutex.RUnlock()

	docState, exists := cs.documentStates[pasteID]
	if !exists {
		return nil, fmt.Errorf("document %s not found", pasteID)
	}

	// Return a copy to avoid race conditions
	stateCopy := *docState
	stateCopy.Collaborators = make([]models.CollaboratorState, len(docState.Collaborators))
	copy(stateCopy.Collaborators, docState.Collaborators)

	return &stateCopy, nil
}

// Private helper methods

// assignColor assigns a unique color to a new collaborator
func (cs *CollaborationService) assignColor(collaborators []models.CollaboratorState) string {
	usedColors := make(map[string]bool)
	for _, collaborator := range collaborators {
		usedColors[collaborator.Color] = true
	}

	for _, color := range cs.colors {
		if !usedColors[color] {
			return color
		}
	}

	// If all colors are used, return a random one
	return cs.colors[len(collaborators)%len(cs.colors)]
}

// getOperationsAfterVersion returns operations that happened after a specific version
func (cs *CollaborationService) getOperationsAfterVersion(pasteID string, version int) []*models.Operation {
	operations := cs.operationLog[pasteID]
	var result []*models.Operation

	for _, op := range operations {
		if op.Version > version {
			result = append(result, op)
		}
	}

	// Sort by version to ensure correct order
	sort.Slice(result, func(i, j int) bool {
		return result[i].Version < result[j].Version
	})

	return result
}

// transformOperation transforms an operation against another operation using operational transformation
func (cs *CollaborationService) transformOperation(op1, op2 *models.Operation) (*models.Operation, error) {
	// Simple operational transformation implementation
	// This is a basic version - production systems would need more sophisticated OT algorithms
	
	transformedOp := &models.Operation{
		ID:         op1.ID,
		PasteID:    op1.PasteID,
		UserID:     op1.UserID,
		Username:   op1.Username,
		Operations: make([]models.TextOperation, len(op1.Operations)),
		Timestamp:  op1.Timestamp,
		Version:    op1.Version,
	}
	copy(transformedOp.Operations, op1.Operations)

	// Transform each text operation in op1 against all operations in op2
	for i, textOp1 := range transformedOp.Operations {
		for _, textOp2 := range op2.Operations {
			transformedTextOp, err := cs.transformTextOperation(textOp1, textOp2)
			if err != nil {
				return nil, err
			}
			transformedOp.Operations[i] = transformedTextOp
		}
	}

	return transformedOp, nil
}

// transformTextOperation transforms a single text operation against another
func (cs *CollaborationService) transformTextOperation(op1, op2 models.TextOperation) (models.TextOperation, error) {
	// Basic operational transformation rules
	switch {
	case op1.Type == models.OperationTypeInsert && op2.Type == models.OperationTypeInsert:
		// Both are inserts
		if op2.Position <= op1.Position {
			// op2 happened before op1, shift op1's position
			return models.TextOperation{
				Type:     op1.Type,
				Position: op1.Position + len(op2.Content),
				Content:  op1.Content,
			}, nil
		}
		// op1 position is before op2, no change needed
		return op1, nil

	case op1.Type == models.OperationTypeInsert && op2.Type == models.OperationTypeDelete:
		// op1 is insert, op2 is delete
		if op2.Position <= op1.Position {
			// Delete happened before insert position, shift insert position back
			return models.TextOperation{
				Type:     op1.Type,
				Position: op1.Position - op2.Length,
				Content:  op1.Content,
			}, nil
		}
		// Delete happened after insert position, no change needed
		return op1, nil

	case op1.Type == models.OperationTypeDelete && op2.Type == models.OperationTypeInsert:
		// op1 is delete, op2 is insert
		if op2.Position <= op1.Position {
			// Insert happened before delete position, shift delete position forward
			return models.TextOperation{
				Type:     op1.Type,
				Position: op1.Position + len(op2.Content),
				Length:   op1.Length,
			}, nil
		}
		// Insert happened after delete position, no change needed
		return op1, nil

	case op1.Type == models.OperationTypeDelete && op2.Type == models.OperationTypeDelete:
		// Both are deletes
		if op2.Position <= op1.Position {
			// op2 delete happened before op1, shift op1's position back
			return models.TextOperation{
				Type:     op1.Type,
				Position: op1.Position - op2.Length,
				Length:   op1.Length,
			}, nil
		}
		// op1 position is before op2, no change needed
		return op1, nil

	default:
		// Retain operations or unknown types
		return op1, nil
	}
}

// applyOperationToContent applies a text operation to content string
func (cs *CollaborationService) applyOperationToContent(content string, operation *models.Operation) (string, error) {
	result := content
	
	// Apply operations in reverse order to maintain position integrity
	for i := len(operation.Operations) - 1; i >= 0; i-- {
		textOp := operation.Operations[i]
		var err error
		result, err = cs.applyTextOperation(result, textOp)
		if err != nil {
			return "", err
		}
	}

	return result, nil
}

// applyTextOperation applies a single text operation to content
func (cs *CollaborationService) applyTextOperation(content string, op models.TextOperation) (string, error) {
	contentRunes := []rune(content)
	
	switch op.Type {
	case models.OperationTypeInsert:
		if op.Position < 0 || op.Position > len(contentRunes) {
			return "", fmt.Errorf("insert position %d out of bounds", op.Position)
		}
		
		insertRunes := []rune(op.Content)
		result := make([]rune, 0, len(contentRunes)+len(insertRunes))
		result = append(result, contentRunes[:op.Position]...)
		result = append(result, insertRunes...)
		result = append(result, contentRunes[op.Position:]...)
		return string(result), nil

	case models.OperationTypeDelete:
		if op.Position < 0 || op.Position >= len(contentRunes) {
			return "", fmt.Errorf("delete position %d out of bounds", op.Position)
		}
		if op.Position+op.Length > len(contentRunes) {
			return "", fmt.Errorf("delete range exceeds content length")
		}
		
		result := make([]rune, 0, len(contentRunes)-op.Length)
		result = append(result, contentRunes[:op.Position]...)
		result = append(result, contentRunes[op.Position+op.Length:]...)
		return string(result), nil

	case models.OperationTypeRetain:
		// Retain operations don't change content
		return content, nil

	default:
		return "", fmt.Errorf("unknown operation type: %s", op.Type)
	}
}