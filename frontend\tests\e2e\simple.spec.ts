import { test, expect } from '@playwright/test';

test.describe('Simple Test', () => {
  test('should be able to navigate to a website', async ({ page }) => {
    await page.goto('https://example.com');
    await expect(page).toHaveTitle(/Example Domain/);
  });

  test('should be able to check if localhost is accessible', async ({ page }) => {
    try {
      // Try to access localhost:3000 (frontend)
      await page.goto('http://localhost:3000', { timeout: 5000 });
      console.log('Frontend is accessible');
    } catch (error) {
      console.log('Frontend not accessible:', error.message);
    }

    try {
      // Try to access localhost:8080 (backend health check)
      const response = await page.request.get('http://localhost:8080/api/v1/health');
      if (response.ok()) {
        console.log('Backend is accessible');
      } else {
        console.log('Backend returned status:', response.status());
      }
    } catch (error) {
      console.log('Backend not accessible:', error.message);
    }
  });
});
