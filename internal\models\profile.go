package models

import "time"

// UserStats represents user statistics
type UserStats struct {
	TotalPastes      int            `json:"total_pastes"`
	TotalViews       int            `json:"total_views"`
	MostUsedLanguage string         `json:"most_used_language"`
	LanguageCount    map[string]int `json:"language_count"`
}

// PublicUserProfile represents a user's public profile
type PublicUserProfile struct {
	Username        string    `json:"username"`
	ReputationScore int       `json:"reputation_score"`
	CreatedAt       time.Time `json:"created_at"`
	LastActive      time.Time `json:"last_active"`
	Stats           UserStats `json:"stats"`
	RecentPastes    []*Paste  `json:"recent_pastes"`
}

// UserProfileResponse represents the response for user profile requests
type UserProfileResponse struct {
	User         User      `json:"user"`
	Stats        UserStats `json:"stats"`
	RecentPastes []*Paste  `json:"recent_pastes"`
}
