import { useEffect, useRef, useState, useCallback } from 'react';
import { WebSocketClient } from '../services/websocketClient';
import { WebSocketMessage, PresenceData, ActiveUser } from '../types/websocket';
import { useAuth } from '../contexts/AuthContext';

interface UseWebSocketOptions {
  pasteId?: string;
  autoConnect?: boolean;
}

interface UseWebSocketReturn {
  isConnected: boolean;
  connectionState: string;
  activeUsers: ActiveUser[];
  userCount: number;
  connect: () => Promise<void>;
  disconnect: () => void;
  joinRoom: (pasteId: string) => void;
  leaveRoom: (pasteId: string) => void;
  broadcast: (data: any) => void;
  reconnectAttempts: number;
  websocketClient: WebSocketClient | null;
}

export const useWebSocket = (options: UseWebSocketOptions = {}): UseWebSocketReturn => {
  const { pasteId, autoConnect = true } = options;
  const { token } = useAuth();
  
  const wsClientRef = useRef<WebSocketClient | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState('CLOSED');
  const [activeUsers, setActiveUsers] = useState<ActiveUser[]>([]);
  const [userCount, setUserCount] = useState(0);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  // Initialize WebSocket client
  useEffect(() => {
    const wsUrl = `ws://localhost:8080/api/v1/ws/connect`;
    
    wsClientRef.current = new WebSocketClient({
      url: wsUrl,
      token: token || undefined,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5
    });

    // Set up event handlers
    wsClientRef.current.setHandlers({
      onConnect: () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setConnectionState('OPEN');
        setReconnectAttempts(0);
      },
      
      onDisconnect: () => {
        console.log('WebSocket disconnected');
        setIsConnected(false);
        setConnectionState('CLOSED');
        setActiveUsers([]);
        setUserCount(0);
      },
      
      onPresenceUpdate: (presence: PresenceData) => {
        console.log('Presence update:', presence);
        setActiveUsers(presence.active_users);
        setUserCount(presence.user_count);
      },
      
      onUserJoin: (user: ActiveUser) => {
        console.log('User joined:', user);
        setActiveUsers(prev => {
          // Check if user already exists
          const exists = prev.some(u => u.user_id === user.user_id);
          if (exists) return prev;
          return [...prev, user];
        });
        setUserCount(prev => prev + 1);
      },
      
      onUserLeave: (userId: string) => {
        console.log('User left:', userId);
        setActiveUsers(prev => prev.filter(u => u.user_id !== userId));
        setUserCount(prev => Math.max(0, prev - 1));
      },
      
      onMessage: (message: WebSocketMessage) => {
        console.log('Received broadcast message:', message);
        // Handle broadcast messages here
      },
      
      onError: (error: Event) => {
        console.error('WebSocket error:', error);
        setConnectionState('ERROR');
      },
      
      onReconnect: (attempt: number) => {
        console.log(`Reconnecting... attempt ${attempt}`);
        setReconnectAttempts(attempt);
        setConnectionState('CONNECTING');
      }
    });

    return () => {
      if (wsClientRef.current) {
        wsClientRef.current.disconnect();
      }
    };
  }, [token]);

  // Auto-connect if enabled
  useEffect(() => {
    if (autoConnect && wsClientRef.current && !isConnected) {
      wsClientRef.current.connect().catch(console.error);
    }
  }, [autoConnect, isConnected]);

  // Auto-join room if pasteId is provided
  useEffect(() => {
    if (pasteId && isConnected && wsClientRef.current) {
      wsClientRef.current.joinRoom(pasteId);
      
      return () => {
        if (wsClientRef.current) {
          wsClientRef.current.leaveRoom(pasteId);
        }
      };
    }
  }, [pasteId, isConnected]);

  // Update connection state periodically
  useEffect(() => {
    const interval = setInterval(() => {
      if (wsClientRef.current) {
        const state = wsClientRef.current.getConnectionState();
        setConnectionState(state);
        setIsConnected(wsClientRef.current.isConnected());
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Memoized functions
  const connect = useCallback(async () => {
    if (wsClientRef.current) {
      await wsClientRef.current.connect();
    }
  }, []);

  const disconnect = useCallback(() => {
    if (wsClientRef.current) {
      wsClientRef.current.disconnect();
    }
  }, []);

  const joinRoom = useCallback((roomId: string) => {
    if (wsClientRef.current) {
      wsClientRef.current.joinRoom(roomId);
    }
  }, []);

  const leaveRoom = useCallback((roomId: string) => {
    if (wsClientRef.current) {
      wsClientRef.current.leaveRoom(roomId);
    }
  }, []);

  const broadcast = useCallback((data: any) => {
    if (wsClientRef.current) {
      wsClientRef.current.broadcast(data);
    }
  }, []);

  return {
    isConnected,
    connectionState,
    activeUsers,
    userCount,
    connect,
    disconnect,
    joinRoom,
    leaveRoom,
    broadcast,
    reconnectAttempts,
    websocketClient: wsClientRef.current
  };
};