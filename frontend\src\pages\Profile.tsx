import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { SkeletonCard, SkeletonPasteItem } from '@/components/ui/skeleton'
import { User, Calendar, Award, FileText, Eye, Code, Plus } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { userAPI, UserProfile, Paste } from '@/lib/api'

export default function Profile() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [recentPastes, setRecentPastes] = useState<Paste[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (user) {
      loadProfile()
    }
  }, [user])

  const loadProfile = async () => {
    try {
      setIsLoading(true)
      const response = await userAPI.getProfile()
      setProfile(response.data)
      setRecentPastes(response.data.recent_pastes || [])
    } catch (error) {
      console.error('Error loading profile:', error)
      toast({
        title: "Error",
        description: "Failed to load profile information",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!user) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardContent className="p-8">
            <div className="text-center">Please log in to view your profile</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <SkeletonCard />
        <SkeletonCard />
        <div className="space-y-3">
          <SkeletonPasteItem />
          <SkeletonPasteItem />
          <SkeletonPasteItem />
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* User Info */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Profile
              </CardTitle>
              <CardDescription>Your account information and activity</CardDescription>
            </div>
            <Button asChild>
              <Link to="/create">
                <Plus className="h-4 w-4 mr-2" />
                Create Paste
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Username</label>
              <div className="text-lg">{user.username}</div>
            </div>
            <div>
              <label className="text-sm font-medium">Email</label>
              <div className="text-lg">{user.email}</div>
            </div>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Joined {new Date(user.created_at).toLocaleDateString()}
              </div>
              <div className="flex items-center">
                <Award className="h-4 w-4 mr-1" />
                {user.reputation_score} reputation
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Statistics</CardTitle>
          <CardDescription>Your activity overview</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold">{profile?.stats.total_pastes || 0}</div>
              <div className="text-sm text-muted-foreground">Pastes Created</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold">{profile?.stats.total_views || 0}</div>
              <div className="text-sm text-muted-foreground">Total Views</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-sm font-medium">{profile?.stats.most_used_language || 'N/A'}</div>
              <div className="text-sm text-muted-foreground">Most Used Language</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold">
                {profile?.stats.language_count ? Object.keys(profile.stats.language_count).length : 0}
              </div>
              <div className="text-sm text-muted-foreground">Languages Used</div>
            </div>
          </div>

          {profile?.stats.language_count && Object.keys(profile.stats.language_count).length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">Language Breakdown</h4>
              <div className="flex flex-wrap gap-2">
                {Object.entries(profile.stats.language_count).map(([lang, count]) => (
                  <Badge key={lang} variant="outline">
                    {lang}: {count}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Pastes */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Pastes</CardTitle>
              <CardDescription>Your latest code snippets</CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link to="/profile/pastes">View All</Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {recentPastes.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              No pastes yet. <Link to="/create" className="text-primary hover:underline">Create your first paste</Link>
            </div>
          ) : (
            <div className="space-y-3">
              {recentPastes.map((paste) => (
                <div key={paste.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <Link
                          to={`/paste/${paste.id}`}
                          className="font-medium hover:underline"
                        >
                          {paste.title}
                        </Link>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground mt-1">
                          <Code className="h-3 w-3" />
                          <span>{paste.language}</span>
                          <Eye className="h-3 w-3 ml-2" />
                          <span>{paste.view_count} views</span>
                          <Calendar className="h-3 w-3 ml-2" />
                          <span>{new Date(paste.created_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {paste.is_encrypted && (
                        <Badge variant="secondary">Encrypted</Badge>
                      )}
                      {paste.custom_url && (
                        <Badge variant="outline">Custom URL</Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}